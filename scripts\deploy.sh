#!/bin/bash

# TradingAgents Frontend 部署脚本
# 用于自动化部署到不同环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
TradingAgents Frontend 部署脚本

用法: $0 [选项] <环境>

环境:
    dev         开发环境
    staging     预发布环境
    production  生产环境

选项:
    -h, --help              显示此帮助信息
    -f, --force             强制部署（跳过确认）
    -b, --backup            部署前创建备份
    -r, --rollback          回滚到上一个版本
    -c, --check             仅检查环境状态
    -v, --verbose           详细输出
    --no-build              跳过构建步骤
    --no-migrate            跳过数据库迁移
    --no-restart            跳过服务重启

示例:
    $0 staging                  # 部署到预发布环境
    $0 production --backup      # 部署到生产环境并创建备份
    $0 staging --rollback       # 回滚预发布环境
    $0 production --check       # 检查生产环境状态

EOF
}

# 默认参数
ENVIRONMENT=""
FORCE=false
BACKUP=false
ROLLBACK=false
CHECK_ONLY=false
VERBOSE=false
NO_BUILD=false
NO_MIGRATE=false
NO_RESTART=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -b|--backup)
            BACKUP=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK=true
            shift
            ;;
        -c|--check)
            CHECK_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --no-migrate)
            NO_MIGRATE=true
            shift
            ;;
        --no-restart)
            NO_RESTART=true
            shift
            ;;
        dev|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定部署环境 (dev|staging|production)"
    show_help
    exit 1
fi

# 设置环境特定的配置
case $ENVIRONMENT in
    dev)
        COMPOSE_FILE="docker/docker-compose.dev.yml"
        ENV_FILE=".env.local"
        SERVICE_URL="http://localhost:3000"
        ;;
    staging)
        COMPOSE_FILE="docker/docker-compose.yml"
        ENV_FILE=".env.staging"
        SERVICE_URL="https://staging.tradingagents.example.com"
        ;;
    production)
        COMPOSE_FILE="docker/docker-compose.prod.yml"
        ENV_FILE=".env.production"
        SERVICE_URL="https://tradingagents.example.com"
        ;;
    *)
        log_error "不支持的环境: $ENVIRONMENT"
        exit 1
        ;;
esac

# 检查必需的文件
check_requirements() {
    log_info "检查部署要求..."
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warning "环境文件不存在: $ENV_FILE"
        log_info "请从 .env.example 复制并配置环境文件"
    fi
    
    # 检查 Docker 是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请启动 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose 是否可用
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "docker-compose 未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    # 检查容器状态
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_success "服务正在运行"
        docker-compose -f "$COMPOSE_FILE" ps
    else
        log_warning "服务未运行"
    fi
    
    # 检查服务健康状态
    if curl -f -s "$SERVICE_URL/api/health" > /dev/null 2>&1; then
        log_success "服务健康检查通过"
    else
        log_warning "服务健康检查失败"
    fi
}

# 创建备份
create_backup() {
    if [[ "$BACKUP" == true ]]; then
        log_info "创建备份..."
        
        BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # 备份数据库
        log_info "备份数据库..."
        docker-compose -f "$COMPOSE_FILE" exec -T mysql mysqldump \
            -u root -p"$MYSQL_ROOT_PASSWORD" \
            "$MYSQL_DATABASE" > "$BACKUP_DIR/database.sql"
        
        # 备份配置文件
        log_info "备份配置文件..."
        cp "$ENV_FILE" "$BACKUP_DIR/"
        cp "$COMPOSE_FILE" "$BACKUP_DIR/"
        
        log_success "备份完成: $BACKUP_DIR"
    fi
}

# 构建应用
build_application() {
    if [[ "$NO_BUILD" == false ]]; then
        log_info "构建应用程序..."
        
        # 拉取最新代码（如果在服务器上）
        if [[ -d ".git" ]]; then
            log_info "拉取最新代码..."
            git pull origin main
        fi
        
        # 构建 Docker 镜像
        log_info "构建 Docker 镜像..."
        docker-compose -f "$COMPOSE_FILE" build --no-cache
        
        log_success "应用构建完成"
    fi
}

# 数据库迁移
migrate_database() {
    if [[ "$NO_MIGRATE" == false ]]; then
        log_info "执行数据库迁移..."
        
        # 检查是否有迁移文件
        if [[ -d "database" ]]; then
            log_info "发现数据库迁移文件..."
            # 这里可以添加具体的迁移逻辑
            # 例如：运行 SQL 脚本或迁移工具
        fi
        
        log_success "数据库迁移完成"
    fi
}

# 部署服务
deploy_service() {
    log_info "部署服务..."
    
    # 停止旧服务
    log_info "停止旧服务..."
    docker-compose -f "$COMPOSE_FILE" down
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    MAX_RETRIES=10
    RETRY_COUNT=0
    
    while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
        if curl -f -s "$SERVICE_URL/api/health" > /dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        else
            log_info "等待服务启动... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
            sleep 10
            RETRY_COUNT=$((RETRY_COUNT + 1))
        fi
    done
    
    log_error "健康检查失败"
    return 1
}

# 回滚操作
rollback_deployment() {
    log_warning "开始回滚操作..."
    
    # 查找最新的备份
    LATEST_BACKUP=$(ls -t backups/ | head -n1)
    
    if [[ -z "$LATEST_BACKUP" ]]; then
        log_error "未找到备份文件"
        exit 1
    fi
    
    log_info "使用备份: $LATEST_BACKUP"
    
    # 停止当前服务
    docker-compose -f "$COMPOSE_FILE" down
    
    # 恢复配置文件
    cp "backups/$LATEST_BACKUP/$ENV_FILE" "$ENV_FILE"
    
    # 恢复数据库
    log_info "恢复数据库..."
    docker-compose -f "$COMPOSE_FILE" up -d mysql
    sleep 20
    
    docker-compose -f "$COMPOSE_FILE" exec -T mysql mysql \
        -u root -p"$MYSQL_ROOT_PASSWORD" \
        "$MYSQL_DATABASE" < "backups/$LATEST_BACKUP/database.sql"
    
    # 重启服务
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "回滚完成"
}

# 主函数
main() {
    log_info "开始部署到 $ENVIRONMENT 环境"
    
    # 检查要求
    check_requirements
    
    # 如果只是检查状态
    if [[ "$CHECK_ONLY" == true ]]; then
        check_service_status
        exit 0
    fi
    
    # 如果是回滚操作
    if [[ "$ROLLBACK" == true ]]; then
        if [[ "$FORCE" == false ]]; then
            read -p "确认回滚 $ENVIRONMENT 环境? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "取消回滚"
                exit 0
            fi
        fi
        rollback_deployment
        exit 0
    fi
    
    # 生产环境需要确认
    if [[ "$ENVIRONMENT" == "production" && "$FORCE" == false ]]; then
        read -p "确认部署到生产环境? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消部署"
            exit 0
        fi
    fi
    
    # 执行部署步骤
    create_backup
    build_application
    migrate_database
    deploy_service
    
    # 健康检查
    if ! health_check; then
        log_error "部署失败，考虑回滚"
        exit 1
    fi
    
    log_success "部署完成！"
    log_info "服务地址: $SERVICE_URL"
}

# 运行主函数
main "$@"
