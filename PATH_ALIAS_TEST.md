# 路径别名配置测试

## 配置完成的别名

现在您可以在项目中使用以下路径别名：

### 根目录别名
```typescript
// 访问项目根目录下的任何文件
import something from '@/package.json'
import config from '@/next.config.js'
```

### 源码目录别名
```typescript
// 访问 src 目录
import utils from '@/src/utils/helper'
import types from '@/src/types/index'
```

### 组件别名
```typescript
// 访问组件目录
import Button from '@/components/ui/Button'
import Header from '@/components/layout/Header'
```

### 应用目录别名
```typescript
// 访问 app 目录
import HomePage from '@/app/page'
import CreateTask from '@/app/create-task/page'
```

### 其他常用别名
```typescript
// 工具函数
import { cn } from '@/lib/utils'

// 类型定义
import type { User } from '@/types/user'

// 自定义 hooks
import { useAuth } from '@/hooks/useAuth'

// 状态管理
import { store } from '@/store/index'

// 公共资源
import logo from '@/public/logo.png'
```

## 配置文件说明

### 1. tsconfig.json
- 配置了 TypeScript 的路径映射
- 支持所有 `.ts` 和 `.tsx` 文件

### 2. jsconfig.json
- 配置了 JavaScript 的路径映射
- 支持所有 `.js` 和 `.jsx` 文件

### 3. .vscode/settings.json
- 配置了 VS Code 的智能提示
- 支持路径自动补全
- 配置了 Tailwind CSS 支持

## 使用示例

### 在组件中使用
```typescript
// src/components/example/TestComponent.tsx
import React from 'react'
import { Button } from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import type { User } from '@/types/user'

export function TestComponent() {
  const { user } = useAuth()
  
  return (
    <div className={cn('container mx-auto')}>
      <Button>Test Button</Button>
    </div>
  )
}
```

### 在页面中使用
```typescript
// src/app/test/page.tsx
import { TestComponent } from '@/components/example/TestComponent'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'

export default function TestPage() {
  return (
    <div>
      <Header />
      <main>
        <TestComponent />
      </main>
      <Footer />
    </div>
  )
}
```

## 验证方法

1. **智能提示测试**：
   - 在任何文件中输入 `@/`
   - 应该看到路径自动补全提示

2. **导入测试**：
   - 尝试导入任何组件或工具函数
   - 应该能正确解析路径

3. **构建测试**：
   ```bash
   npm run build
   ```
   - 构建应该成功，没有路径解析错误

## 注意事项

1. **重启编辑器**：配置更改后建议重启 VS Code
2. **TypeScript 服务**：可能需要重启 TypeScript 服务
   - 在 VS Code 中按 `Ctrl+Shift+P`
   - 输入 "TypeScript: Restart TS Server"
3. **缓存清理**：如果遇到问题，可以清理 Next.js 缓存
   ```bash
   rm -rf .next
   npm run dev
   ```

## 常见问题

### Q: 路径别名不生效？
A: 检查以下几点：
- 确认 tsconfig.json 和 jsconfig.json 配置正确
- 重启 TypeScript 服务
- 重启开发服务器

### Q: VS Code 没有智能提示？
A: 检查以下几点：
- 确认安装了 TypeScript 扩展
- 检查 .vscode/settings.json 配置
- 重启 VS Code

### Q: 构建时出现路径错误？
A: 检查以下几点：
- 确认 Next.js 版本支持路径别名
- 检查 tsconfig.json 中的 paths 配置
- 确认文件确实存在于指定路径
