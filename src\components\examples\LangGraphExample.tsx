'use client';

import React, { useState } from 'react';
import { langGraphClient } from '@/lib/langgraph-client';
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';

export function LangGraphExample() {
  const [ticker, setTicker] = useState('AAPL');
  const [message, setMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamResults, setStreamResults] = useState<any[]>([]);

  const {
    messages,
    isProcessing,
    currentStep,
    analysisResults,
    tradingDecision,
    error,
    threadId,
    analyzeStock,
    sendMessage,
    streamAnalysis,
    clearConversation,
  } = useLangGraphAgent();

  // 直接使用客户端的示例
  const handleDirectAnalysis = async () => {
    try {
      const result = await langGraphClient.analyzeStock(ticker);
      console.log('直接分析结果:', result);
    } catch (error) {
      console.error('直接分析失败:', error);
    }
  };

  // 流式分析示例
  const handleStreamAnalysis = async () => {
    setIsStreaming(true);
    setStreamResults([]);
    
    try {
      for await (const chunk of langGraphClient.streamAnalysis(ticker)) {
        setStreamResults(prev => [...prev, chunk]);
      }
    } catch (error) {
      console.error('流式分析失败:', error);
    } finally {
      setIsStreaming(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">LangGraph 架构示例</h1>
      
      {/* 架构说明 */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">新架构特点</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ LangGraph 逻辑完全在后端运行</li>
          <li>✅ 前端只负责状态管理和 UI 交互</li>
          <li>✅ 支持流式响应和实时状态更新</li>
          <li>✅ 清晰的前后端分离</li>
          <li>✅ 可扩展的 API 设计</li>
        </ul>
      </div>

      {/* 股票分析区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">股票分析</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">股票代码</label>
              <input
                type="text"
                value={ticker}
                onChange={(e) => setTicker(e.target.value.toUpperCase())}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="输入股票代码，如 AAPL"
              />
            </div>
            
            <div className="space-y-2">
              <button
                onClick={() => analyzeStock(ticker)}
                disabled={isProcessing}
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                {isProcessing ? '分析中...' : '使用 Hook 分析'}
              </button>
              
              <button
                onClick={handleDirectAnalysis}
                className="w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600"
              >
                直接使用客户端分析
              </button>
              
              <button
                onClick={handleStreamAnalysis}
                disabled={isStreaming}
                className="w-full bg-purple-500 text-white py-2 px-4 rounded-md hover:bg-purple-600 disabled:opacity-50"
              >
                {isStreaming ? '流式分析中...' : '流式分析'}
              </button>
            </div>
          </div>

          {currentStep && (
            <div className="mt-4 p-2 bg-yellow-50 rounded">
              <p className="text-sm">当前步骤: {currentStep}</p>
            </div>
          )}

          {error && (
            <div className="mt-4 p-2 bg-red-50 text-red-700 rounded">
              <p className="text-sm">错误: {error}</p>
            </div>
          )}
        </div>

        {/* 聊天区域 */}
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">AI 对话</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">消息</label>
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="输入您的问题..."
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && message.trim()) {
                    sendMessage(message);
                    setMessage('');
                  }
                }}
              />
            </div>
            
            <button
              onClick={() => {
                if (message.trim()) {
                  sendMessage(message);
                  setMessage('');
                }
              }}
              disabled={isProcessing || !message.trim()}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              发送消息
            </button>

            <button
              onClick={clearConversation}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600"
            >
              清除对话
            </button>
          </div>

          {threadId && (
            <div className="mt-4 p-2 bg-gray-50 rounded">
              <p className="text-xs text-gray-600">会话 ID: {threadId}</p>
            </div>
          )}
        </div>
      </div>

      {/* 消息历史 */}
      {messages.length > 0 && (
        <div className="border rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4">对话历史</h3>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`p-2 rounded ${
                  msg.type === 'human'
                    ? 'bg-blue-50 ml-8'
                    : msg.type === 'ai'
                    ? 'bg-green-50 mr-8'
                    : 'bg-gray-50'
                }`}
              >
                <div className="text-xs text-gray-500 mb-1">
                  {msg.type} - {msg.timestamp.toLocaleTimeString()}
                </div>
                <div className="text-sm">{msg.content}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 流式分析结果 */}
      {streamResults.length > 0 && (
        <div className="border rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4">流式分析结果</h3>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {streamResults.map((result, index) => (
              <div key={index} className="p-2 bg-purple-50 rounded">
                <div className="text-xs text-gray-500 mb-1">
                  步骤 {index + 1} - 进度: {result.progress || 0}%
                </div>
                <div className="text-sm">
                  {result.currentStep || JSON.stringify(result, null, 2)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 分析结果 */}
      {analysisResults && (
        <div className="border rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4">分析结果</h3>
          <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto">
            {JSON.stringify(analysisResults, null, 2)}
          </pre>
        </div>
      )}

      {/* 交易决策 */}
      {tradingDecision && (
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">交易决策</h3>
          <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto">
            {JSON.stringify(tradingDecision, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
