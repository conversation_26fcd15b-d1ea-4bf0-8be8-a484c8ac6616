/** @type {import('next').NextConfig} */
const nextConfig = {
    // 启用standalone输出模式用于Docker部署
    output: 'standalone',
    env: {
        NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001',
        NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
        BACK_END_URL: process.env.BACK_END_URL || 'http://127.0.0.1:5000',
    },
    // 移除代理配置，因为API路由应该在前端处理
    // async rewrites() {
    //     return [{
    //         source: '/api/:path*',
    //         destination: `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000'}/api/:path*`,
    //     }, ];
    // },
    images: {
        domains: ['localhost'],
    },
    webpack: (config, { isServer }) => {
        // 处理 Node.js 内置模块
        if (!isServer) {
            config.resolve.fallback = {
                ...config.resolve.fallback,
                "async_hooks": false,
                "fs": false,
                "path": false,
                "os": false,
                "crypto": false,
                "stream": false,
                "util": false,
                "buffer": false,
                "events": false,
                "string_decoder": false,
            };
        }

        // 忽略 LangGraph 在客户端的导入
        config.externals = config.externals || [];
        if (!isServer) {
            config.externals.push({
                '@langchain/langgraph': 'commonjs @langchain/langgraph',
            });
        }

        return config;
    },
};

module.exports = nextConfig;