# TradingAgents Frontend 环境变量配置模板
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# 应用程序配置
# ===========================================

# 环境标识
NODE_ENV=development
ENVIRONMENT=development

# 调试模式
DEBUG=true
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# API 服务配置
# ===========================================

# 后端 API 基础 URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
BACK_END_URL=http://localhost:5000

# WebSocket 连接 URL
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# ===========================================
# 数据库配置
# ===========================================

# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_analysis_dev
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123
MYSQL_HOST=localhost
MYSQL_PORT=13306

# 数据库连接 URL（备用格式）
DATABASE_URL=mysql://trading_user:trading123@localhost:13306/trading_analysis_dev

# ===========================================
# 外部 API 密钥
# ===========================================

# OpenAI API 密钥（必需）
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Finnhub API 密钥（金融数据，必需）
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# ===========================================
# 安全配置
# ===========================================

# JWT 密钥（用于用户认证）
JWT_SECRET=your_jwt_secret_here_change_in_production
REFRESH_SECRET=your_refresh_secret_here_change_in_production

# 会话密钥
SESSION_SECRET=your_session_secret_here

# 用户认证配置
NEXT_PUBLIC_ENABLE_REGISTRATION=true
NEXT_PUBLIC_REQUIRE_EMAIL_VERIFICATION=false

# ===========================================
# 监控和日志配置
# ===========================================

# 日志级别
LOG_LEVEL=debug

# Sentry 错误监控（可选）
SENTRY_DSN=your_sentry_dsn_here

# Google Analytics（可选）
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# ===========================================
# 特性开关
# ===========================================

# 启用实验性功能
ENABLE_EXPERIMENTAL_FEATURES=false

# 启用调试模式
ENABLE_DEBUG_MODE=true

# 启用性能监控
ENABLE_PERFORMANCE_MONITORING=false

# ===========================================
# 部署配置
# ===========================================

# 应用程序 URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# CDN 配置（生产环境）
NEXT_PUBLIC_CDN_URL=https://cdn.example.com

# ===========================================
# 配置说明
# ===========================================

# 1. 将此文件复制为 .env.local
# 2. 填入实际的配置值
# 3. 不要将包含敏感信息的 .env.local 文件提交到版本控制
# 4. 生产环境请使用更强的密码和密钥
# 5. 定期轮换 API 密钥和密码

# ===========================================
# 环境特定配置示例
# ===========================================

# 开发环境 (.env.local)
# NODE_ENV=development
# NEXT_PUBLIC_API_BASE_URL=http://localhost:5000

# 预发布环境 (.env.staging)
# NODE_ENV=production
# NEXT_PUBLIC_API_BASE_URL=https://api-staging.example.com

# 生产环境 (.env.production)
# NODE_ENV=production
# NEXT_PUBLIC_API_BASE_URL=https://api.example.com
