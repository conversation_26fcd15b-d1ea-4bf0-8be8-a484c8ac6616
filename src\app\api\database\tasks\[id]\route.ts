// 数据库API路由 - 单个任务操作
import { NextRequest, NextResponse } from 'next/server';

// 使用外部定义的内存存储（实际项目中应该使用数据库）
let tasks: any[] = [];

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: taskId } = await params;
    const task = tasks.find((t) => t.task_id === taskId);

    if (!task) {
      return NextResponse.json({ message: '任务不存在' }, { status: 404 });
    }

    return NextResponse.json(task);
  } catch (error) {
    console.error('获取任务失败:', error);
    return NextResponse.json({ message: '获取任务失败' }, { status: 500 });
  }
}
