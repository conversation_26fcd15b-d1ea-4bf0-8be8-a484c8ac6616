'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  WifiIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting: boolean;
  error?: string | null;
  onReconnect?: () => void;
  className?: string;
}

export function ConnectionStatus({ 
  isConnected, 
  isConnecting, 
  error, 
  onReconnect,
  className 
}: ConnectionStatusProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [lastConnectedTime, setLastConnectedTime] = useState<Date | null>(null);

  useEffect(() => {
    if (isConnected) {
      setLastConnectedTime(new Date());
    }
  }, [isConnected]);

  const getStatusInfo = () => {
    if (isConnecting) {
      return {
        variant: 'warning' as const,
        icon: ArrowPathIcon,
        text: '连接中...',
        description: '正在建立连接',
      };
    }
    
    if (isConnected) {
      return {
        variant: 'success' as const,
        icon: WifiIcon,
        text: '已连接',
        description: '实时数据连接正常',
      };
    }
    
    return {
      variant: 'danger' as const,
      icon: ExclamationTriangleIcon,
      text: '连接断开',
      description: error || '无法连接到服务器',
    };
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <div className={className}>
      <div 
        className="flex items-center space-x-2 cursor-pointer"
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="relative">
          <StatusIcon className={`h-4 w-4 ${
            statusInfo.variant === 'success' ? 'text-green-600' :
            statusInfo.variant === 'warning' ? 'text-yellow-600' :
            'text-red-600'
          } ${isConnecting ? 'animate-spin' : ''}`} />
          
          {isConnected && (
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          )}
        </div>
        
        <Badge variant={statusInfo.variant} size="sm">
          {statusInfo.text}
        </Badge>
      </div>

      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 mt-2 p-4 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg z-50 min-w-64"
          >
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <StatusIcon className={`h-5 w-5 ${
                  statusInfo.variant === 'success' ? 'text-green-600' :
                  statusInfo.variant === 'warning' ? 'text-yellow-600' :
                  'text-red-600'
                }`} />
                <span className="font-medium text-slate-900 dark:text-white">
                  连接状态
                </span>
              </div>
              
              <div className="text-sm text-slate-600 dark:text-slate-400">
                {statusInfo.description}
              </div>
              
              {lastConnectedTime && (
                <div className="text-xs text-slate-500">
                  最后连接: {lastConnectedTime.toLocaleTimeString()}
                </div>
              )}
              
              {error && (
                <div className="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                  错误: {error}
                </div>
              )}
              
              {!isConnected && onReconnect && (
                <Button
                  size="sm"
                  onClick={onReconnect}
                  disabled={isConnecting}
                  className="w-full"
                >
                  {isConnecting ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      连接中...
                    </>
                  ) : (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2" />
                      重新连接
                    </>
                  )}
                </Button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// 网络状态监控组件
export function NetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始状态
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 z-50"
    >
      <div className="flex items-center justify-center space-x-2">
        <ExclamationTriangleIcon className="h-5 w-5" />
        <span>网络连接已断开，请检查网络设置</span>
      </div>
    </motion.div>
  );
}
