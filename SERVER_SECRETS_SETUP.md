# 服务器部署 GitHub Secrets 配置指南

## 🔐 需要添加的 GitHub Secrets

### 1. 服务器URL配置

在 GitHub 仓库 `Settings` > `Secrets and variables` > `Actions` 中添加以下 Repository secrets：

#### 预发布环境URL
```
STAGING_URL=https://staging.yourdomain.com
STAGING_API_URL=https://api-staging.yourdomain.com
STAGING_WS_URL=wss://ws-staging.yourdomain.com
```

#### 生产环境URL
```
PRODUCTION_URL=https://yourdomain.com
PRODUCTION_API_URL=https://api.yourdomain.com
PRODUCTION_WS_URL=wss://ws.yourdomain.com
```

### 2. 服务器连接信息

#### SSH连接配置
```
DEPLOY_SSH_KEY=你的SSH私钥内容
DEPLOY_HOST=你的服务器IP地址
DEPLOY_USER=你的服务器用户名
DEPLOY_PORT=22
DEPLOY_PATH=/path/to/your/app
```

#### 示例SSH私钥格式
```
-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
...你的私钥内容...
...
-----END RSA PRIVATE KEY-----
```

### 3. 数据库配置

#### 预发布环境数据库
```
MYSQL_ROOT_PASSWORD_STAGING=your_staging_root_password
MYSQL_PASSWORD_STAGING=your_staging_user_password
```

#### 生产环境数据库
```
MYSQL_ROOT_PASSWORD_PROD=your_production_root_password
MYSQL_PASSWORD_PROD=your_production_user_password
```

### 4. 阿里云镜像服务（已配置）
```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 🌐 URL配置示例

### 示例1：使用子域名
```bash
# 预发布环境
STAGING_URL=https://staging.mytrading.com
STAGING_API_URL=https://api-staging.mytrading.com
STAGING_WS_URL=wss://ws-staging.mytrading.com

# 生产环境
PRODUCTION_URL=https://mytrading.com
PRODUCTION_API_URL=https://api.mytrading.com
PRODUCTION_WS_URL=wss://ws.mytrading.com
```

### 示例2：使用端口区分
```bash
# 预发布环境
STAGING_URL=https://myserver.com:3001
STAGING_API_URL=https://myserver.com:5001
STAGING_WS_URL=wss://myserver.com:8001

# 生产环境
PRODUCTION_URL=https://myserver.com
PRODUCTION_API_URL=https://myserver.com:5000
PRODUCTION_WS_URL=wss://myserver.com:8000
```

### 示例3：使用IP地址（开发测试）
```bash
# 预发布环境
STAGING_URL=http://*************:3001
STAGING_API_URL=http://*************:5001
STAGING_WS_URL=ws://*************:8001

# 生产环境
PRODUCTION_URL=http://*************:3000
PRODUCTION_API_URL=http://*************:5000
PRODUCTION_WS_URL=ws://*************:8000
```

## 🔧 SSH密钥配置步骤

### 1. 生成SSH密钥对
```bash
# 在本地生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/deploy_key

# 这会生成两个文件：
# ~/.ssh/deploy_key      (私钥 - 添加到GitHub Secrets)
# ~/.ssh/deploy_key.pub  (公钥 - 添加到服务器)
```

### 2. 将公钥添加到服务器
```bash
# 方法1：使用ssh-copy-id
ssh-copy-id -i ~/.ssh/deploy_key.pub user@your-server-ip

# 方法2：手动添加
cat ~/.ssh/deploy_key.pub | ssh user@your-server-ip "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### 3. 将私钥添加到GitHub Secrets
```bash
# 复制私钥内容
cat ~/.ssh/deploy_key

# 将输出的内容完整复制到GitHub Secrets的DEPLOY_SSH_KEY中
```

### 4. 测试SSH连接
```bash
# 测试SSH连接是否正常
ssh -i ~/.ssh/deploy_key user@your-server-ip
```

## 🚀 服务器环境准备

### 1. 安装Docker和Docker Compose
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose

# CentOS/RHEL
sudo yum install docker docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

### 2. 创建应用目录
```bash
# 创建应用部署目录
sudo mkdir -p /opt/trading-agents
sudo chown $USER:$USER /opt/trading-agents
cd /opt/trading-agents
```

### 3. 配置防火墙（如果需要）
```bash
# Ubuntu UFW
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3000  # 应用端口
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 📋 配置检查清单

### GitHub Secrets配置
- [ ] `STAGING_URL` - 预发布环境主页URL
- [ ] `STAGING_API_URL` - 预发布环境API URL
- [ ] `STAGING_WS_URL` - 预发布环境WebSocket URL
- [ ] `PRODUCTION_URL` - 生产环境主页URL
- [ ] `PRODUCTION_API_URL` - 生产环境API URL
- [ ] `PRODUCTION_WS_URL` - 生产环境WebSocket URL
- [ ] `DEPLOY_SSH_KEY` - SSH私钥
- [ ] `DEPLOY_HOST` - 服务器IP地址
- [ ] `DEPLOY_USER` - 服务器用户名
- [ ] `DEPLOY_PATH` - 部署路径
- [ ] `MYSQL_ROOT_PASSWORD_STAGING` - 预发布数据库root密码
- [ ] `MYSQL_PASSWORD_STAGING` - 预发布数据库用户密码
- [ ] `MYSQL_ROOT_PASSWORD_PROD` - 生产数据库root密码
- [ ] `MYSQL_PASSWORD_PROD` - 生产数据库用户密码
- [ ] `ALIYUN_REGISTRY_USERNAME` - 阿里云用户名
- [ ] `ALIYUN_REGISTRY_PASSWORD` - 阿里云密码

### 服务器环境
- [ ] Docker已安装并运行
- [ ] Docker Compose已安装
- [ ] SSH密钥已配置
- [ ] 防火墙端口已开放
- [ ] 域名DNS已配置（如果使用域名）
- [ ] SSL证书已配置（如果使用HTTPS）

## 🔍 测试配置

### 1. 测试SSH连接
```bash
ssh -i ~/.ssh/deploy_key user@your-server-ip "echo 'SSH连接成功'"
```

### 2. 测试Docker
```bash
ssh -i ~/.ssh/deploy_key user@your-server-ip "docker --version"
```

### 3. 测试域名解析（如果使用域名）
```bash
nslookup yourdomain.com
ping yourdomain.com
```

## 🆘 常见问题

### 1. SSH连接失败
- 检查SSH密钥格式是否正确
- 确认服务器IP地址和用户名
- 检查服务器SSH服务是否运行

### 2. 域名无法访问
- 检查DNS解析是否正确
- 确认防火墙端口是否开放
- 验证Web服务是否运行

### 3. Docker权限问题
- 确认用户已添加到docker组
- 重新登录或重启服务器
- 检查Docker服务状态

---

**💡 提示**：配置完成后，推送代码到develop或main分支将自动触发部署流程。建议先在develop分支测试预发布环境的部署。
