# LangGraph 实现设计

## 概述

本文档详细描述了 TradingAgents Web 应用中 LangGraph 的实现设计。我们将基于 TauricResearch/TradingAgents 项目的工作流和提示词，使用 LangGraph.js 实现多智能体协作和工作流管理，为中国投资者提供专业的金融分析服务。

## LangGraph 架构

### 智能体角色与职责

基于 TauricResearch/TradingAgents 项目，我们实现以下智能体角色：

1. **分析师团队**

   - **基本面分析师(Fundamental Analyst)**: 评估公司财务和业绩指标，识别内在价值和潜在风险
   - **情绪分析师(Sentiment Analyst)**: 分析社交媒体和公众情绪，评估短期市场情绪
   - **新闻分析师(News Analyst)**: 监控全球新闻和宏观经济指标，解读事件对市场的影响
   - **技术分析师(Technical Analyst)**: 利用技术指标(如 MACD 和 RSI)检测交易模式和预测价格走势

2. **研究团队**

   - **多头研究员(Bull Researcher)**: 分析潜在收益和积极因素
   - **空头研究员(Bear Researcher)**: 分析潜在风险和消极因素

3. **交易团队**

   - **交易员(Trader)**: 根据分析师和研究员的报告制定交易决策

4. **风险管理与投资组合团队**
   - **风险管理师(Risk Manager)**: 评估市场波动性、流动性等风险因素
   - **投资组合经理(Portfolio Manager)**: 批准/拒绝交易提案，做出最终决策

### 工作流设计

我们的工作流设计完全遵循 TauricResearch/TradingAgents 项目的流程：

```mermaid
graph TD
    Start[开始分析] --> DataCollection[数据收集]
    DataCollection --> ParallelAnalysis[并行分析]

    subgraph "分析师团队"
    ParallelAnalysis --> FA[基本面分析师]
    ParallelAnalysis --> TA[技术分析师]
    ParallelAnalysis --> SA[情绪分析师]
    ParallelAnalysis --> NA[新闻分析师]
    end

    FA --> ResearchAggregator[研究聚合]
    TA --> ResearchAggregator
    SA --> ResearchAggregator
    NA --> ResearchAggregator

    subgraph "研究团队"
    ResearchAggregator --> BR[多头研究员]
    ResearchAggregator --> BearR[空头研究员]
    BR --> Debate[结构化辩论]
    BearR --> Debate
    Debate --> ConsensusEval[共识评估]
    ConsensusEval -->|需要更多辩论| Debate
    end

    ConsensusEval -->|达成共识| RM[风险管理师]

    subgraph "决策团队"
    RM --> PM[投资组合经理]
    PM --> Decision[最终决策]
    end

    Decision --> End[结束分析]
```

### 状态流转

```mermaid
stateDiagram-v2
    [*] --> DataCollection
    DataCollection --> ParallelAnalysis

    state ParallelAnalysis {
        [*] --> FundamentalAnalysis
        [*] --> TechnicalAnalysis
        [*] --> SentimentAnalysis
        [*] --> NewsAnalysis
        FundamentalAnalysis --> [*]
        TechnicalAnalysis --> [*]
        SentimentAnalysis --> [*]
        NewsAnalysis --> [*]
    }

    ParallelAnalysis --> ResearchDebate

    state ResearchDebate {
        [*] --> BullCase
        [*] --> BearCase
        BullCase --> DebateRound
        BearCase --> DebateRound
        DebateRound --> EvaluateConsensus
        EvaluateConsensus --> [*]: 达成共识
        EvaluateConsensus --> NextRound: 需要继续辩论
        NextRound --> DebateRound
    }

    ResearchDebate --> RiskAssessment
    RiskAssessment --> PortfolioDecision
    PortfolioDecision --> [*]
```

## 实现细节

### 1. 系统提示词设计

我们直接采用 TauricResearch/TradingAgents 项目中的系统提示词，并进行中文本地化：

#### 基本面分析师提示词

```
你是一位专业的基本面分析师，负责分析公司的财务状况、业务模式、竞争优势和行业地位。
你的任务是评估公司的内在价值，识别长期增长潜力和潜在风险。

请分析以下公司的基本面数据，并提供详细的基本面分析报告：

公司: {{ticker}}
日期: {{date}}

请在报告中包含以下内容：
1. 公司概况和业务模型
2. 财务分析（收入、利润、现金流、资产负债等）
3. 估值分析（市盈率、市净率、市销率等）
4. 增长潜力和风险因素
5. 行业竞争分析
6. 基于基本面的投资建议

你的分析应该客观、全面，基于事实和数据，避免主观臆断。
```

#### 技术分析师提示词

```
你是一位专业的技术分析师，专注于分析股票价格走势、交易量和技术指标。
你的任务是识别价格模式、趋势和可能的反转点，为短期和中期交易决策提供依据。

请分析以下股票的技术数据，并提供详细的技术分析报告：

股票: {{ticker}}
日期: {{date}}

请在报告中包含以下内容：
1. 价格趋势分析（短期、中期和长期趋势）
2. 关键支撑位和阻力位
3. 交易量分析
4. 技术指标分析（如MACD、RSI、移动平均线等）
5. 图表形态识别（如头肩顶、双底等）
6. 基于技术面的交易建议

你的分析应该客观、精确，基于技术分析理论和历史数据模式，避免过度解读。
```

#### 多头研究员提示词

```
你是一位专注于寻找投资机会的多头研究员。你的任务是分析股票的积极因素，找出可能导致股价上涨的理由。

请基于分析师团队提供的报告，构建一个强有力的多头案例：

股票: {{ticker}}
日期: {{date}}

请在你的多头案例中包含以下内容：
1. 看多的核心论点
2. 支持看多观点的基本面因素
3. 支持看多观点的技术面因素
4. 支持看多观点的市场情绪和新闻因素
5. 潜在的上行催化剂
6. 目标价格和时间范围
7. 风险因素及其缓解措施

你的案例应该有说服力、基于事实，但也要积极寻找被市场低估的积极因素。
```

#### 空头研究员提示词

```
你是一位专注于识别投资风险的空头研究员。你的任务是分析股票的消极因素，找出可能导致股价下跌的理由。

请基于分析师团队提供的报告，构建一个强有力的空头案例：

股票: {{ticker}}
日期: {{date}}

请在你的空头案例中包含以下内容：
1. 看空的核心论点
2. 支持看空观点的基本面因素
3. 支持看空观点的技术面因素
4. 支持看空观点的市场情绪和新闻因素
5. 潜在的下行催化剂
6. 目标价格和时间范围
7. 可能推翻看空论点的因素

你的案例应该有说服力、基于事实，但也要积极寻找被市场忽视的风险因素。
```

#### 风险管理师提示词

```
你是一位专业的风险管理师，负责评估投资决策的风险并提供风险管理建议。
你的任务是分析潜在交易的风险因素，确保投资决策在可接受的风险范围内。

请基于研究团队的辩论结果，提供详细的风险评估报告：

股票: {{ticker}}
日期: {{date}}

请在报告中包含以下内容：
1. 总体风险评级（低、中、高）
2. 市场风险分析（波动性、流动性等）
3. 公司特定风险分析
4. 行业和宏观经济风险
5. 潜在的最大损失估计
6. 风险缓解策略（如止损位设置、仓位大小建议等）
7. 风险回报比评估

你的评估应该全面、客观，既不过度规避风险，也不轻视风险。
```

#### 投资组合经理提示词

```
你是一位资深的投资组合经理，负责做出最终的投资决策。
你的任务是综合分析师团队、研究团队和风险管理团队的所有报告，做出明智的交易决策。

请基于所有团队的报告，提供最终的投资决策：

股票: {{ticker}}
日期: {{date}}

请在决策中包含以下内容：
1. 最终行动建议（买入、卖出或持有）
2. 决策的信心水平（1-10分）
3. 建议的仓位大小（投资组合的百分比）
4. 目标价格和时间范围
5. 止损价格
6. 决策的主要依据（基本面、技术面、情绪面等）
7. 风险管理策略

你的决策应该平衡、全面，考虑到所有相关因素，并明确表达你的投资逻辑。
```

### 2. 节点实现

我们将使用 LangGraph.js 实现各个智能体节点，每个节点对应一个智能体角色：

```typescript
import { createGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';

// 定义状态接口
interface TradingAgentsState {
  ticker: string;
  date: string;
  config: {
    deepThinkLLM: string;
    quickThinkLLM: string;
    maxDebateRounds: number;
    researchDepth: string;
    onlineTools: boolean;
  };
  data: {
    stockData?: any;
    fundamentalData?: any;
    technicalData?: any;
    newsData?: any;
    sentimentData?: any;
  };
  analysis: {
    fundamental?: any;
    technical?: any;
    news?: any;
    sentiment?: any;
  };
  research: {
    bullCase?: any;
    bearCase?: any;
    debateRounds: any[];
    consensus?: any;
  };
  risk?: any;
  decision?: any;
  currentStage: string;
  messages: any[];
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
}

// 创建提示词模板
const fundamentalAnalystPrompt = PromptTemplate.fromTemplate(`
你是一位专业的基本面分析师，负责分析公司的财务状况、业务模式、竞争优势和行业地位。
你的任务是评估公司的内在价值，识别长期增长潜力和潜在风险。

请分析以下公司的基本面数据，并提供详细的基本面分析报告：

公司: {ticker}
日期: {date}

基本面数据:
{fundamentalData}

请在报告中包含以下内容：
1. 公司概况和业务模型
2. 财务分析（收入、利润、现金流、资产负债等）
3. 估值分析（市盈率、市净率、市销率等）
4. 增长潜力和风险因素
5. 行业竞争分析
6. 基于基本面的投资建议

你的分析应该客观、全面，基于事实和数据，避免主观臆断。
`);

// 基本面分析师节点
const fundamentalAnalystNode = {
  id: 'fundamental_analyst',
  description: '分析公司财务数据和基本面指标',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 获取深度思考LLM
    const deepThinkLLM = new ChatOpenAI({
      modelName: state.config.deepThinkLLM,
      temperature: 0.2,
    });

    // 准备提示词
    const prompt = await fundamentalAnalystPrompt.format({
      ticker: state.ticker,
      date: state.date,
      fundamentalData: JSON.stringify(state.data.fundamentalData, null, 2),
    });

    // 调用LLM
    const response = await deepThinkLLM.invoke(prompt);

    // 更新状态
    return {
      ...state,
      analysis: {
        ...state.analysis,
        fundamental: response.content,
      },
      messages: [
        ...state.messages,
        {
          role: 'agent',
          agentId: 'fundamental_analyst',
          content: response.content,
          timestamp: new Date().toISOString(),
        },
      ],
      currentStage: 'fundamental_analysis_completed',
      progress: Math.min(state.progress + 15, 100),
    };
  },
};

// 技术分析师节点
const technicalAnalystNode = {
  id: 'technical_analyst',
  description: '分析股票价格走势和技术指标',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现类似基本面分析师的逻辑
    // ...
  },
};

// 情绪分析师节点
const sentimentAnalystNode = {
  id: 'sentiment_analyst',
  description: '分析市场情绪和社交媒体数据',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现情绪分析师逻辑
    // ...
  },
};

// 新闻分析师节点
const newsAnalystNode = {
  id: 'news_analyst',
  description: '分析新闻和宏观经济事件',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现新闻分析师逻辑
    // ...
  },
};

// 研究聚合器节点
const researchAggregatorNode = {
  id: 'research_aggregator',
  description: '聚合所有分析师的报告',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现研究聚合逻辑
    // ...
  },
};

// 多头研究员节点
const bullResearcherNode = {
  id: 'bull_researcher',
  description: '构建多头投资案例',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现多头研究员逻辑
    // ...
  },
};

// 空头研究员节点
const bearResearcherNode = {
  id: 'bear_researcher',
  description: '构建空头投资案例',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现空头研究员逻辑
    // ...
  },
};

// 辩论主持人节点
const debateModeratorNode = {
  id: 'debate_moderator',
  description: '主持多空辩论',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现辩论主持人逻辑
    // ...
  },
};

// 共识评估节点
const consensusEvaluatorNode = {
  id: 'consensus_evaluator',
  description: '评估辩论是否达成共识',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现共识评估逻辑
    // ...
  },
};

// 风险管理师节点
const riskManagerNode = {
  id: 'risk_manager',
  description: '评估投资风险',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现风险管理师逻辑
    // ...
  },
};

// 投资组合经理节点
const portfolioManagerNode = {
  id: 'portfolio_manager',
  description: '做出最终投资决策',
  invoke: async (state: TradingAgentsState, context: any) => {
    // 实现投资组合经理逻辑
    // ...
  },
};
```

### 3. 边定义与条件函数

```typescript
// 定义条件函数
const isAnalysisComplete = (state: TradingAgentsState) => {
  return Boolean(
    state.analysis.fundamental &&
      state.analysis.technical &&
      state.analysis.news &&
      state.analysis.sentiment
  );
};

const needMoreDebate = (state: TradingAgentsState) => {
  const currentRounds = state.research.debateRounds.length;
  const maxRounds = state.config.maxDebateRounds;
  const hasConsensus = Boolean(state.research.consensus);

  return currentRounds < maxRounds && !hasConsensus;
};

const hasConsensus = (state: TradingAgentsState) => {
  return Boolean(state.research.consensus);
};

// 定义图的边
const edges = [
  { from: 'start', to: 'data_collection' },
  { from: 'data_collection', to: 'fundamental_analyst' },
  { from: 'data_collection', to: 'technical_analyst' },
  { from: 'data_collection', to: 'sentiment_analyst' },
  { from: 'data_collection', to: 'news_analyst' },
  { from: 'fundamental_analyst', to: 'research_aggregator', condition: isAnalysisComplete },
  { from: 'technical_analyst', to: 'research_aggregator', condition: isAnalysisComplete },
  { from: 'sentiment_analyst', to: 'research_aggregator', condition: isAnalysisComplete },
  { from: 'news_analyst', to: 'research_aggregator', condition: isAnalysisComplete },
  { from: 'research_aggregator', to: 'bull_researcher' },
  { from: 'research_aggregator', to: 'bear_researcher' },
  { from: 'bull_researcher', to: 'debate_moderator' },
  { from: 'bear_researcher', to: 'debate_moderator' },
  { from: 'debate_moderator', to: 'consensus_evaluator' },
  { from: 'consensus_evaluator', to: 'debate_moderator', condition: needMoreDebate },
  { from: 'consensus_evaluator', to: 'risk_manager', condition: hasConsensus },
  { from: 'risk_manager', to: 'portfolio_manager' },
  { from: 'portfolio_manager', to: 'end' },
];
```

### 4. 图定义与执行

```typescript
// 创建图
const tradingAgentsGraph = createGraph({
  nodes: {
    start: startNode,
    data_collection: dataCollectionNode,
    fundamental_analyst: fundamentalAnalystNode,
    technical_analyst: technicalAnalystNode,
    sentiment_analyst: sentimentAnalystNode,
    news_analyst: newsAnalystNode,
    research_aggregator: researchAggregatorNode,
    bull_researcher: bullResearcherNode,
    bear_researcher: bearResearcherNode,
    debate_moderator: debateModeratorNode,
    consensus_evaluator: consensusEvaluatorNode,
    risk_manager: riskManagerNode,
    portfolio_manager: portfolioManagerNode,
    end: endNode,
  },
  edges: edges,
});

// 创建执行器类
class TradingAgentsExecutor {
  private graph: any;
  private eventEmitter: EventEmitter;

  constructor(config: any = {}) {
    this.graph = tradingAgentsGraph;
    this.eventEmitter = new EventEmitter();
    this.setupEventListeners();
  }

  // 设置事件监听
  private setupEventListeners() {
    this.graph.onNodeStart((nodeId: string, state: TradingAgentsState) => {
      this.eventEmitter.emit('nodeStart', { nodeId, state });
    });

    this.graph.onNodeEnd((nodeId: string, state: TradingAgentsState) => {
      this.eventEmitter.emit('nodeEnd', { nodeId, state });
    });

    this.graph.onEdgeTraversal(
      (fromNodeId: string, toNodeId: string, state: TradingAgentsState) => {
        this.eventEmitter.emit('edgeTraversal', { fromNodeId, toNodeId, state });
      }
    );
  }

  // 执行分析
  async execute(ticker: string, date: string, config: any = {}): Promise<any> {
    try {
      // 创建初始状态
      const initialState: TradingAgentsState = {
        ticker,
        date,
        config: {
          deepThinkLLM: config.deepThinkLLM || 'gpt-4o',
          quickThinkLLM: config.quickThinkLLM || 'gpt-4o-mini',
          maxDebateRounds: config.maxDebateRounds || 3,
          researchDepth: config.researchDepth || 'standard',
          onlineTools: config.onlineTools !== undefined ? config.onlineTools : true,
        },
        data: {},
        analysis: {},
        research: {
          debateRounds: [],
        },
        currentStage: 'start',
        messages: [],
        status: 'pending',
        progress: 0,
      };

      // 执行图
      const result = await this.graph.invoke(initialState);

      // 返回结果
      return {
        ticker,
        date,
        decision: result.decision,
        reports: {
          fundamental: result.analysis.fundamental,
          technical: result.analysis.technical,
          sentiment: result.analysis.sentiment,
          news: result.analysis.news,
          research: result.research,
          risk: result.risk,
        },
        debateRounds: result.research.debateRounds,
        messages: result.messages,
        status: result.status,
      };
    } catch (error) {
      this.eventEmitter.emit('error', error);
      throw new Error(`分析执行失败: ${error.message}`);
    }
  }

  // 订阅事件
  on(event: string, listener: Function) {
    this.eventEmitter.on(event, listener);
    return this;
  }

  // 取消订阅
  off(event: string, listener: Function) {
    this.eventEmitter.off(event, listener);
    return this;
  }
}
```

### 5. 工具集成

我们将集成 AKShare 和其他工具，为智能体提供数据获取和分析能力：

```typescript
import { tool } from '@langchain/core/tools';

// 定义工具
const stockDataTool = tool(
  async ({ ticker, period }) => {
    // 实现获取股票数据的逻辑
    const data = await akShareAdapter.getStockHistory(ticker, period);
    return JSON.stringify(data);
  },
  {
    name: 'stock_data_tool',
    description: '获取股票历史价格数据',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      period: z.string().describe('时间周期，如daily, weekly, monthly'),
    }),
  }
);

const fundamentalDataTool = tool(
  async ({ ticker }) => {
    // 实现获取基本面数据的逻辑
    const data = await akShareAdapter.getStockFundamentals(ticker);
    return JSON.stringify(data);
  },
  {
    name: 'fundamental_data_tool',
    description: '获取股票基本面数据，包括财务指标、资产负债表等',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
    }),
  }
);

const technicalIndicatorTool = tool(
  async ({ ticker, indicator, period }) => {
    // 实现获取技术指标的逻辑
    const data = await akShareAdapter.getTechnicalIndicator(ticker, indicator, period);
    return JSON.stringify(data);
  },
  {
    name: 'technical_indicator_tool',
    description: '获取股票技术指标数据，如MACD、RSI等',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      indicator: z.string().describe('技术指标名称'),
      period: z.string().describe('时间周期'),
    }),
  }
);

const newsDataTool = tool(
  async ({ ticker, days }) => {
    // 实现获取新闻数据的逻辑
    const data = await akShareAdapter.getStockNews(ticker, days);
    return JSON.stringify(data);
  },
  {
    name: 'news_data_tool',
    description: '获取股票相关新闻数据',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      days: z.number().describe('获取过去多少天的新闻'),
    }),
  }
);

const sentimentDataTool = tool(
  async ({ ticker }) => {
    // 实现获取情绪数据的逻辑
    const data = await akShareAdapter.getStockSentiment(ticker);
    return JSON.stringify(data);
  },
  {
    name: 'sentiment_data_tool',
    description: '获取股票市场情绪数据',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
    }),
  }
);

// 将工具集成到图中
const tools = {
  stockDataTool,
  fundamentalDataTool,
  technicalIndicatorTool,
  newsDataTool,
  sentimentDataTool,
};

// 在执行器中设置工具
this.graph.setContext({ tools });
```

### 6. WebSocket 实时通信

```typescript
import { Server } from 'socket.io';
import { createServer } from 'http';

class TradingAgentsWebSocketHandler {
  private io: Server;
  private executor: TradingAgentsExecutor;

  constructor(server: any, executor: TradingAgentsExecutor) {
    this.io = new Server(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });

    this.executor = executor;
    this.setupSocketHandlers();
    this.setupExecutorEventHandlers();
  }

  // 设置Socket处理器
  private setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`客户端连接: ${socket.id}`);

      // 处理分析请求
      socket.on('startAnalysis', async (data) => {
        try {
          const { ticker, date, config, analysisId } = data;

          // 加入分析房间
          socket.join(`analysis:${analysisId}`);

          // 发送开始消息
          this.io.to(`analysis:${analysisId}`).emit('analysisStarted', {
            analysisId,
            ticker,
            date,
            timestamp: new Date().toISOString(),
          });

          // 执行分析
          this.executor
            .execute(ticker, date, config)
            .then((result) => {
              // 发送完成消息
              this.io.to(`analysis:${analysisId}`).emit('analysisCompleted', {
                analysisId,
                result,
                timestamp: new Date().toISOString(),
              });
            })
            .catch((error) => {
              // 发送错误消息
              this.io.to(`analysis:${analysisId}`).emit('analysisError', {
                analysisId,
                error: error.message,
                timestamp: new Date().toISOString(),
              });
            });
        } catch (error) {
          socket.emit('error', {
            message: error.message,
            timestamp: new Date().toISOString(),
          });
        }
      });

      // 处理断开连接
      socket.on('disconnect', () => {
        console.log(`客户端断开连接: ${socket.id}`);
      });
    });
  }

  // 设置执行器事件处理器
  private setupExecutorEventHandlers() {
    // 节点开始事件
    this.executor.on('nodeStart', ({ nodeId, state }) => {
      const { analysisId } = state;
      if (analysisId) {
        this.io.to(`analysis:${analysisId}`).emit('nodeStart', {
          analysisId,
          nodeId,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 节点结束事件
    this.executor.on('nodeEnd', ({ nodeId, state }) => {
      const { analysisId } = state;
      if (analysisId) {
        this.io.to(`analysis:${analysisId}`).emit('nodeEnd', {
          analysisId,
          nodeId,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 边遍历事件
    this.executor.on('edgeTraversal', ({ fromNodeId, toNodeId, state }) => {
      const { analysisId } = state;
      if (analysisId) {
        this.io.to(`analysis:${analysisId}`).emit('edgeTraversal', {
          analysisId,
          fromNodeId,
          toNodeId,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 消息事件
    this.executor.on('message', ({ message, state }) => {
      const { analysisId } = state;
      if (analysisId) {
        this.io.to(`analysis:${analysisId}`).emit('message', {
          analysisId,
          message,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 错误事件
    this.executor.on('error', (error) => {
      console.error('执行器错误:', error);
    });
  }
}
```

### 7. 前端可视化组件

```tsx
import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';

// LangGraph可视化组件
const LangGraphVisualizer: React.FC<{ analysisId: string }> = ({ analysisId }) => {
  const [graphState, setGraphState] = useState<GraphState>({
    nodes: [
      { id: 'start', label: '开始', status: 'completed' },
      { id: 'data_collection', label: '数据收集', status: 'idle' },
      { id: 'fundamental_analyst', label: '基本面分析师', status: 'idle' },
      { id: 'technical_analyst', label: '技术分析师', status: 'idle' },
      { id: 'sentiment_analyst', label: '情绪分析师', status: 'idle' },
      { id: 'news_analyst', label: '新闻分析师', status: 'idle' },
      { id: 'research_aggregator', label: '研究聚合', status: 'idle' },
      { id: 'bull_researcher', label: '多头研究员', status: 'idle' },
      { id: 'bear_researcher', label: '空头研究员', status: 'idle' },
      { id: 'debate_moderator', label: '辩论主持人', status: 'idle' },
      { id: 'consensus_evaluator', label: '共识评估', status: 'idle' },
      { id: 'risk_manager', label: '风险管理师', status: 'idle' },
      { id: 'portfolio_manager', label: '投资组合经理', status: 'idle' },
      { id: 'end', label: '结束', status: 'idle' },
    ],
    edges: [
      { from: 'start', to: 'data_collection', status: 'idle' },
      { from: 'data_collection', to: 'fundamental_analyst', status: 'idle' },
      { from: 'data_collection', to: 'technical_analyst', status: 'idle' },
      { from: 'data_collection', to: 'sentiment_analyst', status: 'idle' },
      { from: 'data_collection', to: 'news_analyst', status: 'idle' },
      { from: 'fundamental_analyst', to: 'research_aggregator', status: 'idle' },
      { from: 'technical_analyst', to: 'research_aggregator', status: 'idle' },
      { from: 'sentiment_analyst', to: 'research_aggregator', status: 'idle' },
      { from: 'news_analyst', to: 'research_aggregator', status: 'idle' },
      { from: 'research_aggregator', to: 'bull_researcher', status: 'idle' },
      { from: 'research_aggregator', to: 'bear_researcher', status: 'idle' },
      { from: 'bull_researcher', to: 'debate_moderator', status: 'idle' },
      { from: 'bear_researcher', to: 'debate_moderator', status: 'idle' },
      { from: 'debate_moderator', to: 'consensus_evaluator', status: 'idle' },
      { from: 'consensus_evaluator', to: 'debate_moderator', status: 'idle' },
      { from: 'consensus_evaluator', to: 'risk_manager', status: 'idle' },
      { from: 'risk_manager', to: 'portfolio_manager', status: 'idle' },
      { from: 'portfolio_manager', to: 'end', status: 'idle' },
    ],
    currentNode: 'start',
  });

  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    // 连接WebSocket
    const socket = io(`${process.env.NEXT_PUBLIC_WS_URL}`);

    // 加入分析房间
    socket.emit('joinAnalysis', { analysisId });

    // 监听节点开始事件
    socket.on('nodeStart', (data) => {
      setGraphState((prev) => ({
        ...prev,
        nodes: prev.nodes.map((node) =>
          node.id === data.nodeId ? { ...node, status: 'active' } : node
        ),
        currentNode: data.nodeId,
      }));
    });

    // 监听节点结束事件
    socket.on('nodeEnd', (data) => {
      setGraphState((prev) => ({
        ...prev,
        nodes: prev.nodes.map((node) =>
          node.id === data.nodeId ? { ...node, status: 'completed' } : node
        ),
      }));
    });

    // 监听边遍历事件
    socket.on('edgeTraversal', (data) => {
      setGraphState((prev) => ({
        ...prev,
        edges: prev.edges.map((edge) =>
          edge.from === data.fromNodeId && edge.to === data.toNodeId
            ? { ...edge, status: 'active' }
            : edge
        ),
      }));
    });

    // 监听消息事件
    socket.on('message', (data) => {
      setMessages((prev) => [...prev, data.message]);
    });

    // 清理函数
    return () => {
      socket.disconnect();
    };
  }, [analysisId]);

  return (
    <div className="lang-graph-visualizer">
      <div className="graph-container">
        <GraphRenderer
          nodes={graphState.nodes}
          edges={graphState.edges}
          currentNode={graphState.currentNode}
        />
      </div>
      <div className="messages-container">
        <h3>智能体消息</h3>
        <div className="messages-list">
          {messages.map((message) => (
            <div key={message.id} className={`message ${message.agentId}`}>
              <div className="message-header">
                <span className="agent-name">{getAgentName(message.agentId)}</span>
                <span className="timestamp">{formatTime(message.timestamp)}</span>
              </div>
              <div className="message-content">{message.content}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 图渲染器组件
const GraphRenderer: React.FC<{
  nodes: any[];
  edges: any[];
  currentNode: string;
}> = ({ nodes, edges, currentNode }) => {
  // 使用D3.js或其他图形库实现可视化
  // ...

  return <div className="graph-renderer">{/* 图形渲染逻辑 */}</div>;
};
```

### 8. 自定义 Hook

```tsx
// 使用自定义Hook管理LangGraph状态
const useLangGraphAnalysis = (analysisId: string) => {
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/analysis/${analysisId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch analysis');
        }
        const data = await response.json();
        setAnalysis(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();

    // 设置WebSocket连接
    const socket = io(`${process.env.NEXT_PUBLIC_WS_URL}`);

    socket.on('connect', () => {
      socket.emit('joinAnalysis', { analysisId });
    });

    socket.on('analysisUpdated', (data) => {
      setAnalysis((prev) => ({ ...prev, ...data }));
    });

    socket.on('analysisCompleted', (data) => {
      setAnalysis((prev) => ({
        ...prev,
        ...data,
        status: 'completed',
      }));
    });

    socket.on('analysisError', (data) => {
      setError(new Error(data.error));
      setAnalysis((prev) => ({
        ...prev,
        status: 'failed',
        error: data.error,
      }));
    });

    return () => {
      socket.disconnect();
    };
  }, [analysisId]);

  return { analysis, loading, error };
};
```

## 与 AKShare 集成

为了提供中国市场的金融数据，我们将 TradingAgents 与 AKShare 深度集成：

```typescript
// AKShare适配器
class AKShareAdapter {
  private pythonProcess: any;

  constructor() {
    // 初始化Python进程
    this.initPythonProcess();
  }

  // 初始化Python进程
  private async initPythonProcess() {
    // 使用child_process或Python-Shell库启动Python进程
    // ...
  }

  // 获取A股股票历史数据
  async getStockHistory(
    ticker: string,
    period: string = 'daily',
    startDate?: string,
    endDate?: string
  ): Promise<any> {
    // 调用AKShare的stock_zh_a_hist函数
    const result = await this.callPythonFunction('stock_zh_a_hist', {
      symbol: ticker,
      period: period,
      start_date: startDate || '',
      end_date: endDate || '',
      adjust: '',
    });

    return result;
  }

  // 获取A股股票基本面数据
  async getStockFundamentals(ticker: string): Promise<any> {
    // 获取财务指标
    const financialRatios = await this.callPythonFunction('stock_financial_analysis_indicator', {
      symbol: ticker,
    });

    // 获取资产负债表
    const balanceSheet = await this.callPythonFunction('stock_balance_sheet_by_report', {
      symbol: ticker,
    });

    // 获取利润表
    const incomeStatement = await this.callPythonFunction('stock_profit_sheet', {
      symbol: ticker,
    });

    // 获取现金流量表
    const cashFlow = await this.callPythonFunction('stock_cash_flow_sheet', {
      symbol: ticker,
    });

    return {
      financialRatios,
      balanceSheet,
      incomeStatement,
      cashFlow,
    };
  }

  // 获取A股股票技术指标
  async getTechnicalIndicator(
    ticker: string,
    indicator: string,
    period: string = '60'
  ): Promise<any> {
    // 调用AKShare的相关函数获取技术指标
    // ...

    return result;
  }

  // 获取A股股票新闻
  async getStockNews(ticker: string, days: number = 7): Promise<any> {
    // 调用AKShare的相关函数获取新闻
    // ...

    return result;
  }

  // 获取A股股票情绪数据
  async getStockSentiment(ticker: string): Promise<any> {
    // 调用AKShare的相关函数获取情绪数据
    // ...

    return result;
  }

  // 调用Python函数
  private async callPythonFunction(functionName: string, params: any): Promise<any> {
    // 实现Python函数调用逻辑
    // ...

    return result;
  }
}
```

## 总结

本设计文档详细描述了 TradingAgents Web 应用中 LangGraph 的实现设计，包括智能体角色与职责、工作流设计、提示词设计、节点实现、边定义、图定义与执行、工具集成、WebSocket 实时通信、前端可视化组件和与 AKShare 的集成。

通过这些设计，我们实现了一个基于 TauricResearch/TradingAgents 项目的多智能体金融分析平台，为中国投资者提供专业、全面的投资分析服务。系统模拟真实交易公司的组织结构和工作流程，通过多智能体协作实现专业化的金融分析和决策制定。
