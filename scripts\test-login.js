#!/usr/bin/env node

/**
 * 登录接口测试脚本
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testLogin() {
  console.log('🧪 测试登录接口...\n');

  try {
    // 测试1: 正常登录
    console.log('1. 测试正常登录...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test@123456',
      }),
    });

    const loginResult = await loginResponse.json();
    console.log('登录响应:', loginResponse.status, loginResult);

    if (loginResponse.ok) {
      console.log('✅ 登录成功');
      
      // 测试2: 获取用户信息
      console.log('\n2. 测试获取用户信息...');
      const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Cookie': loginResponse.headers.get('set-cookie'),
        },
      });
      
      const meResult = await meResponse.json();
      console.log('用户信息响应:', meResponse.status, meResult);
      
      if (meResponse.ok) {
        console.log('✅ 获取用户信息成功');
      } else {
        console.log('❌ 获取用户信息失败:', meResult);
      }
    } else {
      console.log('❌ 登录失败:', loginResult);
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testLogin();
}
