'use client';

import { clsx } from 'clsx';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info' | 'purple' | 'pink';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Badge({ 
  children, 
  variant = 'default', 
  size = 'md', 
  className 
}: BadgeProps) {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  
  const variantClasses = {
    default: 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200',
    success: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    danger: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    purple: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    pink: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400',
  };

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-sm',
    lg: 'px-3 py-1 text-base',
  };

  return (
    <span
      className={clsx(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
    >
      {children}
    </span>
  );
}

interface StatusBadgeProps {
  status: 'idle' | 'running' | 'completed' | 'error';
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const statusConfig = {
    idle: { variant: 'default' as const, text: '等待中' },
    running: { variant: 'info' as const, text: '运行中' },
    completed: { variant: 'success' as const, text: '已完成' },
    error: { variant: 'danger' as const, text: '错误' },
  };

  const config = statusConfig[status];

  return (
    <Badge variant={config.variant} className={className}>
      <div className="flex items-center space-x-1">
        <div className={clsx(
          'w-2 h-2 rounded-full',
          status === 'running' && 'animate-pulse',
          {
            'bg-slate-400': status === 'idle',
            'bg-blue-500': status === 'running',
            'bg-green-500': status === 'completed',
            'bg-red-500': status === 'error',
          }
        )} />
        <span>{config.text}</span>
      </div>
    </Badge>
  );
}

interface ProgressBadgeProps {
  progress: number;
  className?: string;
}

export function ProgressBadge({ progress, className }: ProgressBadgeProps) {
  const getVariant = (progress: number) => {
    if (progress === 100) return 'success';
    if (progress >= 75) return 'info';
    if (progress >= 50) return 'warning';
    return 'default';
  };

  return (
    <Badge variant={getVariant(progress)} className={className}>
      {progress}%
    </Badge>
  );
}
