# SSH密钥生成和配置脚本
# 用于Windows PowerShell

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerIP,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerUser,
    
    [string]$ServerPath = "/opt/trading-agents",
    [string]$AppDomain = "",
    [switch]$Help
)

# 颜色函数
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# 显示帮助
function Show-Help {
    @"
SSH密钥生成和配置脚本

用法: .\setup-ssh-keys.ps1 -ServerIP <IP> -ServerUser <用户名> [选项]

参数:
    -ServerIP       Ubuntu服务器IP地址 (必需)
    -ServerUser     Ubuntu服务器用户名 (必需)
    -ServerPath     部署路径 (默认: /opt/trading-agents)
    -AppDomain      应用域名 (可选，如果没有将使用IP)
    -Help           显示此帮助信息

示例:
    .\setup-ssh-keys.ps1 -ServerIP "*************" -ServerUser "ubuntu"
    .\setup-ssh-keys.ps1 -ServerIP "*************" -ServerUser "ubuntu" -AppDomain "myapp.com"

"@
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-ColorText "🔑 SSH密钥生成和配置助手" "Cyan"
    Write-ColorText "================================" "Cyan"
    Write-ColorText ""

    # 显示配置信息
    Write-ColorText "配置信息:" "Yellow"
    Write-ColorText "服务器IP: $ServerIP" "White"
    Write-ColorText "用户名: $ServerUser" "White"
    Write-ColorText "部署路径: $ServerPath" "White"
    
    if ($AppDomain) {
        Write-ColorText "应用域名: $AppDomain" "White"
    } else {
        Write-ColorText "应用域名: 使用IP地址" "White"
    }
    Write-ColorText ""

    # 确认继续
    $confirm = Read-Host "确认以上信息正确吗? (y/N)"
    if ($confirm -ne "y" -and $confirm -ne "Y") {
        Write-ColorText "操作已取消" "Red"
        return
    }

    try {
        # 1. 创建.ssh目录
        Write-ColorText "📁 创建SSH目录..." "Green"
        $sshDir = "$env:USERPROFILE\.ssh"
        if (-not (Test-Path $sshDir)) {
            New-Item -ItemType Directory -Force -Path $sshDir | Out-Null
            Write-ColorText "✅ SSH目录创建成功: $sshDir" "Green"
        } else {
            Write-ColorText "✅ SSH目录已存在: $sshDir" "Green"
        }

        # 2. 生成SSH密钥
        Write-ColorText "🔐 生成SSH密钥..." "Green"
        $keyPath = "$sshDir\trading_deploy"
        
        if (Test-Path $keyPath) {
            $overwrite = Read-Host "SSH密钥已存在，是否覆盖? (y/N)"
            if ($overwrite -ne "y" -and $overwrite -ne "Y") {
                Write-ColorText "使用现有密钥" "Yellow"
            } else {
                Remove-Item $keyPath -Force
                Remove-Item "$keyPath.pub" -Force -ErrorAction SilentlyContinue
            }
        }

        if (-not (Test-Path $keyPath)) {
            # 使用空密码短语生成密钥
            $process = Start-Process -FilePath "ssh-keygen" -ArgumentList @(
                "-t", "rsa",
                "-b", "4096",
                "-C", "trading-agents-deploy",
                "-f", $keyPath,
                "-N", ""
            ) -Wait -PassThru -NoNewWindow

            if ($process.ExitCode -eq 0) {
                Write-ColorText "✅ SSH密钥生成成功" "Green"
            } else {
                throw "SSH密钥生成失败"
            }
        }

        # 3. 读取密钥内容
        Write-ColorText "📖 读取密钥内容..." "Green"
        $privateKey = Get-Content $keyPath -Raw
        $publicKey = Get-Content "$keyPath.pub" -Raw

        # 4. 配置服务器SSH访问
        Write-ColorText "🔗 配置服务器SSH访问..." "Green"
        Write-ColorText "请输入服务器密码以配置SSH密钥访问:" "Yellow"
        
        # 尝试使用ssh-copy-id
        $sshCopyProcess = Start-Process -FilePath "ssh-copy-id" -ArgumentList @(
            "-i", "$keyPath.pub",
            "$ServerUser@$ServerIP"
        ) -Wait -PassThru -NoNewWindow

        if ($sshCopyProcess.ExitCode -eq 0) {
            Write-ColorText "✅ SSH公钥配置成功" "Green"
        } else {
            Write-ColorText "⚠️ ssh-copy-id失败，请手动配置" "Yellow"
            Write-ColorText "请将以下公钥内容添加到服务器的 ~/.ssh/authorized_keys 文件中:" "Yellow"
            Write-ColorText $publicKey "Cyan"
            Read-Host "配置完成后按回车继续"
        }

        # 5. 测试SSH连接
        Write-ColorText "🧪 测试SSH连接..." "Green"
        $testProcess = Start-Process -FilePath "ssh" -ArgumentList @(
            "-i", $keyPath,
            "-o", "StrictHostKeyChecking=no",
            "$ServerUser@$ServerIP",
            "echo SSH connection test successful"
        ) -Wait -PassThru -NoNewWindow

        if ($testProcess.ExitCode -eq 0) {
            Write-ColorText "✅ SSH连接测试成功" "Green"
        } else {
            Write-ColorText "❌ SSH连接测试失败" "Red"
            throw "SSH连接配置有问题"
        }

        # 6. 生成GitHub Secrets配置
        Write-ColorText "📝 生成GitHub Secrets配置..." "Green"
        
        # 设置URL
        if ($AppDomain) {
            $deployUrl = "https://$AppDomain"
            $apiUrl = "https://api.$AppDomain"
            $wsUrl = "wss://ws.$AppDomain"
        } else {
            $deployUrl = "http://$ServerIP`:3000"
            $apiUrl = "http://$ServerIP`:5000"
            $wsUrl = "ws://$ServerIP`:8000"
        }

        $secretsConfig = @"
# GitHub Secrets 配置
# 请将以下内容添加到 GitHub 仓库的 Secrets 中

# 服务器连接信息
DEPLOY_SSH_KEY=$privateKey
DEPLOY_HOST=$ServerIP
DEPLOY_USER=$ServerUser
DEPLOY_PATH=$ServerPath

# 应用URL配置
DEPLOY_URL=$deployUrl
API_URL=$apiUrl
WS_URL=$wsUrl

# 数据库配置（请替换为实际密码）
MYSQL_ROOT_PASSWORD=请设置您的数据库root密码
MYSQL_PASSWORD=请设置您的数据库用户密码

# 阿里云镜像服务（已配置）
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
"@

        # 保存配置到文件
        $secretsConfig | Out-File -FilePath "github_secrets_config.txt" -Encoding UTF8
        Write-ColorText "✅ GitHub Secrets配置已保存到: github_secrets_config.txt" "Green"

        # 7. 复制私钥到剪贴板
        Write-ColorText "📋 复制私钥到剪贴板..." "Green"
        $privateKey | Set-Clipboard
        Write-ColorText "✅ 私钥已复制到剪贴板" "Green"

        # 8. 显示完成信息
        Write-ColorText ""
        Write-ColorText "🎉 配置完成！" "Green"
        Write-ColorText "================================" "Cyan"
        Write-ColorText ""
        Write-ColorText "📋 下一步操作:" "Yellow"
        Write-ColorText "1. 查看生成的配置文件: github_secrets_config.txt" "White"
        Write-ColorText "2. 进入GitHub仓库 Settings > Secrets and variables > Actions" "White"
        Write-ColorText "3. 逐个添加配置文件中的所有Secrets" "White"
        Write-ColorText "4. 推送代码到 develop 或 main 分支测试部署" "White"
        Write-ColorText ""
        Write-ColorText "💡 提示:" "Yellow"
        Write-ColorText "- 私钥已复制到剪贴板，可直接粘贴到 DEPLOY_SSH_KEY" "White"
        Write-ColorText "- 请记得设置数据库密码" "White"
        Write-ColorText "- 配置文件保存在当前目录的 github_secrets_config.txt" "White"

    } catch {
        Write-ColorText "❌ 配置过程中出现错误: $($_.Exception.Message)" "Red"
        Write-ColorText "请检查网络连接和服务器配置" "Red"
    }
}

# 运行主函数
Main
