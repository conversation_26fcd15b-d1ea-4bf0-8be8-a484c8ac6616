# SSH部署配置完整指南

## 🎯 概述

现在的部署配置会通过SSH连接到您的服务器，自动拉取Docker镜像并启动服务。

## 🔐 必需的GitHub Secrets（11个）

### 1. 服务器连接信息（4个）
```
DEPLOY_SSH_KEY=你的SSH私钥内容
DEPLOY_HOST=你的服务器IP地址
DEPLOY_USER=你的服务器用户名
DEPLOY_PATH=/opt/trading-agents
```

### 2. 应用URL配置（3个）
```
DEPLOY_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com
WS_URL=wss://ws.yourdomain.com
```

### 3. 数据库配置（2个）
```
MYSQL_ROOT_PASSWORD=你的数据库root密码
MYSQL_PASSWORD=你的数据库用户密码
```

### 4. 阿里云镜像服务（2个）
```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 🖥️ 服务器准备步骤

### 1. 服务器基础环境
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

### 2. 创建应用目录
```bash
# 创建部署目录
sudo mkdir -p /opt/trading-agents
sudo chown $USER:$USER /opt/trading-agents
cd /opt/trading-agents
```

### 3. 配置防火墙
```bash
# Ubuntu UFW
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3000  # 应用端口
sudo ufw enable

# 或者 CentOS firewalld
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 🔑 SSH密钥配置

### 1. 生成SSH密钥对
```bash
# 在本地机器上生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "trading-agents-deploy" -f ~/.ssh/trading_deploy

# 这会生成两个文件：
# ~/.ssh/trading_deploy     (私钥 - 添加到GitHub Secrets)
# ~/.ssh/trading_deploy.pub (公钥 - 添加到服务器)
```

### 2. 将公钥添加到服务器
```bash
# 方法1：使用ssh-copy-id
ssh-copy-id -i ~/.ssh/trading_deploy.pub user@your-server-ip

# 方法2：手动添加
cat ~/.ssh/trading_deploy.pub | ssh user@your-server-ip "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys && chmod 700 ~/.ssh"
```

### 3. 测试SSH连接
```bash
# 测试SSH连接
ssh -i ~/.ssh/trading_deploy user@your-server-ip "echo 'SSH连接成功'"

# 测试Docker权限
ssh -i ~/.ssh/trading_deploy user@your-server-ip "docker --version && docker ps"
```

## 📝 GitHub Secrets配置示例

### 完整配置示例
```bash
# 服务器连接信息
DEPLOY_SSH_KEY=-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
...你的完整SSH私钥内容...
...
-----END RSA PRIVATE KEY-----

DEPLOY_HOST=*************
DEPLOY_USER=ubuntu
DEPLOY_PATH=/opt/trading-agents

# 应用URL（根据你的实际情况选择）
# 选项1：使用域名
DEPLOY_URL=https://mytrading.com
API_URL=https://api.mytrading.com
WS_URL=wss://ws.mytrading.com

# 选项2：使用IP和端口
DEPLOY_URL=http://*************:3000
API_URL=http://*************:5000
WS_URL=ws://*************:8000

# 数据库配置
MYSQL_ROOT_PASSWORD=MySecureRootPassword123!
MYSQL_PASSWORD=MySecureUserPassword123!

# 阿里云镜像服务
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 🚀 部署流程说明

### 自动部署步骤
1. **代码推送** → 触发GitHub Actions
2. **代码检查** → ESLint、TypeScript检查
3. **安全扫描** → 依赖和文件扫描
4. **构建镜像** → Docker镜像构建
5. **推送镜像** → 推送到阿里云
6. **SSH连接** → 连接到您的服务器
7. **部署应用** → 在服务器上拉取镜像并启动
8. **健康检查** → 验证部署成功

### SSH部署详细过程
```bash
# 1. 设置SSH密钥和连接
# 2. 上传配置文件到服务器
# 3. SSH到服务器执行：
#    - 登录阿里云镜像服务
#    - 拉取最新镜像
#    - 停止旧服务
#    - 启动新服务
#    - 清理旧镜像
# 4. 健康检查验证部署
```

## 🔧 服务器上的文件结构

部署完成后，服务器上的文件结构：
```
/opt/trading-agents/
├── .env.production          # 环境配置文件
├── docker-compose.yml       # Docker Compose配置
└── logs/                    # 应用日志（如果有）
```

## 🔍 验证部署

### 1. 检查容器状态
```bash
# SSH到服务器
ssh -i ~/.ssh/trading_deploy user@your-server-ip

# 检查容器运行状态
cd /opt/trading-agents
docker-compose ps

# 查看容器日志
docker-compose logs -f
```

### 2. 检查应用访问
```bash
# 检查应用是否可访问
curl http://your-server-ip:3000
curl http://your-server-ip:3000/api/health
```

### 3. 检查资源使用
```bash
# 检查系统资源
docker stats
df -h
free -h
```

## 🆘 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH密钥格式
cat ~/.ssh/trading_deploy | head -1
# 应该显示: -----BEGIN RSA PRIVATE KEY-----

# 检查服务器SSH服务
ssh -v user@your-server-ip
```

#### 2. Docker权限问题
```bash
# 确认用户在docker组中
ssh user@your-server-ip "groups"

# 重新登录或重启服务器
ssh user@your-server-ip "sudo systemctl restart docker"
```

#### 3. 镜像拉取失败
```bash
# 手动测试阿里云登录
ssh user@your-server-ip
echo "ezreal123" | docker login --username aliyun1315382626 --password-stdin crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
```

#### 4. 端口访问问题
```bash
# 检查防火墙状态
sudo ufw status
sudo firewall-cmd --list-all

# 检查端口监听
netstat -tlnp | grep :3000
```

### 调试命令
```bash
# 查看GitHub Actions日志
# 在GitHub仓库的Actions页面查看详细日志

# 查看服务器容器日志
ssh user@your-server-ip "cd /opt/trading-agents && docker-compose logs --tail=100"

# 查看系统日志
ssh user@your-server-ip "sudo journalctl -u docker -f"
```

## 📋 部署检查清单

### 服务器准备
- [ ] Docker已安装并运行
- [ ] Docker Compose已安装
- [ ] 用户已添加到docker组
- [ ] 防火墙端口已开放
- [ ] 部署目录已创建

### SSH配置
- [ ] SSH密钥对已生成
- [ ] 公钥已添加到服务器
- [ ] SSH连接测试成功
- [ ] Docker权限测试成功

### GitHub Secrets
- [ ] DEPLOY_SSH_KEY（SSH私钥）
- [ ] DEPLOY_HOST（服务器IP）
- [ ] DEPLOY_USER（服务器用户名）
- [ ] DEPLOY_PATH（部署路径）
- [ ] DEPLOY_URL（应用URL）
- [ ] API_URL（API URL）
- [ ] WS_URL（WebSocket URL）
- [ ] MYSQL_ROOT_PASSWORD（数据库root密码）
- [ ] MYSQL_PASSWORD（数据库用户密码）
- [ ] ALIYUN_REGISTRY_USERNAME（阿里云用户名）
- [ ] ALIYUN_REGISTRY_PASSWORD（阿里云密码）

---

**🎉 配置完成后，推送代码到 `main` 或 `develop` 分支，系统会自动SSH到您的服务器进行部署！**
