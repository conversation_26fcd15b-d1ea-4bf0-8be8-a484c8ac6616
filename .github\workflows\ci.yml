# TradingAgents Frontend 统一 CI/CD Pipeline
# 集成代码质量检查、Docker构建、安全扫描和自动部署
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    tags: ['v*']
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: '强制部署（跳过检查）'
        required: false
        default: false
        type: boolean
      push_to_registry:
        description: '是否推送到镜像仓库'
        required: false
        default: true
        type: boolean

permissions:
  contents: read
  security-events: write
  packages: write
  pull-requests: write

env:
  NODE_VERSION: '18'
  # 阿里云容器镜像服务
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  ALIYUN_IMAGE_NAME: frontend
  DOCKER_BUILDKIT: 1
  # 部署环境URL
  DEPLOY_URL: ${{ secrets.DEPLOY_URL }}
  API_URL: ${{ secrets.API_URL }}

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest

    outputs:
      cache-hit: ${{ steps.cache.outputs.cache-hit }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整历史用于分析

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 缓存依赖
        id: cache
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: 安装依赖
        run: npm ci

      - name: ESLint 代码检查
        run: npm run lint
        continue-on-error: false

      - name: TypeScript 类型检查
        run: npm run type-check

  # 单元测试（预留）
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    if: false # 暂时禁用，等添加测试后启用

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 运行测试
        run: npm test -- --coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        if: always()
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Docker 构建和推送
  build-and-push:
    name: Docker 构建和推送
    needs: [code-quality]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: 登录到阿里云容器镜像服务
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 提取镜像元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.ALIYUN_IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=staging,enable=${{ github.ref == 'refs/heads/develop' }}
          labels: |
            org.opencontainers.image.title=TradingAgents Frontend
            org.opencontainers.image.description=多智能体大语言模型金融交易框架前端
            org.opencontainers.image.vendor=TradingAgents
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.documentation=https://github.com/${{ github.repository }}/blob/main/README.md

      - name: 构建并推送 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          # 根据需要选择平台，单平台构建更快
          platforms: 'linux/amd64'
          push: ${{ github.event_name != 'pull_request' && (github.event.inputs.push_to_registry != 'false') }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            NODE_ENV=production
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VCS_REF=${{ github.sha }}

      - name: Docker 镜像安全扫描
        if: github.event_name != 'pull_request'
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.ALIYUN_IMAGE_NAME }}:${{ steps.meta.outputs.version }}
          format: 'sarif'
          output: 'docker-security-results.sarif'

      - name: 生成镜像清单
        if: github.event_name != 'pull_request'
        run: |
          echo "## Docker 镜像信息" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像标签**: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像摘要**: ${{ steps.build.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: $(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_STEP_SUMMARY
          echo "- **Git SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # 镜像测试
  test-images:
    name: 测试 Docker 镜像
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.event_name != 'pull_request'

    steps:
      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到阿里云注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 测试镜像启动
        run: |
          # 提取第一个标签进行测试
          IMAGE_TAG=$(echo "${{ needs.build-and-push.outputs.image-tag }}" | head -n1)
          echo "测试镜像: $IMAGE_TAG"

          # 运行容器并检查健康状态
          docker run --rm -d --name test-container \
            -p 3000:3000 \
            -e NODE_ENV=production \
            "$IMAGE_TAG"

          # 等待容器启动
          sleep 30

          # 检查容器是否正在运行
          if docker ps | grep test-container; then
            echo "✅ 容器启动成功"
          else
            echo "❌ 容器启动失败"
            docker logs test-container
            exit 1
          fi

          # 清理
          docker stop test-container || true

  # 部署到生产环境
  deploy:
    name: 部署应用
    needs: [build-and-push, test-images]
    runs-on: ubuntu-latest
    if: |
      (github.ref == 'refs/heads/main') ||
      (github.ref == 'refs/heads/develop') ||
      (github.event_name == 'workflow_dispatch')
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
      url: ${{ env.DEPLOY_URL }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置部署环境变量
        run: |
          if [ "${{ github.ref }}" == "refs/heads/main" ]; then
            echo "DEPLOY_ENV=production" >> $GITHUB_ENV
            echo "IMAGE_TAG=latest" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker/docker-compose.prod.frontend.yml" >> $GITHUB_ENV
          else
            echo "DEPLOY_ENV=staging" >> $GITHUB_ENV
            echo "IMAGE_TAG=staging" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker/docker-compose.prod.frontend.yml" >> $GITHUB_ENV
          fi

      - name: 准备部署配置
        run: |
          # 创建环境配置文件
          cat > .env.production << 'ENVEOF'
          NODE_ENV=production
          NEXT_PUBLIC_API_BASE_URL=${{ env.API_URL }}
          NEXT_PUBLIC_WS_URL=${{ secrets.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_OPENAI_API_KEY=${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}
          NEXT_PUBLIC_FINNHUB_API_KEY=${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}
          BACK_END_URL=${{ env.API_URL }}
          MYSQL_ROOT_PASSWORD=${{ secrets.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE=trading_analysis
          MYSQL_USER=trading_user
          MYSQL_PASSWORD=${{ secrets.MYSQL_PASSWORD }}
          ENVEOF

          echo "✅ .env.production 文件已创建"
          echo "文件大小: $(wc -c < .env.production) bytes"

      - name: 设置SSH密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 部署应用到服务器
        run: |
          echo "🚀 开始部署到 ${{ env.DEPLOY_ENV }} 环境..."

          # 验证本地文件存在
          if [ ! -f .env.production ]; then
            echo "❌ 本地 .env.production 文件不存在"
            exit 1
          fi

          if [ ! -f ${{ env.COMPOSE_FILE }} ]; then
            echo "❌ 本地 ${{ env.COMPOSE_FILE }} 文件不存在"
            exit 1
          fi

          # 设置部署路径
          DEPLOY_PATH="${{ secrets.DEPLOY_PATH }}"
          if [ -z "$DEPLOY_PATH" ]; then
            DEPLOY_PATH="/root"
            echo "⚠️ DEPLOY_PATH 未设置，使用默认路径: $DEPLOY_PATH"
          fi

          # 上传配置文件到服务器
          echo "📤 上传配置文件到服务器..."
          scp -i ~/.ssh/deploy_key .env.production ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/
          scp -i ~/.ssh/deploy_key ${{ env.COMPOSE_FILE }} ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/

          # SSH连接到服务器执行部署
          ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << 'EOF'
            cd ${{ secrets.DEPLOY_PATH }}

            echo "1. 登录阿里云镜像服务..."
            echo "${{ secrets.ALIYUN_REGISTRY_PASSWORD }}" | docker login --username ${{ secrets.ALIYUN_REGISTRY_USERNAME }} --password-stdin ${{ env.ALIYUN_REGISTRY }}

            echo "2. 拉取最新镜像..."
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production pull frontend

            echo "3. 停止旧前端服务..."
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production stop frontend || true
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production rm -f frontend || true

            echo "4. 清理前端相关资源..."
            docker container ls -a --filter "name=tradingagents-frontend" --format "{{.ID}}" | xargs -r docker rm -f || true

            echo "5. 启动新前端服务..."
            if ! docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production up -d frontend; then
              echo "❌ 容器启动失败，尝试强制重建..."
              docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production up -d --force-recreate frontend
            fi

            echo "6. 清理旧镜像..."
            docker image prune -f

            echo "✅ 部署完成"
          EOF

      - name: 健康检查
        run: |
          echo "🔍 执行健康检查..."
          sleep 60  # 给应用充分的启动时间

          # 检查容器状态
          echo "检查容器运行状态..."
          CONTAINER_STATUS=$(ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
            "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) ps frontend | grep -c 'Up'" 2>/dev/null || echo "0")

          if [ "$CONTAINER_STATUS" -gt 0 ]; then
            echo "✅ 容器运行正常"
            
            # 简单的HTTP连接检查
            echo "检查HTTP服务..."
            if curl -f -s --connect-timeout 15 --max-time 45 ${{ env.DEPLOY_URL }}/ > /dev/null 2>&1; then
              echo "✅ HTTP服务正常"
            else
              echo "⚠️ HTTP服务检查失败，但容器正在运行"
            fi
            
            echo "✅ 部署验证完成"
          else
            echo "❌ 容器未正常运行，查看日志:"
            ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
              "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) logs --tail=50 frontend"
          fi

      - name: 通知部署结果
        if: always()
        run: |
          echo "## 部署结果 📋" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ **应用部署成功**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "- **环境**: ${{ env.DEPLOY_ENV }}" >> $GITHUB_STEP_SUMMARY
            echo "- **访问地址**: [${{ env.DEPLOY_URL }}](${{ env.DEPLOY_URL }})" >> $GITHUB_STEP_SUMMARY
            echo "- **镜像标签**: ${{ env.IMAGE_TAG }}" >> $GITHUB_STEP_SUMMARY
            echo "- **部署时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 快速链接 🔗" >> $GITHUB_STEP_SUMMARY
            echo "- [应用首页](${{ env.DEPLOY_URL }})" >> $GITHUB_STEP_SUMMARY
            echo "- [健康检查](${{ env.DEPLOY_URL }}/api/health)" >> $GITHUB_STEP_SUMMARY
            
            echo "✅ 应用部署成功到 ${{ env.DEPLOY_ENV }} 环境"
            echo "🔗 访问地址: ${{ env.DEPLOY_URL }}"
          else
            echo "❌ **应用部署失败**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "请检查上述日志获取详细错误信息。" >> $GITHUB_STEP_SUMMARY
            
            echo "❌ 应用部署失败"
            echo "请检查 GitHub Actions 日志获取详细信息"
          fi

  # 性能测试
  performance-test:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
