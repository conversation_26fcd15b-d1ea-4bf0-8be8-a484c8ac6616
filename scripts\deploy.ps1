# TradingAgents Frontend 部署脚本 (PowerShell)
# 用于在 Windows 环境下自动化部署

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "production")]
    [string]$Environment,
    
    [switch]$Force,
    [switch]$Backup,
    [switch]$Rollback,
    [switch]$CheckOnly,
    [switch]$Verbose,
    [switch]$NoBuild,
    [switch]$NoMigrate,
    [switch]$NoRestart,
    [switch]$Help
)

# 颜色定义
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

# 日志函数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "Info" { 
            Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor $Colors.Blue
        }
        "Success" { 
            Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor $Colors.Green
        }
        "Warning" { 
            Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor $Colors.Yellow
        }
        "Error" { 
            Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor $Colors.Red
        }
    }
}

# 显示帮助信息
function Show-Help {
    @"
TradingAgents Frontend 部署脚本 (PowerShell)

用法: .\deploy.ps1 -Environment <环境> [选项]

环境:
    dev         开发环境
    staging     预发布环境
    production  生产环境

选项:
    -Force              强制部署（跳过确认）
    -Backup             部署前创建备份
    -Rollback           回滚到上一个版本
    -CheckOnly          仅检查环境状态
    -Verbose            详细输出
    -NoBuild            跳过构建步骤
    -NoMigrate          跳过数据库迁移
    -NoRestart          跳过服务重启
    -Help               显示此帮助信息

示例:
    .\deploy.ps1 -Environment staging
    .\deploy.ps1 -Environment production -Backup
    .\deploy.ps1 -Environment staging -Rollback
    .\deploy.ps1 -Environment production -CheckOnly

"@
}

# 检查必需的工具
function Test-Requirements {
    Write-Log "检查部署要求..." "Info"
    
    # 检查 Docker
    try {
        $dockerVersion = docker --version
        Write-Log "Docker 版本: $dockerVersion" "Info"
    }
    catch {
        Write-Log "Docker 未安装或未运行" "Error"
        exit 1
    }
    
    # 检查 Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Log "Docker Compose 版本: $composeVersion" "Info"
    }
    catch {
        Write-Log "Docker Compose 未安装" "Error"
        exit 1
    }
    
    # 检查配置文件
    if (-not (Test-Path $ComposeFile)) {
        Write-Log "Docker Compose 文件不存在: $ComposeFile" "Error"
        exit 1
    }
    
    if (-not (Test-Path $EnvFile)) {
        Write-Log "环境文件不存在: $EnvFile" "Warning"
        Write-Log "请从 .env.example 复制并配置环境文件" "Info"
    }
    
    Write-Log "环境检查通过" "Success"
}

# 检查服务状态
function Test-ServiceStatus {
    Write-Log "检查服务状态..." "Info"
    
    try {
        $containers = docker-compose -f $ComposeFile ps
        Write-Host $containers
        
        if ($containers -match "Up") {
            Write-Log "服务正在运行" "Success"
        }
        else {
            Write-Log "服务未运行" "Warning"
        }
    }
    catch {
        Write-Log "无法获取服务状态" "Error"
    }
    
    # 健康检查
    try {
        $response = Invoke-WebRequest -Uri "$ServiceUrl/api/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Log "服务健康检查通过" "Success"
        }
    }
    catch {
        Write-Log "服务健康检查失败" "Warning"
    }
}

# 创建备份
function New-Backup {
    if ($Backup) {
        Write-Log "创建备份..." "Info"
        
        $backupDir = "backups\$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        
        # 备份数据库
        Write-Log "备份数据库..." "Info"
        try {
            $env:MYSQL_PWD = $env:MYSQL_ROOT_PASSWORD
            docker-compose -f $ComposeFile exec -T mysql mysqldump -u root $env:MYSQL_DATABASE > "$backupDir\database.sql"
        }
        catch {
            Write-Log "数据库备份失败: $_" "Error"
        }
        
        # 备份配置文件
        Write-Log "备份配置文件..." "Info"
        Copy-Item $EnvFile "$backupDir\" -ErrorAction SilentlyContinue
        Copy-Item $ComposeFile "$backupDir\" -ErrorAction SilentlyContinue
        
        Write-Log "备份完成: $backupDir" "Success"
    }
}

# 构建应用
function Build-Application {
    if (-not $NoBuild) {
        Write-Log "构建应用程序..." "Info"
        
        # 拉取最新代码（如果在 Git 仓库中）
        if (Test-Path ".git") {
            Write-Log "拉取最新代码..." "Info"
            try {
                git pull origin main
            }
            catch {
                Write-Log "拉取代码失败: $_" "Warning"
            }
        }
        
        # 构建 Docker 镜像
        Write-Log "构建 Docker 镜像..." "Info"
        try {
            docker-compose -f $ComposeFile build --no-cache
            Write-Log "应用构建完成" "Success"
        }
        catch {
            Write-Log "构建失败: $_" "Error"
            exit 1
        }
    }
}

# 数据库迁移
function Invoke-DatabaseMigration {
    if (-not $NoMigrate) {
        Write-Log "执行数据库迁移..." "Info"
        
        # 检查是否有迁移文件
        if (Test-Path "database") {
            Write-Log "发现数据库迁移文件..." "Info"
            # 这里可以添加具体的迁移逻辑
        }
        
        Write-Log "数据库迁移完成" "Success"
    }
}

# 部署服务
function Deploy-Service {
    Write-Log "部署服务..." "Info"
    
    # 停止旧服务
    Write-Log "停止旧服务..." "Info"
    try {
        docker-compose -f $ComposeFile down
    }
    catch {
        Write-Log "停止服务时出现警告: $_" "Warning"
    }
    
    # 启动新服务
    Write-Log "启动新服务..." "Info"
    try {
        docker-compose -f $ComposeFile --env-file $EnvFile up -d
        
        # 等待服务启动
        Write-Log "等待服务启动..." "Info"
        Start-Sleep -Seconds 30
        
        Write-Log "服务部署完成" "Success"
    }
    catch {
        Write-Log "服务启动失败: $_" "Error"
        exit 1
    }
}

# 健康检查
function Test-HealthCheck {
    Write-Log "执行健康检查..." "Info"
    
    $maxRetries = 10
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        try {
            $response = Invoke-WebRequest -Uri "$ServiceUrl/api/health" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Log "健康检查通过" "Success"
                return $true
            }
        }
        catch {
            Write-Log "等待服务启动... ($($retryCount + 1)/$maxRetries)" "Info"
            Start-Sleep -Seconds 10
            $retryCount++
        }
    }
    
    Write-Log "健康检查失败" "Error"
    return $false
}

# 回滚操作
function Invoke-Rollback {
    Write-Log "开始回滚操作..." "Warning"
    
    # 查找最新的备份
    $latestBackup = Get-ChildItem "backups" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if (-not $latestBackup) {
        Write-Log "未找到备份文件" "Error"
        exit 1
    }
    
    Write-Log "使用备份: $($latestBackup.Name)" "Info"
    
    # 停止当前服务
    docker-compose -f $ComposeFile down
    
    # 恢复配置文件
    $backupPath = $latestBackup.FullName
    Copy-Item "$backupPath\$(Split-Path $EnvFile -Leaf)" $EnvFile -Force
    
    # 恢复数据库
    Write-Log "恢复数据库..." "Info"
    docker-compose -f $ComposeFile up -d mysql
    Start-Sleep -Seconds 20
    
    try {
        $env:MYSQL_PWD = $env:MYSQL_ROOT_PASSWORD
        Get-Content "$backupPath\database.sql" | docker-compose -f $ComposeFile exec -T mysql mysql -u root $env:MYSQL_DATABASE
    }
    catch {
        Write-Log "数据库恢复失败: $_" "Error"
    }
    
    # 重启服务
    docker-compose -f $ComposeFile up -d
    
    Write-Log "回滚完成" "Success"
}

# 主函数
function Main {
    # 显示帮助
    if ($Help) {
        Show-Help
        exit 0
    }
    
    # 设置环境特定的配置
    switch ($Environment) {
        "dev" {
            $script:ComposeFile = "docker\docker-compose.dev.yml"
            $script:EnvFile = ".env.local"
            $script:ServiceUrl = "http://localhost:3000"
        }
        "staging" {
            $script:ComposeFile = "docker\docker-compose.yml"
            $script:EnvFile = ".env.staging"
            $script:ServiceUrl = "https://staging.tradingagents.example.com"
        }
        "production" {
            $script:ComposeFile = "docker\docker-compose.prod.yml"
            $script:EnvFile = ".env.production"
            $script:ServiceUrl = "https://tradingagents.example.com"
        }
    }
    
    Write-Log "开始部署到 $Environment 环境" "Info"
    
    # 检查要求
    Test-Requirements
    
    # 如果只是检查状态
    if ($CheckOnly) {
        Test-ServiceStatus
        exit 0
    }
    
    # 如果是回滚操作
    if ($Rollback) {
        if (-not $Force) {
            $confirmation = Read-Host "确认回滚 $Environment 环境? (y/N)"
            if ($confirmation -ne "y" -and $confirmation -ne "Y") {
                Write-Log "取消回滚" "Info"
                exit 0
            }
        }
        Invoke-Rollback
        exit 0
    }
    
    # 生产环境需要确认
    if ($Environment -eq "production" -and -not $Force) {
        $confirmation = Read-Host "确认部署到生产环境? (y/N)"
        if ($confirmation -ne "y" -and $confirmation -ne "Y") {
            Write-Log "取消部署" "Info"
            exit 0
        }
    }
    
    # 执行部署步骤
    New-Backup
    Build-Application
    Invoke-DatabaseMigration
    Deploy-Service
    
    # 健康检查
    if (-not (Test-HealthCheck)) {
        Write-Log "部署失败，考虑回滚" "Error"
        exit 1
    }
    
    Write-Log "部署完成！" "Success"
    Write-Log "服务地址: $ServiceUrl" "Info"
}

# 运行主函数
Main
