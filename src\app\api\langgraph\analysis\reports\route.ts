import analysisStore from '@/lib/analysis-store';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);
    
    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 返回报告列表
    return NextResponse.json({
      analysisId: analysis.analysisId,
      reports: analysis.reports,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('获取报告失败:', error);
    return NextResponse.json(
      { error: '获取报告失败' },
      { status: 500 }
    );
  }
}
