'use client';

import { gsap } from 'gsap';
import { useEffect, useRef, useState } from 'react';

interface Option {
  value: string;
  label: string;
}

interface AnimatedSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
}

function AnimatedSelect({
  options,
  value,
  onChange,
  placeholder = "请选择选项",
  label
}: AnimatedSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);
  const arrowRef = useRef<SVGSVGElement>(null);

  // GSAP动画：打开下拉框
  const openDropdown = () => {
    setShouldRender(true);
    setIsOpen(true);

    // 使用 requestAnimationFrame 确保 DOM 已渲染
    requestAnimationFrame(() => {
      if (!optionsRef.current || !arrowRef.current) return;

      // 设置初始状态
      gsap.set(optionsRef.current, {
        opacity: 0,
        y: -10,
        scaleY: 0,
        transformOrigin: "top center"
      });

      // 动画展开
      const tl = gsap.timeline();
      tl.to(arrowRef.current, {
        rotation: 180,
        duration: 0.3,
        ease: "power2.out"
      })
      .to(optionsRef.current, {
        opacity: 1,
        y: 0,
        scaleY: 1,
        duration: 0.4,
        ease: "back.out(1.7)"
      }, "-=0.1");
    });
  };

  // GSAP动画：关闭下拉框
  const closeDropdown = () => {
    if (!optionsRef.current || !arrowRef.current) return;

    const tl = gsap.timeline({
      onComplete: () => {
        setIsOpen(false);
        setShouldRender(false);
      }
    });

    tl.to(optionsRef.current, {
      opacity: 0,
      y: -10,
      scaleY: 0,
      duration: 0.3,
      ease: "power2.in"
    })
    .to(arrowRef.current, {
      rotation: 0,
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.2");
  };

  // 切换下拉框状态
  const toggleDropdown = () => {
    if (isOpen) {
      closeDropdown();
    } else {
      openDropdown();
    }
  };

  // 处理选项点击
  const handleOptionClick = (optionValue: string) => {
    onChange(optionValue);
    closeDropdown();
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        if (isOpen) {
          closeDropdown();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // 获取显示文本
  const getDisplayText = () => {
    if (!value) return placeholder;
    const option = options.find(opt => opt.value === value);
    return option?.label || value;
  };

  return (
    <div className="space-y-2">
      {label && (
        <p className="text-white text-base font-medium leading-normal pb-2">{label}</p>
      )}
      
      <div className="relative" ref={dropdownRef}>
        {/* 主选择框 */}
        <div
          onClick={toggleDropdown}
          className="flex w-full items-center justify-between rounded-xl text-white focus:outline-0 focus:ring-0 border-none bg-[#282d43] h-14 px-4 text-base font-normal leading-normal cursor-pointer hover:bg-[#2a2f47] transition-colors"
        >
          <span className={!value ? "text-[#99a0c2]" : "text-white"}>
            {getDisplayText()}
          </span>
          
          {/* 动画箭头 */}
          <svg
            ref={arrowRef}
            xmlns="http://www.w3.org/2000/svg"
            width="24px"
            height="24px"
            fill="rgb(153,160,194)"
            viewBox="0 0 256 256"
            className="flex-shrink-0"
          >
            <path d="M181.66,170.34a8,8,0,0,1,0,11.32l-48,48a8,8,0,0,1-11.32,0l-48-48a8,8,0,0,1,11.32-11.32L128,212.69l42.34-42.35A8,8,0,0,1,181.66,170.34Zm-96-84.68L128,43.31l42.34,42.35a8,8,0,0,0,11.32-11.32l-48-48a8,8,0,0,0-11.32,0l-48,48A8,8,0,0,0,85.66,85.66Z" />
          </svg>
        </div>

        {/* 下拉选项 */}
        {shouldRender && (
          <div
            ref={optionsRef}
            className="absolute top-full left-0 right-0 mt-2 bg-[#282d43] rounded-xl border border-[#3a3f5c] shadow-lg z-50 max-h-60 overflow-y-auto"
          >
            {options.map((option, index) => {
              const isSelected = value === option.value;
              return (
                <div
                  key={option.value}
                  onClick={() => handleOptionClick(option.value)}
                  className={`
                    flex items-center justify-between px-4 py-3 cursor-pointer transition-all duration-200
                    ${isSelected 
                      ? 'bg-[#3a3f5c] text-white' 
                      : 'text-[#99a0c2] hover:bg-[#2a2f47] hover:text-white'
                    }
                    ${index === 0 ? 'rounded-t-xl' : ''}
                    ${index === options.length - 1 ? 'rounded-b-xl' : ''}
                  `}
                >
                  <span className="text-base font-normal">{option.label}</span>
                  
                  {/* 选中状态指示器 */}
                  {isSelected && (
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export { AnimatedSelect };
export default AnimatedSelect;
