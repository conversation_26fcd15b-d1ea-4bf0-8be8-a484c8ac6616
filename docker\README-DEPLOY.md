# TradingAgents 服务器部署指南

## 概述

本指南将帮助您在服务器上部署 TradingAgents 应用，使用已推送到阿里云容器镜像服务的镜像。

## 已推送的镜像

- **前端镜像**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest`
- **数据库镜像**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28`

## 前置要求

1. 服务器已安装 Docker 和 Docker Compose
2. 服务器可以访问互联网
3. 服务器开放以下端口：
   - 3000 (前端应用)
   - 13306 (MySQL数据库)
   - 80/443 (可选，如果使用 Nginx)

## 快速部署

### 方法一：使用自动化脚本 (推荐)

1. **上传文件到服务器**
   ```bash
   # 将以下文件上传到服务器的某个目录
   - docker-compose.prod.yml
   - deploy-server.sh (Linux) 或 deploy-server.bat (Windows)
   - .env.prod (可选)
   ```

2. **Linux 服务器部署**
   ```bash
   # 给脚本执行权限
   chmod +x deploy-server.sh
   
   # 运行部署脚本
   ./deploy-server.sh
   
   # 选择选项 1 进行完整部署
   ```

3. **Windows 服务器部署**
   ```cmd
   # 直接运行批处理文件
   deploy-server.bat
   
   # 选择选项 1 进行完整部署
   ```

### 方法二：手动部署

1. **登录阿里云容器镜像服务**
   ```bash
   docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
   # 密码: ezreal123
   ```

2. **拉取镜像**
   ```bash
   # 拉取前端镜像
   docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
   
   # 拉取数据库镜像
   docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
   
   # 拉取 Nginx 镜像 (可选)
   docker pull nginx:alpine
   ```

3. **启动服务**
   ```bash
   # 创建必要目录
   mkdir -p mysql/init
   
   # 启动服务
   docker-compose -f docker-compose.prod.yml up -d
   
   # 如果需要 Nginx 反向代理
   docker-compose -f docker-compose.prod.yml --profile nginx up -d
   ```

## 配置说明

### 环境变量配置

复制 `.env.prod` 为 `.env` 并修改以下配置：

```bash
# 将 your-server-ip 替换为服务器实际IP
NEXT_PUBLIC_API_BASE_URL=http://your-server-ip:5000
NEXT_PUBLIC_WS_URL=ws://your-server-ip:8000

# 配置API密钥
NEXT_PUBLIC_OPENAI_API_KEY=your-openai-api-key
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key
```

### 端口映射

- **3000**: 前端应用端口
- **13306**: MySQL数据库端口 (避免与系统MySQL冲突)
- **80**: Nginx HTTP端口 (可选)
- **443**: Nginx HTTPS端口 (可选)

## 常用命令

### 查看服务状态
```bash
docker-compose -f docker-compose.prod.yml ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs frontend
docker-compose -f docker-compose.prod.yml logs mysql
```

### 停止服务
```bash
docker-compose -f docker-compose.prod.yml down
```

### 重启服务
```bash
docker-compose -f docker-compose.prod.yml restart
```

### 更新镜像
```bash
# 拉取最新镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 重新创建容器
docker-compose -f docker-compose.prod.yml up -d --force-recreate frontend
```

## 故障排除

### 1. 容器启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.prod.yml logs [service-name]

# 检查容器状态
docker ps -a
```

### 2. 网络连接问题
- 检查防火墙设置
- 确认端口是否正确开放
- 检查环境变量配置

### 3. 数据库连接问题
- 检查MySQL容器是否正常启动
- 确认数据库密码配置正确
- 查看MySQL日志排查问题

### 4. 镜像拉取失败
- 检查网络连接
- 确认阿里云镜像服务登录状态
- 重试拉取命令

## 安全建议

1. **修改默认密码**
   - 修改MySQL root密码
   - 修改数据库用户密码

2. **配置防火墙**
   - 只开放必要端口
   - 限制数据库端口访问

3. **使用HTTPS**
   - 配置SSL证书
   - 启用Nginx HTTPS

4. **定期备份**
   - 备份数据库数据
   - 备份配置文件

## 监控和维护

### 健康检查
应用已配置健康检查，可通过以下方式查看：
```bash
docker-compose -f docker-compose.prod.yml ps
```

### 日志轮转
建议配置日志轮转以避免日志文件过大：
```bash
# 清理旧日志
docker system prune -f
```

### 资源监控
```bash
# 查看资源使用情况
docker stats
```

## 联系支持

如果在部署过程中遇到问题，请检查：
1. Docker和Docker Compose版本
2. 服务器系统要求
3. 网络连接状态
4. 日志错误信息
