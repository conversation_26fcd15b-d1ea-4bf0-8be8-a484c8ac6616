import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 更新任务状态
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: taskId } = await params;
    const { status, error_message } = await request.json();

    // 验证状态值
    const validStatuses = ['pending', 'running', 'completed', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `无效的状态值: ${status}。有效状态: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // 构建更新SQL
    let updateSql = 'UPDATE tasks SET status = ?, updated_at = NOW()';
    let updateParams = [status, taskId];

    // 根据状态添加时间戳
    if (status === 'running') {
      updateSql += ', started_at = NOW()';
    } else if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      updateSql += ', completed_at = NOW()';
    }

    // 如果有错误消息，添加到更新中
    if (error_message) {
      updateSql += ', error_message = ?';
      updateParams.splice(-1, 0, error_message); // 在taskId之前插入error_message
    }

    updateSql += ' WHERE task_id = ?';

    // 执行更新
    const result = await query(updateSql, updateParams);

    // 检查是否更新了任何行
    if ((result as any).affectedRows === 0) {
      return NextResponse.json({ error: '任务不存在' }, { status: 404 });
    }

    // 如果任务开始运行，添加系统消息
    if (status === 'running') {
      const { v4: uuidv4 } = require('uuid');
      await query(
        `INSERT INTO messages (
          message_id, task_id, message_type, content, sequence_number
        ) VALUES (?, ?, ?, ?, ?)`,
        [
          uuidv4(),
          taskId,
          'system',
          '任务开始执行',
          1
        ]
      );
    }

    return NextResponse.json({
      success: true,
      message: '任务状态更新成功',
      task_id: taskId,
      status: status
    });

  } catch (error) {
    console.error('更新任务状态失败:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return NextResponse.json(
      {
        error: '更新任务状态失败',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
