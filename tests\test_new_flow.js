/**
 * 测试新的任务流程和数据库结构
 * 在浏览器控制台中运行此脚本来验证新流程
 */

class TaskFlowTester {
  constructor() {
    this.testResults = [];
    this.createdTasks = [];
    this.baseUrl = window.location.origin;
  }

  // 记录测试结果
  logTest(testName, success, message = '', data = null) {
    const result = {
      testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}: ${message}`);
    if (data && !success) {
      console.log('   Data:', data);
    }
  }

  // 测试创建任务
  async testCreateTask() {
    const testName = '创建任务';
    
    try {
      const taskData = {
        ticker: 'TEST',
        title: '测试任务 - 新流程验证',
        description: '验证移除conversation概念后的任务流程',
        config: {
          analysisType: 'comprehensive',
          testMode: true
        },
        created_by: 'test_user'
      };
      
      const response = await fetch(`${this.baseUrl}/api/database/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });
      
      if (response.ok) {
        const result = await response.json();
        const taskId = result.task_id;
        if (taskId) {
          this.createdTasks.push(taskId);
          this.logTest(testName, true, `任务创建成功，ID: ${taskId}`);
          return taskId;
        } else {
          this.logTest(testName, false, '响应中缺少task_id', result);
          return null;
        }
      } else {
        const errorText = await response.text();
        this.logTest(testName, false, `HTTP ${response.status}`, errorText);
        return null;
      }
    } catch (error) {
      this.logTest(testName, false, `异常: ${error.message}`);
      return null;
    }
  }

  // 测试获取任务详情
  async testGetTask(taskId) {
    const testName = '获取任务详情';
    
    try {
      const response = await fetch(`${this.baseUrl}/api/database/tasks/${taskId}`);
      
      if (response.ok) {
        const task = await response.json();
        const requiredFields = ['task_id', 'ticker', 'title', 'status'];
        const missingFields = requiredFields.filter(field => !(field in task));
        
        if (missingFields.length === 0) {
          this.logTest(testName, true, `任务详情获取成功，状态: ${task.status}`);
          return true;
        } else {
          this.logTest(testName, false, `缺少必要字段: ${missingFields.join(', ')}`, task);
          return false;
        }
      } else {
        const errorText = await response.text();
        this.logTest(testName, false, `HTTP ${response.status}`, errorText);
        return false;
      }
    } catch (error) {
      this.logTest(testName, false, `异常: ${error.message}`);
      return false;
    }
  }

  // 测试更新任务状态
  async testUpdateTaskStatus(taskId) {
    const testName = '更新任务状态';
    
    try {
      const response = await fetch(`${this.baseUrl}/api/database/tasks/${taskId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: 'running' })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          this.logTest(testName, true, '任务状态更新为running成功');
          return true;
        } else {
          this.logTest(testName, false, '更新失败', result);
          return false;
        }
      } else {
        const errorText = await response.text();
        this.logTest(testName, false, `HTTP ${response.status}`, errorText);
        return false;
      }
    } catch (error) {
      this.logTest(testName, false, `异常: ${error.message}`);
      return false;
    }
  }

  // 测试添加消息（新结构，无conversation_id）
  async testAddMessage(taskId) {
    const testName = '添加消息';
    
    try {
      const messageData = {
        task_id: taskId,
        message_type: 'system',
        content: '测试消息 - 验证新的消息结构',
        metadata: {
          test: true,
          timestamp: Date.now()
        },
        thread_id: `test_thread_${Math.random().toString(36).substr(2, 8)}`
      };
      
      const response = await fetch(`${this.baseUrl}/api/database/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          this.logTest(testName, true, `消息添加成功，ID: ${result.message_id}`);
          return true;
        } else {
          this.logTest(testName, false, '添加失败', result);
          return false;
        }
      } else {
        const errorText = await response.text();
        this.logTest(testName, false, `HTTP ${response.status}`, errorText);
        return false;
      }
    } catch (error) {
      this.logTest(testName, false, `异常: ${error.message}`);
      return false;
    }
  }

  // 测试获取任务消息
  async testGetTaskMessages(taskId) {
    const testName = '获取任务消息';
    
    try {
      const params = new URLSearchParams({
        task_id: taskId,
        include_metadata: 'true'
      });
      
      const response = await fetch(`${this.baseUrl}/api/database/messages?${params}`);
      
      if (response.ok) {
        const result = await response.json();
        const messages = result.messages || [];
        
        if (Array.isArray(messages)) {
          this.logTest(testName, true, `获取到 ${messages.length} 条消息`);
          
          // 验证消息结构
          if (messages.length > 0) {
            const firstMessage = messages[0];
            const requiredFields = ['message_id', 'task_id', 'message_type', 'content'];
            const missingFields = requiredFields.filter(field => !(field in firstMessage));
            
            if (missingFields.length === 0) {
              // 验证没有conversation_id字段
              if (!('conversation_id' in firstMessage)) {
                this.logTest('消息结构验证', true, '消息结构正确，已移除conversation_id');
              } else {
                this.logTest('消息结构验证', false, '消息中仍包含conversation_id字段');
              }
            } else {
              this.logTest('消息结构验证', false, `消息缺少必要字段: ${missingFields.join(', ')}`);
            }
          }
          
          return true;
        } else {
          this.logTest(testName, false, '响应格式错误', result);
          return false;
        }
      } else {
        const errorText = await response.text();
        this.logTest(testName, false, `HTTP ${response.status}`, errorText);
        return false;
      }
    } catch (error) {
      this.logTest(testName, false, `异常: ${error.message}`);
      return false;
    }
  }

  // 清理测试数据
  async cleanup() {
    console.log('\n🧹 清理测试数据...');
    for (const taskId of this.createdTasks) {
      try {
        await fetch(`${this.baseUrl}/api/database/tasks/${taskId}/status`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: 'cancelled' })
        });
        console.log(`   任务 ${taskId} 已标记为取消`);
      } catch (error) {
        // 忽略清理错误
      }
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => !result.success)
        .forEach(result => {
          console.log(`   - ${result.testName}: ${result.message}`);
        });
    }
    
    console.log('\n✅ 新流程验证完成！');
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始测试新的任务流程...');
    console.log('='.repeat(50));
    
    try {
      // 1. 创建任务
      const taskId = await this.testCreateTask();
      if (!taskId) {
        console.log('❌ 任务创建失败，停止后续测试');
        return;
      }
      
      // 2. 获取任务详情
      await this.testGetTask(taskId);
      
      // 3. 更新任务状态
      await this.testUpdateTaskStatus(taskId);
      
      // 4. 添加消息
      await this.testAddMessage(taskId);
      
      // 5. 获取任务消息
      await this.testGetTaskMessages(taskId);
      
      // 生成测试报告
      this.generateReport();
      
      // 清理
      await this.cleanup();
      
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    }
  }
}

// 使用方法：
// 在浏览器控制台中运行：
// const tester = new TaskFlowTester();
// tester.runAllTests();

// 如果在Node.js环境中，导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TaskFlowTester;
}

// 如果直接在浏览器中运行，创建全局实例
if (typeof window !== 'undefined') {
  window.TaskFlowTester = TaskFlowTester;
  
  // 提供快捷方式
  window.testNewFlow = async function() {
    const tester = new TaskFlowTester();
    await tester.runAllTests();
  };
  
  console.log('💡 使用方法：在控制台中运行 testNewFlow() 来开始测试');
}
