import analysisStore from '@/lib/analysis-store';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('id') || searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);

    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 返回完整的分析状态
    return NextResponse.json(analysis);

  } catch (error) {
    console.error('获取分析状态失败:', error);
    return NextResponse.json(
      { error: '获取分析状态失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);
    
    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 停止分析
    analysis.status = 'stopped';
    analysis.endTime = new Date().toISOString();
    analysisStore.set(analysisId, analysis);

    return NextResponse.json({
      message: '分析已停止',
      analysisId,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('停止分析失败:', error);
    return NextResponse.json(
      { error: '停止分析失败' },
      { status: 500 }
    );
  }
}
