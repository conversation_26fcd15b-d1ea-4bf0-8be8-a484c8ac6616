#!/bin/bash

# TradingAgents Frontend Setup Script
# 用于快速设置和启动前端项目

set -e

echo "🚀 TradingAgents Frontend Setup"
echo "================================"

# 检查 Node.js 版本
check_node_version() {
    echo "📋 检查 Node.js 版本..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装。请安装 Node.js 18.0 或更高版本。"
        echo "   下载地址: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        echo "❌ Node.js 版本过低。当前版本: $NODE_VERSION，需要: $REQUIRED_VERSION 或更高"
        exit 1
    fi
    
    echo "✅ Node.js 版本检查通过: $NODE_VERSION"
}

# 检查包管理器
check_package_manager() {
    echo "📋 检查包管理器..."
    
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        echo "✅ 使用 Yarn"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        echo "✅ 使用 npm"
    else
        echo "❌ 未找到包管理器 (npm 或 yarn)"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn install
    else
        npm install
    fi
    
    echo "✅ 依赖安装完成"
}

# 设置环境变量
setup_environment() {
    echo "⚙️  设置环境变量..."
    
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.local.example" ]; then
            cp .env.local.example .env.local
            echo "✅ 已创建 .env.local 文件"
            echo "📝 请编辑 .env.local 文件，配置必要的环境变量："
            echo "   - NEXT_PUBLIC_API_BASE_URL"
            echo "   - NEXT_PUBLIC_WS_URL"
            echo "   - NEXT_PUBLIC_OPENAI_API_KEY"
            echo "   - NEXT_PUBLIC_FINNHUB_API_KEY"
        else
            echo "❌ 未找到 .env.local.example 文件"
            exit 1
        fi
    else
        echo "✅ .env.local 文件已存在"
    fi
}

# 运行类型检查
run_type_check() {
    echo "🔍 运行 TypeScript 类型检查..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn type-check
    else
        npm run type-check
    fi
    
    echo "✅ 类型检查通过"
}

# 运行代码检查
run_lint() {
    echo "🔍 运行代码检查..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn lint
    else
        npm run lint
    fi
    
    echo "✅ 代码检查通过"
}

# 启动开发服务器
start_dev_server() {
    echo "🚀 启动开发服务器..."
    echo "   访问地址: http://localhost:3000"
    echo "   按 Ctrl+C 停止服务器"
    echo ""
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn dev
    else
        npm run dev
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h          显示此帮助信息"
    echo "  --install-only      仅安装依赖，不启动服务器"
    echo "  --skip-checks       跳过类型检查和代码检查"
    echo "  --production        构建生产版本"
    echo ""
    echo "示例:"
    echo "  $0                  完整设置并启动开发服务器"
    echo "  $0 --install-only   仅安装依赖"
    echo "  $0 --production     构建生产版本"
}

# 构建生产版本
build_production() {
    echo "🏗️  构建生产版本..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn build
    else
        npm run build
    fi
    
    echo "✅ 生产版本构建完成"
    echo "💡 使用以下命令启动生产服务器:"
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        echo "   yarn start"
    else
        echo "   npm start"
    fi
}

# 主函数
main() {
    local install_only=false
    local skip_checks=false
    local production=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --install-only)
                install_only=true
                shift
                ;;
            --skip-checks)
                skip_checks=true
                shift
                ;;
            --production)
                production=true
                shift
                ;;
            *)
                echo "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行设置步骤
    check_node_version
    check_package_manager
    install_dependencies
    setup_environment
    
    if [ "$production" = true ]; then
        if [ "$skip_checks" = false ]; then
            run_type_check
            run_lint
        fi
        build_production
        exit 0
    fi
    
    if [ "$install_only" = true ]; then
        echo "✅ 依赖安装完成"
        exit 0
    fi
    
    if [ "$skip_checks" = false ]; then
        run_type_check
        run_lint
    fi
    
    start_dev_server
}

# 运行主函数
main "$@"
