import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AKShareNewsItem } from '@/types';
import { AIMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';

// 新闻分析提示模板
const newsAnalysisPrompt = PromptTemplate.fromTemplate(`
你是一位资深的财经新闻分析师，专门分析新闻事件对股票价格的影响。你拥有深厚的财经新闻解读能力和敏锐的市场洞察力。

【分析任务】
请对以下股票的相关新闻进行全面分析：

股票代码: {ticker}
分析日期: {date}
新闻数量: {newsCount}条

【新闻数据】
{newsData}

【分析框架】
请按照以下框架进行新闻分析：

1. **新闻分类与权重**
   - 重大利好消息：业绩超预期、重大合作、政策支持等
   - 重大利空消息：业绩不达预期、负面事件、监管风险等
   - 中性消息：常规公告、行业动态等
   - 按影响程度给每类新闻评分（1-10分）

2. **关键新闻深度解读**
   - 选择3-5条最重要的新闻进行深度分析
   - 分析新闻的真实性和可信度
   - 评估新闻对公司基本面的影响
   - 预测市场对新闻的反应程度

3. **时效性分析**
   - 新闻发布时间分布
   - 市场反应的时间窗口
   - 新闻影响的持续性预测

4. **市场影响评估**
   - 短期影响（1-3个交易日）
   - 中期影响（1-4周）
   - 长期影响（1-3个月）
   - 对股价波动幅度的预测

5. **信息质量评估**
   - 新闻来源的权威性
   - 信息的完整性和准确性
   - 是否存在信息不对称
   - 市场预期与实际情况的差异

6. **投资建议**
   - 基于新闻分析的操作建议
   - 关键关注点和风险提示
   - 后续需要跟踪的新闻动向

【输出要求】
- 客观分析新闻内容，避免情绪化解读
- 突出关键信息和核心影响因素
- 提供量化的影响评估
- 识别可能被市场忽视的重要信息
`);

export async function newsAnalystNode(
  state: typeof TradingAgentAnnotation.State
): Promise<Partial<typeof TradingAgentAnnotation.State>> {
  const { ticker, date, config, data, messages } = state;

  console.log(`[新闻分析师] 开始分析股票: ${ticker}`);

  try {
    // 优先使用状态中的新闻数据，如果没有则重新获取
    let newsData = data.newsData;

    if (!newsData || !Array.isArray(newsData) || newsData.length === 0) {
      console.log(`[新闻分析师] 重新获取新闻数据...`);
      newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
        symbol: ticker,
        limit: 30, // 获取30条新闻进行分析
      });
    }

    if (!Array.isArray(newsData) || newsData.length === 0) {
      throw new Error('无法获取有效的新闻数据');
    }

    console.log(`[新闻分析师] 分析 ${newsData.length} 条新闻...`);

    // 新闻分类和初步分析
    const categorizedNews = categorizeNews(newsData);
    const keyNews = identifyKeyNews(newsData);
    const newsTimeline = analyzeNewsTimeline(newsData);

    // 使用LLM进行深度新闻分析
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    let deepAnalysisReport = '';
    let newsImpact = {};

    if (OPENAI_API_KEY) {
      try {
        const llm = new ChatOpenAI({
          modelName: config.quickThinkLLM || 'gpt-4o-mini',
          temperature: 0.2,
          openAIApiKey: OPENAI_API_KEY,
          configuration: {
            baseURL: OPENAI_BASE_URL,
          },
        });

        // 准备新闻摘要用于LLM分析
        const newsSummary = newsData
          .slice(0, 15)
          .map((item, index) => {
            const title = item.标题 || item.title || '';
            const time = item.发布时间 || item.time || '';
            const source = item.来源 || item.source || '';
            return `${index + 1}. [${time}] ${title} (来源: ${source})`;
          })
          .join('\n');

        const prompt = await newsAnalysisPrompt.format({
          ticker,
          date,
          newsCount: newsData.length,
          newsData: newsSummary,
        });

        console.log(`[新闻分析师] 正在进行深度新闻分析...`);
        const response = await llm.invoke(prompt);
        deepAnalysisReport = response.content as string;

        // 提取新闻影响评估
        newsImpact = extractNewsImpact(deepAnalysisReport);
      } catch (llmError) {
        console.warn(`[新闻分析师] LLM分析失败，使用基础分析: ${llmError}`);
        deepAnalysisReport = generateBasicNewsReport(categorizedNews, keyNews);
      }
    } else {
      deepAnalysisReport = generateBasicNewsReport(categorizedNews, keyNews);
    }

    // 计算新闻影响评分
    const impactScore = calculateNewsImpactScore(categorizedNews);
    const credibilityScore = assessNewsCredibility(newsData);

    // 生成摘要
    const summary = generateNewsSummary(keyNews, impactScore, newsData.length);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【新闻分析师报告】\n\n${deepAnalysisReport}`,
        name: 'NewsAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      news: {
        summary,
        report: deepAnalysisReport,
        categorizedNews,
        keyNews,
        newsTimeline,
        newsImpact,
        impactScore,
        credibilityScore,
        totalNews: newsData.length,
        analyst: 'NewsAnalyst',
        timestamp: new Date().toISOString(),
        confidence: calculateNewsConfidence(newsData, categorizedNews),
      },
    };

    console.log(`[新闻分析师] 分析完成，影响评分: ${impactScore.toFixed(2)}`);
    return {
      messages: newMessages,
      analysis,
      currentStage: 'news_analysis_completed',
      progress: Math.min(state.progress + 20, 100),
    };
  } catch (error) {
    console.error('[新闻分析师] 分析失败:', error);
    const errorMessage = `新闻分析失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【新闻分析师】${errorMessage}`,
        name: 'NewsAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      news: {
        summary: '新闻分析失败',
        report: errorMessage,
        error: true,
        analyst: 'NewsAnalyst',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, analysis };
  }
}

// 辅助函数：新闻分类
function categorizeNews(newsData: any[]): {
  positive: any[];
  negative: any[];
  neutral: any[];
} {
  const positive: any[] = [];
  const negative: any[] = [];
  const neutral: any[] = [];

  const positiveKeywords = ['利好', '增长', '盈利', '合作', '突破', '获得', '成功', '上涨', '推荐'];
  const negativeKeywords = ['利空', '下跌', '亏损', '风险', '调查', '处罚', '下降', '警告', '减持'];

  newsData.forEach((news) => {
    const title = news.标题 || news.title || '';
    const content = news.内容 || news.content || '';
    const fullText = `${title} ${content}`;

    let positiveCount = 0;
    let negativeCount = 0;

    positiveKeywords.forEach((keyword) => {
      if (fullText.includes(keyword)) positiveCount++;
    });

    negativeKeywords.forEach((keyword) => {
      if (fullText.includes(keyword)) negativeCount++;
    });

    if (positiveCount > negativeCount) {
      positive.push({ ...news, category: 'positive', score: positiveCount });
    } else if (negativeCount > positiveCount) {
      negative.push({ ...news, category: 'negative', score: negativeCount });
    } else {
      neutral.push({ ...news, category: 'neutral', score: 0 });
    }
  });

  return { positive, negative, neutral };
}

// 辅助函数：识别关键新闻
function identifyKeyNews(newsData: any[]): any[] {
  // 基于关键词权重和时效性识别重要新闻
  const keywordWeights = {
    业绩: 10,
    财报: 10,
    重组: 9,
    收购: 9,
    合作: 8,
    政策: 8,
    监管: 8,
    增持: 7,
    减持: 7,
    分红: 6,
    公告: 5,
  };

  return newsData
    .map((news) => {
      const title = news.标题 || news.title || '';
      let importance = 0;

      Object.entries(keywordWeights).forEach(([keyword, weight]) => {
        if (title.includes(keyword)) {
          importance += weight;
        }
      });

      return { ...news, importance };
    })
    .sort((a, b) => b.importance - a.importance)
    .slice(0, 5); // 返回前5条重要新闻
}

// 辅助函数：分析新闻时间线
function analyzeNewsTimeline(newsData: any[]): {
  recent24h: number;
  recent3days: number;
  recent1week: number;
} {
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  let recent24h = 0;
  let recent3days = 0;
  let recent1week = 0;

  newsData.forEach((news) => {
    const timeStr = news.发布时间 || news.time || '';
    const newsTime = new Date(timeStr);

    if (newsTime > oneDayAgo) recent24h++;
    if (newsTime > threeDaysAgo) recent3days++;
    if (newsTime > oneWeekAgo) recent1week++;
  });

  return { recent24h, recent3days, recent1week };
}

// 辅助函数：生成基础新闻报告
function generateBasicNewsReport(categorizedNews: any, keyNews: any[]): string {
  const { positive, negative, neutral } = categorizedNews;

  return `基础新闻分析报告：

新闻分类统计：
- 正面新闻：${positive.length} 条
- 负面新闻：${negative.length} 条  
- 中性新闻：${neutral.length} 条

关键新闻摘要：
${keyNews
  .slice(0, 3)
  .map((news, index) => `${index + 1}. ${news.标题 || news.title} (重要性: ${news.importance})`)
  .join('\n')}

总体评估：
${
  positive.length > negative.length
    ? '正面新闻占主导，市场情绪相对乐观'
    : negative.length > positive.length
    ? '负面新闻较多，需要关注风险'
    : '新闻情绪相对平衡，市场反应可能温和'
}

建议：密切关注后续新闻动向，特别是业绩相关和政策相关的重要公告。`;
}

// 辅助函数：提取新闻影响
function extractNewsImpact(report: string): Record<string, any> {
  const impact: Record<string, any> = {};

  // 提取短期影响
  const shortTermMatch = report.match(/短期影响[：:]?\s*([^。\n]+)/);
  if (shortTermMatch) impact.shortTerm = shortTermMatch[1];

  // 提取中期影响
  const mediumTermMatch = report.match(/中期影响[：:]?\s*([^。\n]+)/);
  if (mediumTermMatch) impact.mediumTerm = mediumTermMatch[1];

  // 提取长期影响
  const longTermMatch = report.match(/长期影响[：:]?\s*([^。\n]+)/);
  if (longTermMatch) impact.longTerm = longTermMatch[1];

  return impact;
}

// 辅助函数：计算新闻影响评分
function calculateNewsImpactScore(categorizedNews: any): number {
  const { positive, negative, neutral } = categorizedNews;

  const positiveScore = positive.reduce((sum: number, news: any) => sum + (news.score || 1), 0);
  const negativeScore = negative.reduce((sum: number, news: any) => sum + (news.score || 1), 0);

  const totalNews = positive.length + negative.length + neutral.length;
  if (totalNews === 0) return 0;

  return (positiveScore - negativeScore) / totalNews;
}

// 辅助函数：评估新闻可信度
function assessNewsCredibility(newsData: any[]): number {
  const authoritiveSources = [
    '新华社',
    '人民日报',
    '证券时报',
    '上海证券报',
    '中国证券报',
    '财经',
    '第一财经',
  ];

  let credibleCount = 0;
  newsData.forEach((news) => {
    const source = news.来源 || news.source || '';
    if (authoritiveSources.some((authSource) => source.includes(authSource))) {
      credibleCount++;
    }
  });

  return newsData.length > 0 ? credibleCount / newsData.length : 0;
}

// 辅助函数：生成新闻摘要
function generateNewsSummary(keyNews: any[], impactScore: number, totalNews: number): string {
  const impactLevel = impactScore > 1 ? '正面' : impactScore < -1 ? '负面' : '中性';
  const topNews = keyNews.length > 0 ? keyNews[0].标题 || keyNews[0].title : '暂无重要新闻';

  return `${impactLevel}影响 - 分析${totalNews}条新闻，影响评分${impactScore.toFixed(
    2
  )}。重点关注：${topNews.substring(0, 50)}...`;
}

// 辅助函数：计算新闻分析置信度
function calculateNewsConfidence(newsData: any[], categorizedNews: any): number {
  const totalNews = newsData.length;
  if (totalNews < 5) return 0.4;
  if (totalNews < 15) return 0.6;

  // 基于新闻分布的均衡性计算置信度
  const { positive, negative, neutral } = categorizedNews;
  const distribution = [positive.length, negative.length, neutral.length];
  const maxRatio = Math.max(...distribution) / totalNews;

  // 如果某一类新闻占比过高，置信度降低
  if (maxRatio > 0.8) return 0.5;
  if (maxRatio > 0.6) return 0.7;

  return 0.8;
}
