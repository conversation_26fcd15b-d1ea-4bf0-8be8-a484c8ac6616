import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentAnnotation } from '@/lib/langgraph-state';

export async function dataCollectionNode(state: typeof TradingAgentAnnotation.State) {
  const ticker = state.ticker;
  if (!ticker) {
    throw new Error('Ticker not found in state');
  }

  console.log(`[Data Collection] Collecting data for ticker: ${ticker}`);

  try {
    // 并行获取所有数据
    const [fundamentalData, technicalData, newsData, sentimentData] = await Promise.all([
      akshareAdapter.invoke('get_financial_data', { symbol: ticker }),
      akshareAdapter.invoke('get_technical_indicators', { symbol: ticker }),
      akshareAdapter.invoke('get_stock_news', { symbol: ticker }),
      // 注意：我们没有专门的情绪数据工具，情绪分析是在 sentiment_analyst 节点中完成的。
      // 这里我们暂时将新闻数据也用作情绪分析的原始数据。
      akshareAdapter.invoke('get_stock_news', { symbol: ticker }),
    ]);

    const data = {
      fundamentalData,
      technicalData,
      newsData,
      sentimentData, // 使用新闻数据作为情绪分析的输入
    };

    return { data };
  } catch (error) {
    console.error('[Data Collection] Error:', error);
    const errorMessage = `数据收集失败: ${error instanceof Error ? error.message : String(error)}`;
    // 考虑如何将错误信息传递给状态
    return { messages: [{ type: 'ai', content: errorMessage }] };
  }
}
