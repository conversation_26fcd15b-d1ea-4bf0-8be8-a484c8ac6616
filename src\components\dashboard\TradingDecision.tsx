'use client';

import { motion } from 'framer-motion';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  MinusIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { TradingDecision as TradingDecisionType } from '@/lib/api';

interface TradingDecisionProps {
  decision: TradingDecisionType | null;
  isComplete: boolean;
  ticker: string;
}

export function TradingDecision({ decision, isComplete, ticker }: TradingDecisionProps) {
  if (!isComplete) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-600 dark:text-slate-400">
              分析进行中，交易决策即将生成...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!decision) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <p className="text-slate-600 dark:text-slate-400">
              暂无交易决策数据
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'buy':
        return <ArrowUpIcon className="h-8 w-8 text-green-600" />;
      case 'sell':
        return <ArrowDownIcon className="h-8 w-8 text-red-600" />;
      default:
        return <MinusIcon className="h-8 w-8 text-slate-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'buy':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'sell':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-800';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'buy':
        return '买入';
      case 'sell':
        return '卖出';
      default:
        return '持有';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'high':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-800';
    }
  };

  const getRiskText = (risk: string) => {
    switch (risk) {
      case 'low':
        return '低风险';
      case 'medium':
        return '中等风险';
      case 'high':
        return '高风险';
      default:
        return '未知';
    }
  };

  return (
    <div className="space-y-6">
      {/* 主要决策 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>交易决策 - {ticker}</span>
              <span className="text-sm text-slate-500">
                {new Date(decision.timestamp).toLocaleString()}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 交易行动 */}
              <div className="text-center">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${getActionColor(decision.action)} mb-4`}>
                  {getActionIcon(decision.action)}
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  {getActionText(decision.action)}
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  推荐行动
                </p>
              </div>

              {/* 信心度 */}
              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      className="text-slate-200 dark:text-slate-700"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path
                      className="text-blue-600"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      strokeDasharray={`${decision.confidence}, 100`}
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold text-slate-900 dark:text-white">
                      {decision.confidence}%
                    </span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  信心度
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  决策可信度
                </p>
              </div>

              {/* 风险等级 */}
              <div className="text-center">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${getRiskColor(decision.riskLevel)} mb-4`}>
                  <ShieldCheckIcon className="h-8 w-8" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  {getRiskText(decision.riskLevel)}
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  风险评估
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 详细参数 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>交易参数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {decision.targetPrice && (
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      目标价格
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    ${decision.targetPrice.toFixed(2)}
                  </div>
                </div>
              )}

              {decision.stopLoss && (
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <ShieldCheckIcon className="h-5 w-5 text-red-600" />
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      止损价格
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    ${decision.stopLoss.toFixed(2)}
                  </div>
                </div>
              )}

              {decision.positionSize && (
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      仓位大小
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    {decision.positionSize}%
                  </div>
                </div>
              )}

              {decision.timeHorizon && (
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <ClockIcon className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      时间周期
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    {decision.timeHorizon}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 决策理由 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>决策理由</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-slate dark:prose-invert max-w-none">
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                {decision.reasoning}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 免责声明 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="text-center"
      >
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            ⚠️ <strong>免责声明:</strong> 此交易决策仅供参考，不构成投资建议。
            投资有风险，决策需谨慎。请根据自身情况和风险承受能力做出投资决定。
          </p>
        </div>
      </motion.div>
    </div>
  );
}
