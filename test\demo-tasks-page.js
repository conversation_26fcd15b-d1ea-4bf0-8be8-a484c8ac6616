// 任务管理页面演示脚本
// 这个脚本会创建一些示例任务数据用于演示

const BASE_URL = 'http://localhost:3001';

// 创建示例任务的函数
async function createSampleTask(taskData) {
  try {
    const response = await fetch(`${BASE_URL}/api/database/tasks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(taskData),
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 创建任务成功:', taskData.ticker);
      return result;
    } else {
      console.log('⚠️  创建任务失败:', taskData.ticker, response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建任务错误:', taskData.ticker, error.message);
    return null;
  }
}

// 示例任务数据
const sampleTasks = [
  {
    ticker: 'NVDA',
    title: 'NVIDIA 深度技术分析',
    description: '分析NVIDIA在AI芯片市场的竞争优势和未来发展前景',
    status: 'pending',
    research_depth: 'deep',
    analysis_period: '3m',
    priority: 1
  },
  {
    ticker: 'TSLA',
    title: 'Tesla 电动车市场分析',
    description: '评估Tesla在全球电动车市场的地位和增长潜力',
    status: 'running',
    research_depth: 'medium',
    analysis_period: '1m',
    priority: 2
  },
  {
    ticker: 'AAPL',
    title: 'Apple 季度财报分析',
    description: '分析Apple最新季度财报和产品线表现',
    status: 'completed',
    research_depth: 'shallow',
    analysis_period: '1w',
    priority: 3
  },
  {
    ticker: 'MSFT',
    title: 'Microsoft 云服务业务评估',
    description: '深入分析Microsoft Azure和云服务业务的增长趋势',
    status: 'pending',
    research_depth: 'deep',
    analysis_period: '6m',
    priority: 1
  },
  {
    ticker: 'GOOGL',
    title: 'Google 广告业务分析',
    description: '评估Google广告业务在数字营销市场的表现',
    status: 'failed',
    research_depth: 'medium',
    analysis_period: '1m',
    priority: 2,
    error_message: '数据获取失败：API限制'
  }
];

// 创建示例消息的函数
async function createSampleMessage(taskId, messageData) {
  try {
    const response = await fetch(`${BASE_URL}/api/database/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        task_id: taskId,
        conversation_id: `conv_${taskId}`,
        ...messageData
      }),
    });
    
    if (response.ok) {
      console.log('✅ 创建消息成功');
      return await response.json();
    } else {
      console.log('⚠️  创建消息失败:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建消息错误:', error.message);
    return null;
  }
}

// 示例消息数据
const sampleMessages = [
  {
    message_type: 'human',
    content: '请分析NVIDIA的最新财报和技术发展趋势',
    metadata: { source: 'user_input' }
  },
  {
    message_type: 'ai',
    content: '我将为您分析NVIDIA的财报数据。首先让我获取最新的财务信息...',
    metadata: { analysis_type: 'financial' }
  },
  {
    message_type: 'system',
    content: '开始数据收集阶段',
    metadata: { stage: 'data_collection' }
  },
  {
    message_type: 'ai',
    content: '根据最新财报，NVIDIA在数据中心业务方面表现强劲，收入同比增长206%。AI芯片需求持续旺盛...',
    metadata: { analysis_result: 'positive', confidence: 0.85 }
  }
];

// 主演示函数
async function runDemo() {
  console.log('🎬 开始任务管理页面演示...\n');
  
  console.log('📝 创建示例任务...');
  const createdTasks = [];
  
  for (const taskData of sampleTasks) {
    const result = await createSampleTask(taskData);
    if (result) {
      createdTasks.push(result);
    }
    // 添加延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n✅ 成功创建 ${createdTasks.length} 个示例任务`);
  
  // 为第一个任务创建示例消息
  if (createdTasks.length > 0) {
    console.log('\n💬 为第一个任务创建示例消息...');
    const firstTaskId = createdTasks[0].task_id;
    
    for (const messageData of sampleMessages) {
      await createSampleMessage(firstTaskId, messageData);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log('\n🎉 演示数据创建完成！');
  console.log('\n📋 现在您可以：');
  console.log('1. 访问 http://localhost:3001/tasks 查看任务列表');
  console.log('2. 点击"详情"按钮查看任务详细信息');
  console.log('3. 点击"消息"按钮查看对话记录');
  console.log('4. 点击"开始"按钮尝试启动分析（需要后端服务）');
  console.log('\n💡 提示：如果任务已存在，创建可能会失败，这是正常的。');
}

// 清理演示数据的函数
async function cleanupDemo() {
  console.log('🧹 清理演示数据...');
  
  try {
    // 这里可以添加删除示例数据的逻辑
    // 注意：需要相应的删除API支持
    console.log('⚠️  清理功能需要删除API支持');
  } catch (error) {
    console.error('❌ 清理失败:', error.message);
  }
}

// 检查命令行参数
const args = process.argv.slice(2);
if (args.includes('--cleanup')) {
  cleanupDemo();
} else {
  runDemo();
}
