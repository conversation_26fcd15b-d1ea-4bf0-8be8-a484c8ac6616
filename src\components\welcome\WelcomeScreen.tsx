'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  CpuChipIcon, 
  ShieldCheckIcon,
  PlayIcon 
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { AnalysisConfigForm } from './AnalysisConfigForm';

interface WelcomeScreenProps {
  onStartAnalysis: (config: any) => void;
}

export function WelcomeScreen({ onStartAnalysis }: WelcomeScreenProps) {
  const [showConfigForm, setShowConfigForm] = useState(false);

  const features = [
    {
      icon: ChartBarIcon,
      title: '分析师团队',
      description: '基本面分析师、情绪分析师、新闻分析师、技术分析师协同工作',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    },
    {
      icon: UserGroupIcon,
      title: '研究团队',
      description: '多头和空头研究员通过结构化辩论平衡潜在收益与风险',
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
    },
    {
      icon: CpuChipIcon,
      title: '交易员代理',
      description: '基于分析师和研究员的综合报告做出明智的交易决策',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
    },
    {
      icon: ShieldCheckIcon,
      title: '风险管理',
      description: '持续评估投资组合风险，调整交易策略并提供风险评估',
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/20',
    },
  ];

  if (showConfigForm) {
    return (
      <AnalysisConfigForm
        onSubmit={onStartAnalysis}
        onBack={() => setShowConfigForm(false)}
      />
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-12">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-6"
      >
        <div className="flex justify-center">
          <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
            <ChartBarIcon className="h-10 w-10 text-white" />
          </div>
        </div>
        
        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white">
          TradingAgents
        </h1>
        
        <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
          多智能体大语言模型金融交易框架
        </p>
        
        <p className="text-lg text-slate-500 dark:text-slate-500 max-w-4xl mx-auto">
          通过部署专业的LLM驱动代理团队：从基本面分析师、情绪专家、技术分析师到交易员、风险管理团队，
          平台协同评估市场条件并为交易决策提供信息支持。
        </p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Button
            size="lg"
            onClick={() => setShowConfigForm(true)}
            className="px-8 py-4 text-lg"
          >
            <PlayIcon className="h-5 w-5 mr-2" />
            开始分析
          </Button>
        </motion.div>
      </motion.div>

      {/* Features Grid */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
          >
            <Card hover className="h-full">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg ${feature.bgColor}`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 dark:text-slate-400">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Workflow Diagram */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-center">工作流程</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
              {['分析师团队', '研究团队', '交易员', '风险管理', '投资组合管理'].map((step, index) => (
                <div key={step} className="flex items-center">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/20 text-blue-600 rounded-full font-semibold">
                      {index + 1}
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300 text-center">
                      {step}
                    </span>
                  </div>
                  {index < 4 && (
                    <div className="hidden md:block w-8 h-0.5 bg-slate-300 dark:bg-slate-600 ml-4" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Disclaimer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="text-center"
      >
        <p className="text-sm text-slate-500 dark:text-slate-500 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          ⚠️ TradingAgents框架仅用于研究目的。交易表现可能因多种因素而异，包括所选的骨干语言模型、模型温度、交易周期、数据质量和其他非确定性因素。
          <strong>不构成财务、投资或交易建议。</strong>
        </p>
      </motion.div>
    </div>
  );
}
