import sys
import json
import traceback
import akshare as ak
import pandas as pd

def get_stock_history(params):
    """
    Fetches historical stock data using akshare.
    """
    symbol = params.get("symbol")
    period = params.get("period", "daily")
    start_date = params.get("start_date", "20200101")
    end_date = params.get("end_date", "20231231")
    adjust = params.get("adjust", "")

    if not symbol:
        raise ValueError("Stock symbol is required.")

    df = ak.stock_zh_a_hist(symbol=symbol, period=period, start_date=start_date, end_date=end_date, adjust=adjust)
    
    # Convert date columns to string to ensure JSON serializability
    if isinstance(df.index, pd.DatetimeIndex):
        df.index = df.index.strftime('%Y-%m-%d')
    elif '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期']).dt.strftime('%Y-%m-%d')

    return json.loads(df.to_json(orient='records'))

def get_financial_data(params):
    """
    Fetches financial analysis indicators for a stock.
    """
    symbol = params.get("symbol")
    if not symbol:
        raise ValueError("Stock symbol is required.")
    
    df = ak.stock_financial_analysis_indicator(stock=symbol)
    
    # Clean up column names and handle potential NaN values
    df.columns = [col.strip() for col in df.columns]
    df = df.fillna(0) # Replace NaN with 0 or another appropriate value

    return json.loads(df.to_json(orient='records'))

def get_technical_indicators(params):
    """
    Calculates technical indicators for a stock.
    """
    symbol = params.get("symbol")
    start_date = params.get("start_date", "20200101")
    end_date = params.get("end_date", "20231231")
    ma_periods = params.get("ma_periods", [5, 10, 20])
    rsi_period = params.get("rsi_period", 14)

    if not symbol:
        raise ValueError("Stock symbol is required.")

    # Fetch historical data
    hist_df = ak.stock_zh_a_hist(symbol=symbol, start_date=start_date, end_date=end_date, adjust="")
    
    # Calculate Moving Averages
    for period in ma_periods:
        hist_df[f'MA{period}'] = hist_df['收盘'].rolling(window=period).mean()

    # Calculate RSI
    delta = hist_df['收盘'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    hist_df['RSI'] = 100 - (100 / (1 + rs))
    
    hist_df = hist_df.fillna(0)

    # Convert date columns to string
    if isinstance(hist_df.index, pd.DatetimeIndex):
        hist_df.index = hist_df.index.strftime('%Y-%m-%d')
    elif '日期' in hist_df.columns:
        hist_df['日期'] = pd.to_datetime(hist_df['日期']).dt.strftime('%Y-%m-%d')

    return json.loads(hist_df.to_json(orient='records'))

def get_stock_news(params):
    """
    Fetches stock news from Eastmoney.
    """
    symbol = params.get("symbol")
    if not symbol:
        raise ValueError("Stock symbol is required.")
    
    # AKShare's stock_news_em expects the symbol format like "sh600519" or "sz000001"
    # We might need to add logic to determine the market (sh/sz) if not provided.
    # For now, we assume the symbol is passed in the correct format.
    df = ak.stock_news_em(stock=symbol)

    # Convert datetime columns to string
    df['发布时间'] = pd.to_datetime(df['发布时间']).dt.strftime('%Y-%m-%d %H:%M:%S')
    
    return json.loads(df.to_json(orient='records'))

def main():
    """
    Main loop to listen for commands from stdin.
    """
    print("Python worker started. Waiting for commands...", file=sys.stderr)
    for line in sys.stdin:
        try:
            request = json.loads(line)
            command = request.get("command")
            params = request.get("params", {})
            request_id = request.get("request_id")

            response = {"request_id": request_id, "data": None, "error": None}

            # In a real implementation, you would call a function based on the command.
            # For now, we'll just echo the command.
            if command == "echo":
                response["data"] = params
            elif command == "get_stock_history":
                response["data"] = get_stock_history(params)
            elif command == "get_financial_data":
                response["data"] = get_financial_data(params)
            elif command == "get_technical_indicators":
                response["data"] = get_technical_indicators(params)
            elif command == "get_stock_news":
                response["data"] = get_stock_news(params)
            else:
                response["error"] = f"Unknown command: {command}"
            
            print(json.dumps(response), flush=True)

        except json.JSONDecodeError:
            error_response = {
                "error": "Invalid JSON format",
                "request_id": None
            }
            print(json.dumps(error_response), flush=True)
            print(f"JSON Decode Error for line: {line.strip()}", file=sys.stderr)
        except Exception as e:
            error_response = {
                "error": str(e),
                "traceback": traceback.format_exc(),
                "request_id": request.get("request_id") if 'request' in locals() else None
            }
            print(json.dumps(error_response), flush=True)
            print(f"An error occurred: {e}", file=sys.stderr)

if __name__ == "__main__":
    main()
