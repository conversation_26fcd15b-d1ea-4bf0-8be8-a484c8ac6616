-- 交易分析任务全流程记录数据库
-- 版本: 2.0 (简化版本 - 移除conversation概念)
-- 创建时间: 2024-12-19
-- 说明: 记录交易分析任务的完整流程，采用简化的Task->Message直接关联结构

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS trading_analysis;
USE trading_analysis;

-- 1. 任务表 - 记录分析任务的基本信息
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(36) UNIQUE NOT NULL COMMENT '任务唯一标识',
    ticker VARCHAR(20) NOT NULL COMMENT '股票代码',
    title VARCHAR(255) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    research_depth ENUM('shallow', 'medium', 'deep') DEFAULT 'medium' COMMENT '研究深度',
    analysis_period VARCHAR(20) DEFAULT '1m' COMMENT '分析周期',
    config JSON COMMENT '任务配置参数',
    priority INT DEFAULT 0 COMMENT '任务优先级',
    created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    INDEX idx_task_id (task_id),
    INDEX idx_ticker (ticker),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at),
    INDEX idx_research_depth (research_depth),
    INDEX idx_analysis_period (analysis_period),
    INDEX idx_status_created (status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 2. 消息表 - 记录任务执行过程中的消息
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(36) UNIQUE NOT NULL COMMENT '消息唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    message_type ENUM('human', 'ai', 'system', 'tool') NOT NULL COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    metadata JSON COMMENT '消息元数据',
    sequence_number INT NOT NULL DEFAULT 1 COMMENT '消息序号',
    parent_message_id VARCHAR(36) COMMENT '父消息ID（用于构建对话树）',
    thread_id VARCHAR(100) COMMENT 'LangGraph线程ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_task_id (task_id),
    INDEX idx_message_type (message_type),
    INDEX idx_sequence_number (sequence_number),
    INDEX idx_thread_id (thread_id),
    INDEX idx_created_at (created_at),
    INDEX idx_task_sequence (task_id, sequence_number),
    INDEX idx_task_type (task_id, message_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 3. 工具调用表 - 记录分析过程中的工具调用
CREATE TABLE IF NOT EXISTS tool_calls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_call_id VARCHAR(36) UNIQUE NOT NULL COMMENT '工具调用唯一标识',
    message_id VARCHAR(36) COMMENT '关联消息ID',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    tool_name VARCHAR(100) NOT NULL COMMENT '工具名称',
    tool_function VARCHAR(100) NOT NULL COMMENT '工具函数',
    input_parameters JSON COMMENT '输入参数',
    output_result JSON COMMENT '输出结果',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '调用状态',
    execution_time_ms INT COMMENT '执行时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE SET NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_tool_call_id (tool_call_id),
    INDEX idx_message_id (message_id),
    INDEX idx_task_id (task_id),
    INDEX idx_tool_name (tool_name),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具调用表';

-- 4. 分析结果表 - 记录最终的分析结果
CREATE TABLE IF NOT EXISTS analysis_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    result_id VARCHAR(36) UNIQUE NOT NULL COMMENT '结果唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    result_type ENUM('fundamental', 'technical', 'sentiment', 'risk', 'comprehensive') NOT NULL COMMENT '结果类型',
    result_data JSON NOT NULL COMMENT '分析结果数据',
    confidence_score DECIMAL(5,4) COMMENT '置信度分数(0-1)',
    summary TEXT COMMENT '结果摘要',
    recommendations JSON COMMENT '投资建议',
    risk_level ENUM('low', 'medium', 'high', 'very_high') COMMENT '风险等级',
    version INT DEFAULT 1 COMMENT '结果版本',
    is_final BOOLEAN DEFAULT FALSE COMMENT '是否为最终结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_result_id (result_id),
    INDEX idx_task_id (task_id),
    INDEX idx_result_type (result_type),
    INDEX idx_is_final (is_final),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析结果表';

-- 5. 分析步骤表 - 记录分析过程中的详细步骤
CREATE TABLE IF NOT EXISTS analysis_steps (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    step_id VARCHAR(36) UNIQUE NOT NULL COMMENT '步骤唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    step_name VARCHAR(255) NOT NULL COMMENT '步骤名称',
    step_type ENUM('data_collection', 'analysis', 'processing', 'validation', 'decision') NOT NULL COMMENT '步骤类型',
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT '步骤状态',
    progress INT DEFAULT 0 COMMENT '进度百分比(0-100)',
    description TEXT COMMENT '步骤描述',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    metrics JSON COMMENT '性能指标',
    duration_ms INT COMMENT '执行时长(毫秒)',
    sequence_order INT NOT NULL COMMENT '执行顺序',
    parent_step_id VARCHAR(36) COMMENT '父步骤ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_step_id (step_id),
    INDEX idx_task_id (task_id),
    INDEX idx_step_type (step_type),
    INDEX idx_status (status),
    INDEX idx_sequence_order (sequence_order),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析步骤表';

-- 6. 系统日志表 - 记录系统级别的操作日志
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL COMMENT '日志唯一标识',
    task_id VARCHAR(36) COMMENT '关联任务ID',
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL') NOT NULL COMMENT '日志级别',
    component VARCHAR(100) NOT NULL COMMENT '组件名称',
    operation VARCHAR(100) NOT NULL COMMENT '操作名称',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    user_id VARCHAR(100) COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE SET NULL,
    INDEX idx_log_id (log_id),
    INDEX idx_task_id (task_id),
    INDEX idx_log_level (log_level),
    INDEX idx_component (component),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 创建视图 - 任务概览视图
CREATE OR REPLACE VIEW task_overview AS
SELECT
    t.task_id,
    t.ticker,
    t.title,
    t.status as task_status,
    t.created_at as task_created_at,
    t.completed_at as task_completed_at,
    COUNT(DISTINCT m.message_id) as message_count,
    COUNT(DISTINCT tc.tool_call_id) as tool_call_count,
    COUNT(DISTINCT ar.result_id) as result_count,
    MAX(m.created_at) as last_activity_at,
    TIMESTAMPDIFF(SECOND, t.created_at, COALESCE(t.completed_at, NOW())) as duration_seconds
FROM tasks t
LEFT JOIN messages m ON t.task_id = m.task_id
LEFT JOIN tool_calls tc ON t.task_id = tc.task_id
LEFT JOIN analysis_results ar ON t.task_id = ar.task_id
GROUP BY t.task_id, t.ticker, t.title, t.status, t.created_at, t.completed_at;

-- 创建存储过程 - 创建新任务
DROP PROCEDURE IF EXISTS CreateAnalysisTask;
DELIMITER //
CREATE PROCEDURE CreateAnalysisTask(
    IN p_task_id VARCHAR(36),
    IN p_ticker VARCHAR(20),
    IN p_title VARCHAR(255),
    IN p_description TEXT,
    IN p_config JSON,
    IN p_created_by VARCHAR(100),
    IN p_research_depth ENUM('shallow', 'medium', 'deep'),
    IN p_analysis_period VARCHAR(20)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO tasks (task_id, ticker, title, description, config, created_by, status, research_depth, analysis_period)
    VALUES (p_task_id, p_ticker, p_title, p_description, p_config, p_created_by, 'pending', p_research_depth, p_analysis_period);
    
    COMMIT;
END //
DELIMITER ;

-- 创建存储过程 - 开始任务执行
DROP PROCEDURE IF EXISTS StartTaskExecution;
DELIMITER //
CREATE PROCEDURE StartTaskExecution(
    IN p_task_id VARCHAR(36),
    IN p_thread_id VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 更新任务状态为运行中
    UPDATE tasks SET
        status = 'running',
        started_at = NOW(),
        updated_at = NOW()
    WHERE task_id = p_task_id;

    -- 添加系统消息记录任务开始
    INSERT INTO messages (
        message_id, task_id, message_type, content,
        sequence_number, thread_id
    ) VALUES (
        UUID(), p_task_id, 'system',
        CONCAT('任务开始执行 - Thread ID: ', COALESCE(p_thread_id, 'N/A')),
        1, p_thread_id
    );

    COMMIT;
END //
DELIMITER ;

-- 插入示例数据
INSERT INTO tasks (task_id, ticker, title, description, config, created_by, research_depth, analysis_period) VALUES
(UUID(), 'AAPL', 'Apple股票分析', '分析Apple公司股票的投资价值', '{"analysis_type": "comprehensive", "time_horizon": "3_months"}', 'user1', 'medium', '3m'),
(UUID(), 'TSLA', 'Tesla技术分析', '重点关注Tesla的技术面分析', '{"analysis_type": "technical", "indicators": ["RSI", "MACD", "MA"]}', 'user2', 'deep', '1m'),
(UUID(), 'NVDA', 'NVIDIA基本面分析', '分析NVIDIA的基本面和未来前景', '{"analysis_type": "fundamental", "focus": ["earnings", "market_share"]}', 'user1', 'deep', '6m'),
(UUID(), 'MSFT', 'Microsoft综合分析', '微软公司全面投资分析', '{"analysis_type": "comprehensive", "include_sentiment": true}', 'user3', 'medium', '1m'),
(UUID(), 'GOOGL', 'Google快速分析', 'Alphabet公司快速投资评估', '{"analysis_type": "quick", "focus": ["technical"]}', 'user2', 'shallow', '1w');

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建完成信息
SELECT 'Database schema created successfully!' as status,
       'Tables: tasks, messages, tool_calls, analysis_results, analysis_steps, system_logs' as tables_created,
       'Views: task_overview' as views_created,
       'Procedures: CreateAnalysisTask, StartTaskExecution' as procedures_created;
