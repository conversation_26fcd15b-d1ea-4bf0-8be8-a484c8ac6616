import { query } from '@/lib/db';
import { Message } from '@/types/database';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('task_id');
    const messageType = searchParams.get('message_type');
    const threadId = searchParams.get('thread_id');
    const limit = searchParams.get('limit') || '50';
    const offset = searchParams.get('offset') || '0';
    const includeMetadata = searchParams.get('include_metadata') === 'true';

    // 构建查询条件
    let whereConditions: string[] = [];
    let queryParams: any[] = [];

    if (!taskId) {
      return NextResponse.json({ error: '必须提供 task_id' }, { status: 400 });
    }

    whereConditions.push('task_id = ?');
    queryParams.push(taskId);

    if (messageType) {
      whereConditions.push('message_type = ?');
      queryParams.push(messageType);
    }

    if (threadId) {
      whereConditions.push('thread_id = ?');
      queryParams.push(threadId);
    }

    // 构建完整的SQL查询
    let sql = `
      SELECT
        id,
        message_id,
        task_id,
        message_type,
        content,
        ${includeMetadata ? 'metadata,' : ''}
        sequence_number,
        parent_message_id,
        thread_id,
        created_at
      FROM messages
    `;

    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }

    sql += ' ORDER BY sequence_number ASC, created_at ASC';

    // 使用字符串拼接而不是参数化查询来避免MySQL参数问题
    sql += ` LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const messages = (await query(sql, queryParams)) as Message[];

    // 获取总数（用于分页）
    let countSql = 'SELECT COUNT(*) as total FROM messages';
    let countParams: any[] = [];

    if (whereConditions.length > 0) {
      countSql += ' WHERE ' + whereConditions.join(' AND ');
      countParams = queryParams; // 使用原始的查询参数（不包含limit和offset）
    }

    const countResult = (await query(countSql, countParams)) as any[];
    const total = countResult[0]?.total || 0;

    return NextResponse.json({
      messages,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: parseInt(offset) + parseInt(limit) < total,
      },
    });
  } catch (error) {
    console.error('查询消息失败:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return NextResponse.json(
      {
        error: '查询消息失败',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { task_id, message_type, content, metadata, parent_message_id, thread_id } =
      await request.json();

    // 验证必填字段
    if (!task_id || !message_type || !content) {
      return NextResponse.json(
        { error: '缺少必填字段: task_id, message_type, content' },
        { status: 400 }
      );
    }

    // 验证消息类型
    const validTypes = ['human', 'ai', 'system', 'tool'];
    if (!validTypes.includes(message_type)) {
      return NextResponse.json(
        { error: `无效的消息类型: ${message_type}。有效类型: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // 生成消息ID
    const { v4: uuidv4 } = require('uuid');
    const messageId = uuidv4();

    // 获取下一个序号
    const sequenceResult = (await query(
      'SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence FROM messages WHERE task_id = ?',
      [task_id]
    )) as any[];
    const sequenceNumber = sequenceResult[0]?.next_sequence || 1;

    // 插入消息
    await query(
      `INSERT INTO messages (
        message_id, task_id, message_type,
        content, metadata, sequence_number, parent_message_id, thread_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        messageId,
        task_id,
        message_type,
        content,
        metadata ? JSON.stringify(metadata) : null,
        sequenceNumber,
        parent_message_id,
        thread_id,
      ]
    );

    return NextResponse.json({
      message_id: messageId,
      sequence_number: sequenceNumber,
      success: true,
    });
  } catch (error) {
    console.error('添加消息失败:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return NextResponse.json(
      {
        error: '添加消息失败',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
