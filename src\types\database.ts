// 数据库实体类型定义
// 对应数据库表结构

export const researchDepthOptions = ['shallow', 'medium', 'deep'] as const;
export type ResearchDepth = (typeof researchDepthOptions)[number];

export const analysisPeriodOptions = ['1d', '1w', '1m', '3m', '6m', '1y', 'custom'] as const;
export type AnalysisPeriod = (typeof analysisPeriodOptions)[number];

export interface Task {
  id: number;
  task_id: string;
  ticker: string;
  title: string;
  description?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  research_depth: ResearchDepth;
  analysis_period: AnalysisPeriod;
  config?: any;
  priority: number;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
  error_message?: string;
}

export interface Message {
  id: number;
  message_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata?: any;
  sequence_number: number;
  parent_message_id?: string;
  thread_id?: string;
  created_at: Date;
}

export interface ToolCall {
  id: number;
  tool_call_id: string;
  message_id?: string;
  conversation_id: string;
  task_id: string;
  tool_name: string;
  tool_function: string;
  input_parameters?: any;
  output_result?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
  execution_time_ms?: number;
  error_message?: string;
  created_at: Date;
  completed_at?: Date;
}

export interface AnalysisResult {
  id: number;
  result_id: string;
  task_id: string;
  conversation_id?: string;
  result_type: 'fundamental' | 'technical' | 'sentiment' | 'decision' | 'risk' | 'comprehensive';
  result_data: any;
  confidence_score?: number;
  summary?: string;
  recommendations?: any;
  risk_level?: 'low' | 'medium' | 'high' | 'very_high';
  version: number;
  is_final: boolean;
  created_at: Date;
}

export interface AnalysisStep {
  id: number;
  step_id: string;
  task_id: string;
  step_name: string;
  step_type: 'data_collection' | 'analysis' | 'processing' | 'validation' | 'decision';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  description?: string;
  input_data?: any;
  output_data?: any;
  metrics?: any;
  duration_ms?: number;
  sequence_order: number;
  parent_step_id?: string;
  created_at: Date;
  started_at?: Date;
  completed_at?: Date;
}

export interface SystemLog {
  id: number;
  log_id: string;
  task_id?: string;
  log_level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
  component: string;
  operation: string;
  message: string;
  details?: any;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

// 视图类型
export interface TaskOverview {
  task_id: string;
  ticker: string;
  title: string;
  task_status: Task['status'];
  task_created_at: Date;
  task_completed_at?: Date;
  message_count: number;
  tool_call_count: number;
  result_count: number;
  last_activity_at?: Date;
  duration_seconds: number;
}

// API 请求/响应类型
export interface CreateTaskRequest {
  ticker: string;
  title: string;
  description?: string;
  config?: any;
  created_by?: string;
}

export interface CreateTaskResponse {
  success: boolean;
  task_id: string;
  message?: string;
}

export interface StartTaskExecutionRequest {
  task_id: string;
  thread_id?: string;
}

export interface StartTaskExecutionResponse {
  success: boolean;
  task_id: string;
  message?: string;
}

export interface AddMessageRequest {
  task_id: string;
  message_type: Message['message_type'];
  content: string;
  metadata?: any;
  parent_message_id?: string;
  thread_id?: string;
}

export interface AddMessageResponse {
  success: boolean;
  message_id: string;
  sequence_number: number;
  message?: string;
}

export interface RecordToolCallRequest {
  task_id: string;
  tool_name: string;
  tool_function: string;
  input_parameters?: any;
  message_id?: string;
}

export interface RecordToolCallResponse {
  success: boolean;
  tool_call_id: string;
  message?: string;
}

export interface UpdateToolCallRequest {
  tool_call_id: string;
  output_result?: any;
  status: ToolCall['status'];
  execution_time_ms?: number;
  error_message?: string;
}

export interface SaveAnalysisResultRequest {
  task_id: string;
  result_type: AnalysisResult['result_type'];
  result_data: any;
  confidence_score?: number;
  summary?: string;
  recommendations?: any;
  risk_level?: AnalysisResult['risk_level'];
  is_final?: boolean;
}

export interface SaveAnalysisResultResponse {
  success: boolean;
  result_id: string;
  message?: string;
}

// 查询选项
export interface TaskQueryOptions {
  status?: Task['status'][];
  ticker?: string;
  created_by?: string;
  date_from?: Date;
  date_to?: Date;
  limit?: number;
  offset?: number;
  include_conversations?: boolean;
  include_messages?: boolean;
  include_results?: boolean;
}

export interface ConversationQueryOptions {
  task_id?: string;
  status?: ('active' | 'completed' | 'failed' | 'cancelled')[];
  limit?: number;
  offset?: number;
  include_messages?: boolean;
  include_tool_calls?: boolean;
}

// 统计类型
export interface TaskStatistics {
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  running_tasks: number;
  average_duration_seconds: number;
  total_conversations: number;
  total_messages: number;
  total_tool_calls: number;
  success_rate: number;
}

export interface ConversationStatistics {
  conversation_id: string;
  message_count: number;
  tool_call_count: number;
  duration_seconds: number;
  last_activity: Date;
  completion_status: string;
}
