# GitHub Secrets 配置检查指南

## 🔍 问题诊断

您遇到的错误 `Username and password required` 表明 GitHub Actions 无法访问阿里云的登录凭据。

## ✅ 解决步骤

### 1. 检查 GitHub Secrets 是否存在

1. 进入您的 GitHub 仓库页面
2. 点击 `Settings` 选项卡
3. 在左侧菜单选择 `Secrets and variables` > `Actions`
4. 确认存在以下 Repository secrets：

```
✅ ALIYUN_REGISTRY_USERNAME
✅ ALIYUN_REGISTRY_PASSWORD
```

### 2. 添加缺失的 Secrets

如果 Secrets 不存在，请添加：

**添加第一个 Secret:**
- 点击 `New repository secret`
- Name: `ALIYUN_REGISTRY_USERNAME`
- Secret: `aliyun1315382626`
- 点击 `Add secret`

**添加第二个 Secret:**
- 点击 `New repository secret`
- Name: `ALIYUN_REGISTRY_PASSWORD`
- Secret: `ezreal123`
- 点击 `Add secret`

### 3. 验证 Secret 名称

确保名称完全匹配（区分大小写）：

| ✅ 正确 | ❌ 错误 |
|---------|---------|
| `ALIYUN_REGISTRY_USERNAME` | `aliyun_registry_username` |
| `ALIYUN_REGISTRY_PASSWORD` | `ALIYUN_USERNAME` |
| | `aliyun_password` |

### 4. 检查 Secret 值

确保 Secret 值正确：
- Username: `aliyun1315382626`
- Password: `ezreal123`
- 注意：不要有多余的空格或换行符

### 5. 重新运行工作流

添加 Secrets 后：
1. 进入 `Actions` 选项卡
2. 找到失败的工作流运行
3. 点击 `Re-run jobs` > `Re-run all jobs`

## 🔧 调试方法

### 方法1: 查看调试输出

我已经在工作流中添加了调试步骤，重新运行后查看输出：

```
Registry: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
Username exists: true
Password exists: true
```

如果显示 `false`，说明 Secrets 未正确配置。

### 方法2: 手动测试登录

在本地测试阿里云登录：

```bash
# 测试登录命令
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com

# 输入密码: ezreal123
```

### 方法3: 检查网络连接

确认 GitHub Actions 可以访问阿里云：

```bash
# 测试网络连接
ping crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
```

## 🚨 常见错误

### 错误1: Secret 名称不匹配
```
Error: Username and password required
```
**解决**: 检查 Secret 名称是否完全匹配

### 错误2: Secret 值包含特殊字符
```
Error: unauthorized: authentication required
```
**解决**: 确保密码中没有特殊字符被转义

### 错误3: 网络连接问题
```
Error: failed to resolve registry
```
**解决**: 检查网络连接或使用备用地址

## 📝 完整配置检查清单

- [ ] GitHub Secrets 已添加
  - [ ] `ALIYUN_REGISTRY_USERNAME` = `aliyun1315382626`
  - [ ] `ALIYUN_REGISTRY_PASSWORD` = `ezreal123`
- [ ] Secret 名称大小写正确
- [ ] Secret 值没有多余空格
- [ ] 工作流文件语法正确
- [ ] 网络连接正常

## 🔄 测试流程

1. **添加/更新 Secrets**
2. **推送代码触发工作流**
   ```bash
   git add .
   git commit -m "test: 验证阿里云登录配置"
   git push origin main
   ```
3. **查看 Actions 运行结果**
4. **检查调试输出**
5. **确认登录成功**

## 📞 获取帮助

如果问题仍然存在：

1. **查看完整的 Actions 日志**
2. **检查阿里云服务状态**
3. **验证账号权限**
4. **联系项目维护者**

---

**💡 提示**: 添加 Secrets 后，通常需要重新运行工作流才能生效。确保在正确的仓库中添加 Secrets，而不是在 fork 的仓库中。
