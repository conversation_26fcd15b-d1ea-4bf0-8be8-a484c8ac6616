'use client';

import { Message } from '@/types/database';
import 'highlight.js/styles/github-dark.css';
import React, { useState } from 'react';

interface MessageQueryProps {
  className?: string;
}

interface ConversationInfo {
  conversation_id: string;
  task_id: string;
  title?: string;
  status: string;
  created_at: string;
  last_message_at?: string;
}

interface MessageQueryResult {
  conversation: ConversationInfo;
  messages: Message[];
  statistics: {
    total: number;
    byType: Record<string, number>;
  };
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

const MessageQuery: React.FC<MessageQueryProps> = ({ className = '' }) => {
  const [conversationId, setConversationId] = useState('');
  const [messageType, setMessageType] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<MessageQueryResult | null>(null);
  const [renderMarkdown, setRenderMarkdown] = useState(true);

  const handleQuery = async () => {
    if (!conversationId.trim()) {
      setError('请输入对话ID');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const url = new URL(`/api/database/conversations/${conversationId}/messages`, window.location.origin);
      if (messageType) {
        url.searchParams.set('message_type', messageType);
      }
      url.searchParams.set('include_metadata', 'true');

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getMessageTypeColor = (type: string) => {
    const colors = {
      human: 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400',
      ai: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400',
      system: 'bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-300',
      tool: 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400'
    };
    return colors[type as keyof typeof colors] || 'bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-300';
  };

  const getMessageTypeLabel = (type: string) => {
    const labels = {
      human: '用户',
      ai: 'AI',
      system: '系统',
      tool: '工具'
    };
    return labels[type as keyof typeof labels] || type;
  };

  // 消息内容渲染组件
  const MessageContent: React.FC<{ content: string; renderAsMarkdown: boolean }> = ({
    content,
    renderAsMarkdown
  }) => {
    if (renderAsMarkdown) {
      try {
        // 动态导入ReactMarkdown以避免SSR问题
        const ReactMarkdown = require('react-markdown').default;
        const remarkGfm = require('remark-gfm').default;

        return (
          <div className="prose prose-slate dark:prose-invert max-w-none prose-sm">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 自定义代码块样式
                code: (props: any) => {
                  const { node, inline, className, children, ...rest } = props;
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <pre className="bg-slate-100 dark:bg-slate-800 rounded-md p-4 overflow-x-auto border border-slate-200 dark:border-slate-700">
                      <code className={className} {...rest}>
                        {children}
                      </code>
                    </pre>
                  ) : (
                    <code className="bg-slate-100 dark:bg-slate-800 px-1.5 py-0.5 rounded text-sm border border-slate-200 dark:border-slate-700" {...rest}>
                      {children}
                    </code>
                  );
                },
                // 自定义表格样式
                table: (props: any) => (
                  <div className="overflow-x-auto my-4">
                    <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700 border border-slate-200 dark:border-slate-700 rounded-lg">
                      {props.children}
                    </table>
                  </div>
                ),
                // 自定义链接样式
                a: (props: any) => (
                  <a
                    href={props.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-600 dark:text-primary-400 hover:underline font-medium"
                  >
                    {props.children}
                  </a>
                ),
                // 自定义引用块样式
                blockquote: (props: any) => (
                  <blockquote className="border-l-4 border-primary-500 pl-4 py-2 my-4 bg-slate-50 dark:bg-slate-800/50 rounded-r-md">
                    {props.children}
                  </blockquote>
                ),
                // 自定义列表样式
                ul: (props: any) => (
                  <ul className="list-disc list-inside space-y-1 my-2">
                    {props.children}
                  </ul>
                ),
                ol: (props: any) => (
                  <ol className="list-decimal list-inside space-y-1 my-2">
                    {props.children}
                  </ol>
                ),
              }}
            >
              {content}
            </ReactMarkdown>
          </div>
        );
      } catch (error) {
        console.error('Markdown渲染失败:', error);
        // 如果Markdown渲染失败，回退到纯文本显示
        return (
          <div className="text-slate-900 dark:text-white whitespace-pre-wrap break-words">
            {content}
          </div>
        );
      }
    } else {
      return (
        <div className="text-slate-900 dark:text-white whitespace-pre-wrap break-words font-mono text-sm bg-slate-50 dark:bg-slate-800/50 p-3 rounded-md border border-slate-200 dark:border-slate-700">
          {content}
        </div>
      );
    }
  };

  return (
    <div className={`max-w-6xl mx-auto p-6 ${className}`}>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700">
        <div className="p-6 border-b border-slate-200 dark:border-slate-700">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">消息查询</h1>
          
          {/* 查询表单 */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label htmlFor="conversationId" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  对话ID (conversation_id)
                </label>
                <input
                  id="conversationId"
                  type="text"
                  value={conversationId}
                  onChange={(e) => setConversationId(e.target.value)}
                  placeholder="请输入对话ID"
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div className="sm:w-48">
                <label htmlFor="messageType" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  消息类型 (可选)
                </label>
                <select
                  id="messageType"
                  value={messageType}
                  onChange={(e) => setMessageType(e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">全部类型</option>
                  <option value="human">用户消息</option>
                  <option value="ai">AI消息</option>
                  <option value="system">系统消息</option>
                  <option value="tool">工具消息</option>
                </select>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 items-center">
              <button
                onClick={handleQuery}
                disabled={loading}
                className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? '查询中...' : '查询消息'}
              </button>

              <button
                onClick={() => {
                  setConversationId('');
                  setMessageType('');
                  setResult(null);
                  setError('');
                }}
                className="px-6 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-800 transition-colors"
              >
                清空
              </button>

              <button
                onClick={() => {
                  setConversationId('demo-markdown-conversation');
                  setMessageType('');
                  setError('');
                  // 设置示例数据
                  setResult({
                    conversation: {
                      conversation_id: 'demo-markdown-conversation',
                      task_id: 'demo-task',
                      title: 'Markdown 渲染演示',
                      status: 'active',
                      created_at: new Date().toISOString(),
                      last_message_at: new Date().toISOString()
                    },
                    messages: [
                      {
                        id: 1,
                        message_id: 'demo-msg-1',
                        task_id: 'demo-task',
                        message_type: 'human',
                        content: '请展示一些 Markdown 格式的示例',
                        sequence_number: 1,
                        created_at: new Date()
                      },
                      {
                        id: 2,
                        message_id: 'demo-msg-2',
                        task_id: 'demo-task',
                        message_type: 'ai',
                        content: `# Markdown 渲染演示

这是一个 **Markdown** 渲染的演示消息。

## 代码块示例

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
}

greet('World');
\`\`\`

## 表格示例

| 功能 | 状态 | 描述 |
|------|------|------|
| 代码高亮 | ✅ | 支持多种编程语言 |
| 表格渲染 | ✅ | 响应式表格布局 |
| 链接处理 | ✅ | 自动在新窗口打开 |

## 列表示例

### 有序列表
1. 第一项
2. 第二项
3. 第三项

### 无序列表
- 项目 A
- 项目 B
- 项目 C

## 引用块

> 这是一个引用块的示例。
> 可以包含多行内容。

## 链接示例

访问 [GitHub](https://github.com) 查看更多信息。

## 内联代码

使用 \`console.log()\` 函数输出信息。`,
                        sequence_number: 2,
                        created_at: new Date()
                      }
                    ],
                    statistics: {
                      total: 2,
                      byType: { human: 1, ai: 1 }
                    },
                    pagination: {
                      total: 2,
                      limit: 100,
                      offset: 0,
                      hasMore: false
                    }
                  });
                }}
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-800 transition-colors"
              >
                Markdown 演示
              </button>

              <div className="flex items-center gap-2">
                <input
                  id="renderMarkdown"
                  type="checkbox"
                  checked={renderMarkdown}
                  onChange={(e) => setRenderMarkdown(e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 dark:border-slate-600 rounded"
                />
                <label htmlFor="renderMarkdown" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Markdown 渲染
                </label>
              </div>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800 dark:text-red-400">查询失败</h3>
                  <div className="mt-2 text-sm text-red-700 dark:text-red-300">{error}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 查询结果 */}
        {result && (
          <div className="p-6">
            {/* 对话信息 */}
            <div className="mb-6 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
              <h2 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">对话信息</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-slate-700 dark:text-slate-300">对话ID:</span>
                  <span className="ml-2 text-slate-900 dark:text-white font-mono">{result.conversation.conversation_id}</span>
                </div>
                <div>
                  <span className="font-medium text-slate-700 dark:text-slate-300">任务ID:</span>
                  <span className="ml-2 text-slate-900 dark:text-white font-mono">{result.conversation.task_id}</span>
                </div>
                <div>
                  <span className="font-medium text-slate-700 dark:text-slate-300">状态:</span>
                  <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                    result.conversation.status === 'active' ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' :
                    result.conversation.status === 'completed' ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400' :
                    'bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-300'
                  }`}>
                    {result.conversation.status}
                  </span>
                </div>
                {result.conversation.title && (
                  <div className="md:col-span-2 lg:col-span-3">
                    <span className="font-medium text-slate-700 dark:text-slate-300">标题:</span>
                    <span className="ml-2 text-slate-900 dark:text-white">{result.conversation.title}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-slate-700 dark:text-slate-300">创建时间:</span>
                  <span className="ml-2 text-slate-900 dark:text-white">{formatDate(result.conversation.created_at)}</span>
                </div>
                {result.conversation.last_message_at && (
                  <div>
                    <span className="font-medium text-slate-700 dark:text-slate-300">最后消息:</span>
                    <span className="ml-2 text-slate-900 dark:text-white">{formatDate(result.conversation.last_message_at)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* 统计信息 */}
            <div className="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
              <h2 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">消息统计</h2>
              <div className="flex flex-wrap gap-4">
                <div className="bg-white dark:bg-slate-800 px-3 py-2 rounded-md border border-slate-200 dark:border-slate-700">
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">总消息数:</span>
                  <span className="ml-2 text-lg font-bold text-primary-600 dark:text-primary-400">{result.statistics.total}</span>
                </div>
                {Object.entries(result.statistics.byType).map(([type, count]) => (
                  <div key={type} className="bg-white dark:bg-slate-800 px-3 py-2 rounded-md border border-slate-200 dark:border-slate-700">
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">{getMessageTypeLabel(type)}:</span>
                    <span className="ml-2 text-lg font-bold text-primary-600 dark:text-primary-400">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* 消息列表 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-slate-900 dark:text-white">消息列表</h2>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-slate-600 dark:text-slate-400">显示模式:</span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setRenderMarkdown(false)}
                      className={`px-3 py-1 text-xs rounded-md transition-colors ${
                        !renderMarkdown
                          ? 'bg-primary-600 text-white'
                          : 'bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600'
                      }`}
                    >
                      原始文本
                    </button>
                    <button
                      onClick={() => setRenderMarkdown(true)}
                      className={`px-3 py-1 text-xs rounded-md transition-colors ${
                        renderMarkdown
                          ? 'bg-primary-600 text-white'
                          : 'bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600'
                      }`}
                    >
                      Markdown
                    </button>
                  </div>
                </div>
              </div>
              {result.messages.length === 0 ? (
                <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                  没有找到消息
                </div>
              ) : (
                <div className="space-y-4">
                  {result.messages.map((message) => (
                    <div key={message.id} className="border border-slate-200 dark:border-slate-700 rounded-lg p-4 bg-white dark:bg-slate-800/50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMessageTypeColor(message.message_type)}`}>
                            {getMessageTypeLabel(message.message_type)}
                          </span>
                          <span className="text-sm text-slate-500 dark:text-slate-400">#{message.sequence_number}</span>
                          <span className="text-sm text-slate-500 dark:text-slate-400 font-mono">{message.message_id}</span>
                        </div>
                        <span className="text-sm text-slate-500 dark:text-slate-400">{formatDate(message.created_at.toString())}</span>
                      </div>

                      <div className="mb-3">
                        <MessageContent content={message.content} renderAsMarkdown={renderMarkdown} />
                      </div>
                      
                      {message.metadata && (
                        <details className="mt-2">
                          <summary className="text-sm text-slate-600 dark:text-slate-400 cursor-pointer hover:text-slate-800 dark:hover:text-slate-200">
                            查看元数据
                          </summary>
                          <pre className="mt-2 p-2 bg-slate-100 dark:bg-slate-700 rounded text-xs overflow-x-auto text-slate-800 dark:text-slate-200">
                            {JSON.stringify(message.metadata, null, 2)}
                          </pre>
                        </details>
                      )}

                      {message.parent_message_id && (
                        <div className="mt-2 text-xs text-slate-500 dark:text-slate-400">
                          回复消息: {message.parent_message_id}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageQuery;
