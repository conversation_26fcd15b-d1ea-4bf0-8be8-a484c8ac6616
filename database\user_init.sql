-- 用户认证系统数据库初始化
-- 创建时间: 2025-07-19
-- 功能: 用户注册、登录、会话管理

USE trading_analysis;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL COMMENT '用户邮箱，唯一标识',
    username VARCHAR(100) NOT NULL COMMENT '用户显示名称',
    password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    verification_token VARCHAR(255) COMMENT '邮箱验证token',
    reset_token VARCHAR(255) COMMENT '密码重置token',
    reset_token_expires TIMESTAMP NULL COMMENT '重置token过期时间',
    avatar_url VARCHAR(500) COMMENT '用户头像URL',
    role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '账户是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_verification_token (verification_token),
    INDEX idx_reset_token (reset_token),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) UNIQUE NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    token VARCHAR(500) NOT NULL COMMENT 'JWT token',
    refresh_token VARCHAR(500) NOT NULL COMMENT '刷新token',
    expires_at TIMESTAMP NOT NULL COMMENT 'token过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新token过期时间',
    ip_address VARCHAR(45) COMMENT '登录IP地址',
    user_agent TEXT COMMENT '用户代理',
    is_active BOOLEAN DEFAULT TRUE COMMENT '会话是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 3. 用户活动日志表
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    activity_type ENUM('login', 'logout', 'register', 'password_change', 'email_verification', 'password_reset') NOT NULL COMMENT '活动类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    details JSON COMMENT '活动详情',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- 4. 创建用户视图
CREATE OR REPLACE VIEW user_profile AS
SELECT 
    u.id,
    u.email,
    u.username,
    u.email_verified,
    u.avatar_url,
    u.role,
    u.is_active,
    u.last_login_at,
    u.created_at,
    COUNT(s.id) as active_sessions
FROM users u
LEFT JOIN user_sessions s ON u.id = s.user_id AND s.is_active = TRUE
WHERE u.is_active = TRUE
GROUP BY u.id;

-- 5. 创建存储过程 - 用户注册
DROP PROCEDURE IF EXISTS RegisterUser;
DELIMITER //
CREATE PROCEDURE RegisterUser(
    IN p_email VARCHAR(255),
    IN p_username VARCHAR(100),
    IN p_password_hash VARCHAR(255),
    IN p_verification_token VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 检查邮箱是否已存在
    IF EXISTS (SELECT 1 FROM users WHERE email = p_email) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Email already exists';
    END IF;
    
    -- 插入新用户
    INSERT INTO users (email, username, password_hash, verification_token)
    VALUES (p_email, p_username, p_password_hash, p_verification_token);
    
    -- 记录注册活动
    INSERT INTO user_activity_logs (user_id, activity_type, details)
    VALUES (LAST_INSERT_ID(), 'register', JSON_OBJECT('email', p_email, 'username', p_username));
    
    COMMIT;
END //
DELIMITER ;

-- 6. 创建存储过程 - 用户登录
DROP PROCEDURE IF EXISTS UserLogin;
DELIMITER //
CREATE PROCEDURE UserLogin(
    IN p_email VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    DECLARE user_id_val BIGINT;
    
    -- 获取用户ID
    SELECT id INTO user_id_val 
    FROM users 
    WHERE email = p_email AND is_active = TRUE;
    
    IF user_id_val IS NOT NULL THEN
        -- 更新最后登录时间
        UPDATE users 
        SET last_login_at = NOW() 
        WHERE id = user_id_val;
        
        -- 记录登录活动
        INSERT INTO user_activity_logs (user_id, activity_type, ip_address, user_agent)
        VALUES (user_id_val, 'login', p_ip_address, p_user_agent);
    END IF;
END //
DELIMITER ;

-- 7. 插入测试用户 (密码: Test@123456)
INSERT INTO users (email, username, password_hash, email_verified, role) VALUES
('<EMAIL>', '测试用户', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'user'),
('<EMAIL>', '管理员', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'admin');

-- 显示创建完成信息
SELECT 'User authentication system initialized successfully!' as status,
       'Tables: users, user_sessions, user_activity_logs' as tables_created,
       'Views: user_profile' as views_created,
       'Procedures: RegisterUser, UserLogin' as procedures_created,
       'Test users: <EMAIL>, <EMAIL> (password: Test@123456)' as test_data;
