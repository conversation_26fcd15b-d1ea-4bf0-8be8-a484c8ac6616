'use client';

import { useEffect, useRef } from 'react';

// K线图组件
export function KLineChart({ className = "", style = {} }: { className?: string, style?: React.CSSProperties }) {
  return (
    <div className={`absolute opacity-10 ${className}`} style={style}>
      <svg width="200" height="120" viewBox="0 0 200 120" className="text-green-400">
        {/* K线数据 */}
        {[
          { x: 20, high: 20, low: 40, open: 35, close: 25 },
          { x: 35, high: 25, low: 45, open: 25, close: 40 },
          { x: 50, high: 15, low: 35, open: 40, close: 20 },
          { x: 65, high: 20, low: 50, open: 20, close: 45 },
          { x: 80, high: 10, low: 30, open: 45, close: 15 },
          { x: 95, high: 15, low: 40, open: 15, close: 35 },
          { x: 110, high: 25, low: 55, open: 35, close: 50 },
          { x: 125, high: 20, low: 45, open: 50, close: 25 },
          { x: 140, high: 15, low: 35, open: 25, close: 30 },
          { x: 155, high: 30, low: 60, open: 30, close: 55 },
          { x: 170, high: 25, low: 50, open: 55, close: 30 },
        ].map((candle, index) => {
          const isGreen = candle.close > candle.open;
          return (
            <g key={index}>
              {/* 影线 */}
              <line
                x1={candle.x}
                y1={candle.high}
                x2={candle.x}
                y2={candle.low}
                stroke="currentColor"
                strokeWidth="1"
                opacity="0.6"
              />
              {/* 实体 */}
              <rect
                x={candle.x - 4}
                y={Math.min(candle.open, candle.close)}
                width="8"
                height={Math.abs(candle.close - candle.open)}
                fill={isGreen ? "currentColor" : "none"}
                stroke="currentColor"
                strokeWidth="1"
                opacity="0.8"
                className="kline-candle"
              />
            </g>
          );
        })}
      </svg>
    </div>
  );
}

// 股价曲线组件
export function StockCurve({ className = "", style = {} }: { className?: string, style?: React.CSSProperties }) {
  const pathRef = useRef<SVGPathElement>(null);

  useEffect(() => {
    if (pathRef.current) {
      const path = pathRef.current;
      const length = path.getTotalLength();
      path.style.strokeDasharray = `${length}`;
      path.style.strokeDashoffset = `${length}`;
      
      // 动画绘制曲线
      path.animate([
        { strokeDashoffset: length },
        { strokeDashoffset: 0 }
      ], {
        duration: 3000,
        easing: 'ease-in-out',
        iterations: Infinity,
        direction: 'alternate'
      });
    }
  }, []);

  return (
    <div className={`absolute opacity-15 ${className}`} style={style}>
      <svg width="300" height="100" viewBox="0 0 300 100" className="text-blue-400">
        <defs>
          <linearGradient id="stockGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.3"/>
            <stop offset="100%" stopColor="currentColor" stopOpacity="0"/>
          </linearGradient>
        </defs>
        
        {/* 股价曲线 */}
        <path
          ref={pathRef}
          d="M10,80 Q50,60 80,45 T150,35 Q200,25 250,40 T290,30"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          opacity="0.8"
          className="stock-curve"
        />
        
        {/* 填充区域 */}
        <path
          d="M10,80 Q50,60 80,45 T150,35 Q200,25 250,40 T290,30 L290,90 L10,90 Z"
          fill="url(#stockGradient)"
        />
        
        {/* 网格线 */}
        {[20, 40, 60, 80].map(y => (
          <line
            key={y}
            x1="10"
            y1={y}
            x2="290"
            y2={y}
            stroke="currentColor"
            strokeWidth="0.5"
            opacity="0.3"
            strokeDasharray="2,2"
          />
        ))}
      </svg>
    </div>
  );
}

// 金融图标组件
export function FinancialIcons({ className = "" }: { className?: string }) {
  return (
    <div className={`absolute opacity-8 ${className}`}>
      <div className="flex flex-col space-y-4 text-purple-400">
        {/* 趋势向上图标 */}
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="financial-icon animate-stock-rise">
          <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
        </svg>

        {/* 美元符号 */}
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="financial-icon animate-stock-rise" style={{animationDelay: '0.5s'}}>
          <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
        </svg>

        {/* 饼图图标 */}
        <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor" className="financial-icon animate-stock-rise" style={{animationDelay: '1s'}}>
          <path d="M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z"/>
        </svg>
      </div>
    </div>
  );
}

// 数字滚动组件
export function ScrollingNumbers({ className = "" }: { className?: string }) {
  const numbers = ['1234.56', '2345.67', '3456.78', '4567.89', '5678.90'];
  
  return (
    <div className={`absolute opacity-10 overflow-hidden ${className}`}>
      <div className="text-cyan-400 text-sm font-mono">
        {numbers.map((num, index) => (
          <div
            key={index}
            className="animate-number-scroll"
            style={{
              animationDelay: `${index * 0.5}s`,
              animationDuration: '4s'
            }}
          >
            ${num}
          </div>
        ))}
      </div>
    </div>
  );
}

// 股票代码滚动
export function StockTickers({ className = "" }: { className?: string }) {
  const tickers = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA'];
  
  return (
    <div className={`absolute opacity-12 ${className}`}>
      <div className="flex flex-col space-y-2 text-xs font-bold text-green-400">
        {tickers.map((ticker, index) => (
          <div
            key={ticker}
            className="animate-ticker-flash"
            style={{
              animationDelay: `${index * 0.3}s`,
              animationDuration: '2s'
            }}
          >
            {ticker}
          </div>
        ))}
      </div>
    </div>
  );
}

// 主要背景组件
export function StockBackgroundElements() {
  return (
    <>
      {/* K线图 */}
      <KLineChart className="top-10 left-10 animate-float" />
      <KLineChart className="bottom-20 right-20 animate-float" style={{animationDelay: '2s'}} />
      
      {/* 股价曲线 */}
      <StockCurve className="top-1/3 right-10 animate-glow" />
      <StockCurve className="bottom-1/3 left-5 animate-glow" style={{animationDelay: '1s'}} />
      
      {/* 金融图标 */}
      <FinancialIcons className="top-1/4 right-1/4" />
      <FinancialIcons className="bottom-1/4 left-1/4" />
      
      {/* 滚动数字 */}
      <ScrollingNumbers className="top-20 right-32" />
      <ScrollingNumbers className="bottom-32 left-20" />
      
      {/* 股票代码 */}
      <StockTickers className="top-1/2 left-10" />
      <StockTickers className="bottom-10 right-40" />
    </>
  );
}
