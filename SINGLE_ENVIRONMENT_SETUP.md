# 单环境部署配置指南

## 🎯 简化配置概览

您的部署配置已简化为单一环境，支持从 `main` 和 `develop` 分支自动部署到同一个生产环境。

## 🔐 需要配置的 GitHub Secrets

### 1. 服务器URL配置（3个）
```
DEPLOY_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com
WS_URL=wss://ws.yourdomain.com
```

### 2. 服务器连接信息（4个）
```
DEPLOY_SSH_KEY=你的SSH私钥内容
DEPLOY_HOST=你的服务器IP地址
DEPLOY_USER=你的服务器用户名
DEPLOY_PATH=/opt/trading-agents
```

### 3. 数据库配置（2个）
```
MYSQL_ROOT_PASSWORD=你的数据库root密码
MYSQL_PASSWORD=你的数据库用户密码
```

### 4. 阿里云镜像服务（2个，已配置）
```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 📝 配置示例

### 示例1：使用域名
```bash
DEPLOY_URL=https://mytrading.com
API_URL=https://api.mytrading.com
WS_URL=wss://ws.mytrading.com
DEPLOY_HOST=*************
DEPLOY_USER=ubuntu
DEPLOY_PATH=/home/<USER>/trading-agents
```

### 示例2：使用IP和端口
```bash
DEPLOY_URL=http://*************:3000
API_URL=http://*************:5000
WS_URL=ws://*************:8000
DEPLOY_HOST=*************
DEPLOY_USER=root
DEPLOY_PATH=/opt/trading-agents
```

## 🚀 部署流程

### 自动触发条件
- **推送到 `main` 分支** → 自动部署
- **推送到 `develop` 分支** → 自动部署
- **手动触发** → 通过 GitHub Actions 页面

### 部署步骤
1. **代码检查** → ESLint、TypeScript检查
2. **安全扫描** → 依赖和文件扫描
3. **构建镜像** → Docker镜像构建
4. **推送镜像** → 推送到阿里云
5. **部署应用** → 自动部署到服务器
6. **健康检查** → 验证部署成功

## 🔧 快速配置步骤

### 步骤1：生成SSH密钥
```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "trading-deploy" -f ~/.ssh/trading_deploy

# 将公钥添加到服务器
ssh-copy-id -i ~/.ssh/trading_deploy.pub user@your-server-ip

# 复制私钥内容到GitHub Secrets
cat ~/.ssh/trading_deploy
```

### 步骤2：配置GitHub Secrets
1. 进入GitHub仓库 `Settings` > `Secrets and variables` > `Actions`
2. 点击 `New repository secret`
3. 逐个添加上述11个Secrets

### 步骤3：测试部署
```bash
# 推送到develop分支测试
git checkout develop
git add .
git commit -m "test: 测试单环境部署"
git push origin develop

# 或推送到main分支
git checkout main
git merge develop
git push origin main
```

## 📊 配置对比

### 之前（双环境）
- 需要配置12个URL相关的Secrets
- 需要配置4个数据库密码
- 复杂的环境区分逻辑

### 现在（单环境）
- 只需配置3个URL相关的Secrets
- 只需配置2个数据库密码
- 简化的部署逻辑

## 🛠️ 服务器准备

### 1. 安装Docker
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install docker.io docker-compose

# 启动Docker
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

### 2. 创建应用目录
```bash
sudo mkdir -p /opt/trading-agents
sudo chown $USER:$USER /opt/trading-agents
```

### 3. 配置防火墙
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3000  # 应用端口
sudo ufw enable
```

## 🔍 验证配置

### 1. 检查SSH连接
```bash
ssh -i ~/.ssh/trading_deploy user@your-server-ip "echo 'SSH连接成功'"
```

### 2. 检查Docker
```bash
ssh -i ~/.ssh/trading_deploy user@your-server-ip "docker --version"
```

### 3. 检查GitHub Secrets
在GitHub Actions页面查看工作流运行日志，确认所有Secrets都能正确加载。

## 📋 完整的Secrets清单

复制以下内容，替换实际值后添加到GitHub Secrets：

```
# 服务器URL（替换为您的实际域名或IP）
DEPLOY_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com
WS_URL=wss://ws.yourdomain.com

# 服务器连接信息（替换为您的实际服务器信息）
DEPLOY_SSH_KEY=-----BEGIN RSA PRIVATE KEY-----
...您的SSH私钥内容...
-----END RSA PRIVATE KEY-----
DEPLOY_HOST=*************
DEPLOY_USER=ubuntu
DEPLOY_PATH=/opt/trading-agents

# 数据库配置（替换为您的实际密码）
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_user_password

# 阿里云镜像服务（已配置，无需修改）
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 🆘 故障排除

### 常见问题
1. **SSH连接失败** → 检查密钥格式和服务器配置
2. **域名无法访问** → 检查DNS解析和防火墙
3. **Docker权限问题** → 确认用户在docker组中
4. **健康检查失败** → 检查应用端口和服务状态

### 获取帮助
- 查看GitHub Actions运行日志
- 检查服务器Docker容器状态
- 验证网络连接和端口开放
- 联系项目维护者

---

**🎉 配置完成后，您只需要推送代码到 `main` 或 `develop` 分支，系统就会自动构建、推送镜像并部署到您的服务器！**
