// 会话管理工具
export class SessionManager {
  private static readonly SESSION_KEY = 'sessionId';
  private static readonly USER_KEY = 'currentUser';

  // 存储 sessionId
  static setSession(sessionId: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.SESSION_KEY, sessionId);
    }
  }

  // 获取 sessionId
  static getSession(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.SESSION_KEY);
    }
    return null;
  }

  // 清除 session
  static clearSession(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.SESSION_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  // 存储用户信息
  static setUser(user: any): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  // 获取用户信息
  static getUser(): any | null {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem(this.USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    }
    return null;
  }

  // 检查是否已登录
  static isAuthenticated(): boolean {
    return this.getSession() !== null;
  }

  // 获取认证头
  static getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};
    const sessionId = this.getSession();

    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    return headers;
  }
}

// 全局 session 实例
export const sessionManager = SessionManager;
