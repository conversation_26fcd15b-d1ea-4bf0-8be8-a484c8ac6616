/**
 * 研究团队智能体测试
 * 测试多头研究员、空头研究员、辩论主持人和共识评估节点的功能
 */

describe('研究团队智能体测试', () => {
  // 模拟状态数据
  const mockState = {
    ticker: '600519',
    date: '2025-01-24',
    config: {
      deepThinkLLM: 'gpt-4o',
      researchDepth: 'standard',
      maxDebateRounds: 3,
    },
    analysis: {
      fundamental: {
        summary: '基本面分析显示公司财务健康，估值合理',
        investmentRating: '推荐',
        keyMetrics: { pe: 25.5, pb: 3.2, roe: 0.18 },
        confidence: 0.8,
      },
      technical: {
        summary: '技术分析显示上升趋势，突破关键阻力位',
        tradingSignal: '买入',
        technicalSignals: { trend: '上升', rsi: '正常', macd: '金叉' },
        confidence: 0.75,
      },
      sentiment: {
        summary: '市场情绪积极，投资者信心较强',
        overallSentiment: 'positive',
        confidence: 0.7,
      },
      news: {
        summary: '近期新闻偏正面，业绩预期良好',
        sentiment: 0.65,
        confidence: 0.8,
      },
    },
    research: {
      debateRounds: [],
    },
    messages: [],
    progress: 60,
  };

  describe('研究团队数据结构测试', () => {
    test('应该有正确的状态结构', () => {
      expect(mockState.ticker).toBe('600519');
      expect(mockState.analysis).toBeDefined();
      expect(mockState.analysis.fundamental).toBeDefined();
      expect(mockState.analysis.technical).toBeDefined();
      expect(mockState.analysis.sentiment).toBeDefined();
      expect(mockState.analysis.news).toBeDefined();
      expect(mockState.research).toBeDefined();
    });

    test('分析数据应该包含必要的字段', () => {
      const { fundamental, technical, sentiment, news } = mockState.analysis;

      expect(fundamental.summary).toBeDefined();
      expect(fundamental.investmentRating).toBeDefined();
      expect(fundamental.confidence).toBeGreaterThan(0);

      expect(technical.summary).toBeDefined();
      expect(technical.tradingSignal).toBeDefined();
      expect(technical.confidence).toBeGreaterThan(0);

      expect(sentiment.summary).toBeDefined();
      expect(sentiment.overallSentiment).toBeDefined();

      expect(news.summary).toBeDefined();
      expect(news.sentiment).toBeGreaterThan(0);
    });
  });

  describe('多头研究员逻辑测试', () => {
    test('应该能够识别多头信号', () => {
      const { fundamental, technical, sentiment } = mockState.analysis;

      // 检查多头信号
      const bullishSignals = [];

      if (fundamental.investmentRating === '推荐' || fundamental.investmentRating === '强烈推荐') {
        bullishSignals.push('fundamental_bullish');
      }

      if (technical.tradingSignal === '买入') {
        bullishSignals.push('technical_bullish');
      }

      if (sentiment.overallSentiment === 'positive') {
        bullishSignals.push('sentiment_bullish');
      }

      expect(bullishSignals.length).toBeGreaterThan(0);
      expect(bullishSignals).toContain('fundamental_bullish');
      expect(bullishSignals).toContain('technical_bullish');
      expect(bullishSignals).toContain('sentiment_bullish');
    });

    test('应该能够计算多头置信度', () => {
      const { fundamental, technical, sentiment, news } = mockState.analysis;

      let confidence = 0.5; // 基础置信度

      if (fundamental.investmentRating === '推荐') confidence += 0.15;
      if (technical.tradingSignal === '买入') confidence += 0.15;
      if (sentiment.overallSentiment === 'positive') confidence += 0.1;
      if (news.sentiment > 0.6) confidence += 0.1;

      expect(confidence).toBeGreaterThan(0.5);
      expect(confidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('空头研究员逻辑测试', () => {
    test('应该能够识别空头信号', () => {
      // 创建一个空头倾向的状态
      const bearishState = {
        ...mockState,
        analysis: {
          ...mockState.analysis,
          fundamental: {
            ...mockState.analysis.fundamental,
            investmentRating: '减持',
          },
          technical: {
            ...mockState.analysis.technical,
            tradingSignal: '卖出',
          },
          sentiment: {
            ...mockState.analysis.sentiment,
            overallSentiment: 'negative',
          },
        },
      };

      const { fundamental, technical, sentiment } = bearishState.analysis;

      const bearishSignals = [];

      if (fundamental.investmentRating === '减持' || fundamental.investmentRating === '卖出') {
        bearishSignals.push('fundamental_bearish');
      }

      if (technical.tradingSignal === '卖出') {
        bearishSignals.push('technical_bearish');
      }

      if (sentiment.overallSentiment === 'negative') {
        bearishSignals.push('sentiment_bearish');
      }

      expect(bearishSignals.length).toBeGreaterThan(0);
    });
  });

  describe('辩论主持人逻辑测试', () => {
    test('应该能够识别辩论焦点', () => {
      const bullArguments = ['估值合理', '业绩增长', '行业前景良好'];
      const bearArguments = ['估值过高', '竞争加剧', '政策风险'];

      // 模拟焦点识别逻辑
      const focusPoints = [];

      // 查找对立观点
      if (
        bullArguments.some((arg) => arg.includes('估值')) &&
        bearArguments.some((arg) => arg.includes('估值'))
      ) {
        focusPoints.push('估值争议');
      }

      if (
        bullArguments.some((arg) => arg.includes('增长')) &&
        bearArguments.some((arg) => arg.includes('竞争'))
      ) {
        focusPoints.push('增长vs竞争');
      }

      expect(focusPoints.length).toBeGreaterThan(0);
      expect(focusPoints).toContain('估值争议');
    });

    test('应该能够评估辩论质量', () => {
      const bullConfidence = 0.8;
      const bearConfidence = 0.7;
      const focusPointsCount = 3;

      let quality = 0.5;
      quality += bullConfidence * 0.2;
      quality += bearConfidence * 0.2;
      quality += Math.min(focusPointsCount * 0.05, 0.2);

      expect(quality).toBeGreaterThan(0.5);
      expect(quality).toBeLessThanOrEqual(1.0);
    });
  });

  describe('共识评估师逻辑测试', () => {
    test('应该能够计算概率权重', () => {
      const bullConfidence = 0.8;
      const bearConfidence = 0.7;

      // 简化的概率计算
      const totalConfidence = bullConfidence + bearConfidence;
      const bullishWeight = bullConfidence / totalConfidence;
      const bearishWeight = bearConfidence / totalConfidence;
      const neutralWeight = 1 - bullishWeight - bearishWeight;

      expect(bullishWeight).toBeGreaterThan(0);
      expect(bearishWeight).toBeGreaterThan(0);
      expect(bullishWeight + bearishWeight + neutralWeight).toBeCloseTo(1, 2);
    });

    test('应该能够生成投资建议', () => {
      const bullConfidence = 0.8;
      const bearConfidence = 0.6;

      let recommendation = '观望';

      if (bullConfidence > bearConfidence + 0.1) {
        recommendation = '买入';
      } else if (bearConfidence > bullConfidence + 0.1) {
        recommendation = '卖出';
      } else {
        recommendation = '持有';
      }

      expect(recommendation).toBe('买入');
    });
  });

  describe('工作流集成测试', () => {
    test('应该按正确顺序执行研究流程', () => {
      const workflow = [
        'analyst_team_completed',
        'bull_research_started',
        'bear_research_started',
        'debate_moderation',
        'consensus_evaluation',
      ];

      expect(workflow).toHaveLength(5);
      expect(workflow[0]).toBe('analyst_team_completed');
      expect(workflow[workflow.length - 1]).toBe('consensus_evaluation');
    });

    test('应该正确更新进度', () => {
      let progress = 60; // 分析师团队完成后的进度

      progress += 15; // 多头研究完成
      expect(progress).toBe(75);

      progress += 15; // 空头研究完成
      expect(progress).toBe(90);

      progress += 10; // 辩论主持完成
      expect(progress).toBe(100);

      expect(progress).toBeLessThanOrEqual(100);
    });
  });

  describe('错误处理测试', () => {
    test('应该检测缺失的分析数据', () => {
      const incompleteState = {
        ...mockState,
        analysis: {},
      };

      const hasRequiredAnalysis =
        incompleteState.analysis.fundamental &&
        incompleteState.analysis.technical &&
        incompleteState.analysis.sentiment &&
        incompleteState.analysis.news;

      expect(hasRequiredAnalysis).toBeFalsy();
    });

    test('应该检测辩论完成状态', () => {
      const researchState = {
        bull: { confidence: 0.8 },
        bear: { confidence: 0.7 },
        debateRounds: [{ round: 1 }, { round: 2 }],
        debateCompleted: true,
      };

      const canEvaluateConsensus =
        researchState.bull && researchState.bear && researchState.debateCompleted;

      expect(canEvaluateConsensus).toBeTruthy();
    });
  });
});

console.log('研究团队智能体测试模块已加载');
