{"mcpServers": {"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}}, "mysql": {"command": "node", "args": ["d:/product/mysql-mcp/dist/index.js"], "env": {"MYSQL_HOST": "127.0.0.1", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASS": "root", "MYSQL_DB": "report_db"}, "disabled": false}, "mcp-deepwiki": {"type": "stdio", "command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}}}