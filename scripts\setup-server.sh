#!/bin/bash

# 服务器部署配置脚本
# 用于生成SSH密钥和配置服务器连接

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                服务器部署配置助手                             ║
║              TradingAgents Frontend                          ║
╚══════════════════════════════════════════════════════════════╝

此脚本将帮助您：
✓ 生成SSH密钥对
✓ 配置服务器连接
✓ 生成GitHub Secrets配置
✓ 测试服务器连接

EOF
}

# 收集服务器信息
collect_server_info() {
    log_info "请输入您的服务器信息："

    read -p "服务器IP地址: " SERVER_IP
    read -p "SSH用户名 (默认: root): " SSH_USER
    SSH_USER=${SSH_USER:-root}

    read -p "SSH端口 (默认: 22): " SSH_PORT
    SSH_PORT=${SSH_PORT:-22}

    read -p "部署路径 (默认: /opt/trading-agents): " DEPLOY_PATH
    DEPLOY_PATH=${DEPLOY_PATH:-/opt/trading-agents}

    echo ""
    log_info "域名配置（可选，如果没有域名可以直接回车）："
    read -p "主域名 (如: yourdomain.com): " MAIN_DOMAIN

    if [[ -n "$MAIN_DOMAIN" ]]; then
        STAGING_DOMAIN="staging.$MAIN_DOMAIN"
        API_DOMAIN="api.$MAIN_DOMAIN"
        API_STAGING_DOMAIN="api-staging.$MAIN_DOMAIN"
        WS_DOMAIN="ws.$MAIN_DOMAIN"
        WS_STAGING_DOMAIN="ws-staging.$MAIN_DOMAIN"
    else
        log_warning "未配置域名，将使用IP地址和端口"
        STAGING_DOMAIN="$SERVER_IP:3001"
        API_DOMAIN="$SERVER_IP:5000"
        API_STAGING_DOMAIN="$SERVER_IP:5001"
        WS_DOMAIN="$SERVER_IP:8000"
        WS_STAGING_DOMAIN="$SERVER_IP:8001"
    fi
}

# 生成SSH密钥
generate_ssh_key() {
    log_info "生成SSH密钥对..."

    SSH_KEY_PATH="$HOME/.ssh/trading_agents_deploy"

    if [[ -f "$SSH_KEY_PATH" ]]; then
        read -p "SSH密钥已存在，是否重新生成? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "使用现有SSH密钥"
            return
        fi
    fi

    ssh-keygen -t rsa -b 4096 -C "trading-agents-deploy" -f "$SSH_KEY_PATH" -N ""

    if [[ $? -eq 0 ]]; then
        log_success "SSH密钥生成成功: $SSH_KEY_PATH"
    else
        log_error "SSH密钥生成失败"
        exit 1
    fi
}

# 配置服务器SSH访问
setup_server_ssh() {
    log_info "配置服务器SSH访问..."

    SSH_KEY_PATH="$HOME/.ssh/trading_agents_deploy"

    if [[ ! -f "$SSH_KEY_PATH.pub" ]]; then
        log_error "公钥文件不存在: $SSH_KEY_PATH.pub"
        exit 1
    fi

    log_info "将公钥复制到服务器..."
    ssh-copy-id -i "$SSH_KEY_PATH.pub" -p "$SSH_PORT" "$SSH_USER@$SERVER_IP"

    if [[ $? -eq 0 ]]; then
        log_success "SSH公钥配置成功"
    else
        log_error "SSH公钥配置失败"
        exit 1
    fi
}

# 测试SSH连接
test_ssh_connection() {
    log_info "测试SSH连接..."

    SSH_KEY_PATH="$HOME/.ssh/trading_agents_deploy"

    ssh -i "$SSH_KEY_PATH" -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "echo 'SSH连接测试成功'"

    if [[ $? -eq 0 ]]; then
        log_success "SSH连接测试通过"
    else
        log_error "SSH连接测试失败"
        exit 1
    fi
}

# 生成GitHub Secrets配置
generate_github_secrets() {
    log_info "生成GitHub Secrets配置..."

    SSH_KEY_PATH="$HOME/.ssh/trading_agents_deploy"

    cat > github_secrets.txt << EOF
# GitHub Secrets 配置
# 请将以下内容添加到 GitHub 仓库的 Secrets 中

# 服务器连接信息
DEPLOY_SSH_KEY=$(cat "$SSH_KEY_PATH")
DEPLOY_HOST=$SERVER_IP
DEPLOY_USER=$SSH_USER
DEPLOY_PORT=$SSH_PORT
DEPLOY_PATH=$DEPLOY_PATH

# URL配置
EOF

    if [[ -n "$MAIN_DOMAIN" ]]; then
        cat >> github_secrets.txt << EOF
STAGING_URL=https://$STAGING_DOMAIN
PRODUCTION_URL=https://$MAIN_DOMAIN
STAGING_API_URL=https://$API_STAGING_DOMAIN
PRODUCTION_API_URL=https://$API_DOMAIN
STAGING_WS_URL=wss://$WS_STAGING_DOMAIN
PRODUCTION_WS_URL=wss://$WS_DOMAIN
EOF
    else
        cat >> github_secrets.txt << EOF
STAGING_URL=http://$STAGING_DOMAIN
PRODUCTION_URL=http://$SERVER_IP:3000
STAGING_API_URL=http://$API_STAGING_DOMAIN
PRODUCTION_API_URL=http://$API_DOMAIN
STAGING_WS_URL=ws://$WS_STAGING_DOMAIN
PRODUCTION_WS_URL=ws://$WS_DOMAIN
EOF
    fi

    cat >> github_secrets.txt << EOF

# 数据库配置（请替换为实际密码）
MYSQL_ROOT_PASSWORD_STAGING=your_staging_root_password
MYSQL_PASSWORD_STAGING=your_staging_user_password
MYSQL_ROOT_PASSWORD_PROD=your_production_root_password
MYSQL_PASSWORD_PROD=your_production_user_password

# 阿里云镜像服务（已配置）
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
EOF

    log_success "GitHub Secrets配置已生成: github_secrets.txt"
}

# 主函数
main() {
    show_welcome

    # 收集服务器信息
    collect_server_info

    # 生成SSH密钥
    generate_ssh_key

    # 配置服务器SSH访问
    setup_server_ssh

    # 测试SSH连接
    test_ssh_connection

    # 生成GitHub Secrets配置
    generate_github_secrets

    log_success "服务器配置完成！"

    cat << EOF

📋 下一步操作：

1. 查看生成的配置文件：
   cat github_secrets.txt

2. 将配置添加到GitHub Secrets：
   - 进入GitHub仓库 Settings > Secrets and variables > Actions
   - 逐个添加github_secrets.txt中的配置

3. 推送代码测试部署：
   git push origin develop  # 测试预发布环境
   git push origin main     # 测试生产环境

4. 查看详细配置指南：
   cat SERVER_SECRETS_SETUP.md

EOF
}

# 运行主函数
main "$@"