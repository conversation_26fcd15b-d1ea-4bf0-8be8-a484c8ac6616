import MessageQuery from '@/components/database/MessageQuery';

export default function MessagesPage() {
  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-slate-800 shadow-sm border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-slate-900 dark:text-white">数据库消息查询</h1>
                <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">
                  根据对话ID查询数据库中的消息记录
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <a
                  href="/database"
                  className="inline-flex items-center px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm text-sm font-medium text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-700 hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-slate-800"
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  返回数据库管理
                </a>
                <a
                  href="/api/database/messages"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-slate-800"
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  API 文档
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="py-8">
        <MessageQuery />
      </div>

      {/* 页面底部信息 */}
      <div className="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-4">API 端点</h3>
              <div className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">GET /api/database/messages</code>
                  <p className="mt-1">查询所有消息（支持过滤）</p>
                </div>
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">GET /api/database/conversations/[id]/messages</code>
                  <p className="mt-1">查询特定对话的消息</p>
                </div>
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">POST /api/database/messages</code>
                  <p className="mt-1">添加新消息</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">查询参数</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">conversation_id</code>
                  <p className="mt-1">对话唯一标识符</p>
                </div>
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">message_type</code>
                  <p className="mt-1">消息类型：human, ai, system, tool</p>
                </div>
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">limit</code>
                  <p className="mt-1">返回结果数量限制（默认50）</p>
                </div>
                <div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">offset</code>
                  <p className="mt-1">分页偏移量（默认0）</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-4">消息类型</h3>
              <div className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                  <span className="font-medium">human</span> - 用户消息
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  <span className="font-medium">ai</span> - AI 回复消息
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 bg-gray-500 rounded-full mr-2"></span>
                  <span className="font-medium">system</span> - 系统消息
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
                  <span className="font-medium">tool</span> - 工具调用消息
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-4">Markdown 支持</h3>
              <div className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <p>消息内容支持 Markdown 格式渲染，包括：</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>代码块</strong> - 语法高亮显示</li>
                  <li><strong>表格</strong> - 响应式表格布局</li>
                  <li><strong>链接</strong> - 自动在新窗口打开</li>
                  <li><strong>引用块</strong> - 带边框的引用样式</li>
                  <li><strong>列表</strong> - 有序和无序列表</li>
                  <li><strong>文本格式</strong> - 粗体、斜体、删除线等</li>
                </ul>
                <p className="mt-3">
                  使用消息列表右上角的切换按钮在 "原始文本" 和 "Markdown" 显示模式之间切换。
                </p>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="text-center text-sm text-gray-500">
              <p>
                这个页面允许您查询数据库中的消息记录。输入对话ID来查看该对话的所有消息，
                或使用过滤器来查找特定类型的消息。
              </p>
              <p className="mt-2">
                消息按序号和创建时间排序，支持查看消息内容、元数据和统计信息。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export const metadata = {
  title: '消息查询 - 数据库管理',
  description: '查询数据库中的消息记录，支持根据对话ID和消息类型进行过滤',
};
