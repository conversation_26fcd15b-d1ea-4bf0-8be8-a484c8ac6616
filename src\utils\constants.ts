// 应用常量
export const APP_CONFIG = {
  name: 'TradingAgents',
  version: '1.0.0',
  description: '多智能体大语言模型金融交易框架',
  author: 'Tauric Research',
  repository: 'https://github.com/TauricResearch/TradingAgents',
  website: 'https://tauric.ai',
} as const;

// API 配置
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
  wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// 分析师配置
export const ANALYST_CONFIG = {
  market: {
    id: 'market',
    name: '市场分析师',
    description: '分析技术指标和价格走势',
    icon: 'ChartBarIcon',
    color: 'blue',
  },
  social: {
    id: 'social',
    name: '社交媒体分析师',
    description: '分析社交媒体情绪和舆论',
    icon: 'HeartIcon',
    color: 'pink',
  },
  news: {
    id: 'news',
    name: '新闻分析师',
    description: '分析新闻事件和宏观经济',
    icon: 'NewspaperIcon',
    color: 'green',
  },
  fundamentals: {
    id: 'fundamentals',
    name: '基本面分析师',
    description: '分析财务报表和公司基本面',
    icon: 'UserIcon',
    color: 'purple',
  },
} as const;

// LLM 模型配置
export const LLM_MODELS = {
  'o3-mini-high': {
    name: 'O3 Mini High',
    provider: 'openai',
    description: '高性能推理模型',
    costPer1kTokens: 0.002,
  },
  'gpt-4o-mini': {
    name: 'GPT-4O Mini',
    provider: 'openai',
    description: '快速响应模型',
    costPer1kTokens: 0.0015,
  },
  'o4-mini': {
    name: 'O4 Mini',
    provider: 'openai',
    description: '平衡性能模型',
    costPer1kTokens: 0.001,
  },
} as const;

// 研究深度配置
export const RESEARCH_DEPTH = {
  quick: {
    value: 'quick',
    label: '快速分析',
    description: '基础分析，适合快速决策',
    estimatedTime: '2-5分钟',
    debateRounds: 1,
    riskDiscussRounds: 1,
  },
  standard: {
    value: 'standard',
    label: '标准分析',
    description: '平衡的深度和速度',
    estimatedTime: '5-10分钟',
    debateRounds: 2,
    riskDiscussRounds: 2,
  },
  deep: {
    value: 'deep',
    label: '深度分析',
    description: '全面分析，需要更多时间',
    estimatedTime: '10-20分钟',
    debateRounds: 3,
    riskDiscussRounds: 3,
  },
} as const;

// 分析阶段配置
export const ANALYSIS_STAGES = {
  initialization: {
    id: 'initialization',
    name: '初始化',
    description: '准备分析环境和数据',
    order: 1,
    estimatedDuration: 30,
  },
  data_collection: {
    id: 'data_collection',
    name: '数据收集',
    description: '获取市场数据和新闻信息',
    order: 2,
    estimatedDuration: 60,
  },
  analyst_analysis: {
    id: 'analyst_analysis',
    name: '分析师分析',
    description: '各专业分析师进行深度分析',
    order: 3,
    estimatedDuration: 180,
  },
  research_debate: {
    id: 'research_debate',
    name: '研究辩论',
    description: '多头空头研究员辩论',
    order: 4,
    estimatedDuration: 120,
  },
  trading_decision: {
    id: 'trading_decision',
    name: '交易决策',
    description: '交易员制定交易策略',
    order: 5,
    estimatedDuration: 90,
  },
  risk_assessment: {
    id: 'risk_assessment',
    name: '风险评估',
    description: '风险管理团队评估风险',
    order: 6,
    estimatedDuration: 60,
  },
  final_decision: {
    id: 'final_decision',
    name: '最终决策',
    description: '投资组合管理最终决策',
    order: 7,
    estimatedDuration: 30,
  },
} as const;

// 代理状态配置
export const AGENT_STATUS = {
  idle: {
    value: 'idle',
    label: '等待中',
    color: 'slate',
    icon: 'ClockIcon',
  },
  running: {
    value: 'running',
    label: '运行中',
    color: 'blue',
    icon: 'PlayIcon',
  },
  completed: {
    value: 'completed',
    label: '已完成',
    color: 'green',
    icon: 'CheckCircleIcon',
  },
  error: {
    value: 'error',
    label: '错误',
    color: 'red',
    icon: 'ExclamationCircleIcon',
  },
} as const;

// 交易行动配置
export const TRADING_ACTIONS = {
  buy: {
    value: 'buy',
    label: '买入',
    color: 'green',
    icon: 'ArrowUpIcon',
  },
  sell: {
    value: 'sell',
    label: '卖出',
    color: 'red',
    icon: 'ArrowDownIcon',
  },
  hold: {
    value: 'hold',
    label: '持有',
    color: 'slate',
    icon: 'MinusIcon',
  },
} as const;

// 风险等级配置
export const RISK_LEVELS = {
  low: {
    value: 'low',
    label: '低风险',
    color: 'green',
    description: '保守投资，风险较小',
  },
  medium: {
    value: 'medium',
    label: '中等风险',
    color: 'yellow',
    description: '平衡投资，适中风险',
  },
  high: {
    value: 'high',
    label: '高风险',
    color: 'red',
    description: '激进投资，风险较大',
  },
} as const;

// 数据刷新间隔（毫秒）
export const REFRESH_INTERVALS = {
  realtime: 1000,      // 1秒
  fast: 5000,          // 5秒
  normal: 30000,       // 30秒
  slow: 60000,         // 1分钟
  analysis: 2000,      // 2秒（分析状态）
  agents: 3000,        // 3秒（代理状态）
  reports: 5000,       // 5秒（报告）
  decision: 5000,      // 5秒（决策）
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  userPreferences: 'tradingagents_user_preferences',
  analysisHistory: 'tradingagents_analysis_history',
  recentTickers: 'tradingagents_recent_tickers',
  dashboardLayout: 'tradingagents_dashboard_layout',
} as const;

// 默认配置
export const DEFAULT_ANALYSIS_CONFIG = {
  ticker: 'NVDA',
  analysisDate: new Date().toISOString().split('T')[0],
  selectedAnalysts: ['market', 'social', 'news', 'fundamentals'],
  llmProvider: 'openai',
  deepThinkLlm: 'o3-mini-high',
  quickThinkLlm: 'o3-mini-high',
  maxDebateRounds: 1,
  maxRiskDiscussRounds: 1,
  onlineTools: true,
  researchDepth: 'standard',
} as const;

// 图表配置
export const CHART_CONFIG = {
  colors: {
    primary: '#3b82f6',
    success: '#22c55e',
    danger: '#ef4444',
    warning: '#f59e0b',
    info: '#06b6d4',
    purple: '#8b5cf6',
    pink: '#ec4899',
  },
  animation: {
    duration: 500,
    easing: 'ease-out',
  },
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  networkError: '网络连接错误，请检查网络设置',
  serverError: '服务器错误，请稍后重试',
  authError: '认证失败，请重新登录',
  validationError: '输入数据验证失败',
  notFound: '请求的资源不存在',
  timeout: '请求超时，请重试',
  unknown: '未知错误，请联系技术支持',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  analysisStarted: '分析已成功启动',
  analysisStopped: '分析已停止',
  configSaved: '配置已保存',
  reportGenerated: '报告已生成',
  decisionMade: '交易决策已完成',
} as const;

// 外部链接
export const EXTERNAL_LINKS = {
  github: 'https://github.com/TauricResearch/TradingAgents',
  discord: 'https://discord.com/invite/hk9PGKShPK',
  twitter: 'https://x.com/TauricResearch',
  paper: 'https://arxiv.org/abs/2412.20138',
  website: 'https://tauric.ai',
  documentation: 'https://docs.tauric.ai',
} as const;
