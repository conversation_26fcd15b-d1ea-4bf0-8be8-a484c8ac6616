import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  AnalysisConfig, 
  AnalysisState, 
  AgentStatus, 
  AnalysisReport, 
  TradingDecision 
} from '@/types';

interface AnalysisStore {
  // 当前分析状态
  currentAnalysis: {
    id: string | null;
    config: AnalysisConfig | null;
    state: AnalysisState;
    agentStatuses: AgentStatus[];
    reports: AnalysisReport[];
    decision: TradingDecision | null;
    isRunning: boolean;
  };

  // 分析历史
  analysisHistory: Array<{
    id: string;
    config: AnalysisConfig;
    result: TradingDecision | null;
    timestamp: string;
    duration?: number;
  }>;

  // 用户偏好
  preferences: {
    defaultConfig: Partial<AnalysisConfig>;
    recentTickers: string[];
    dashboardLayout: string;
    autoRefresh: boolean;
    refreshInterval: number;
  };

  // Actions
  setCurrentAnalysis: (analysis: Partial<AnalysisStore['currentAnalysis']>) => void;
  updateAnalysisState: (state: Partial<AnalysisState>) => void;
  updateAgentStatus: (agentId: string, status: Partial<AgentStatus>) => void;
  addReport: (report: AnalysisReport) => void;
  setDecision: (decision: TradingDecision) => void;
  addToHistory: (analysis: AnalysisStore['analysisHistory'][0]) => void;
  updatePreferences: (preferences: Partial<AnalysisStore['preferences']>) => void;
  addRecentTicker: (ticker: string) => void;
  clearCurrentAnalysis: () => void;
  reset: () => void;
}

const initialState = {
  currentAnalysis: {
    id: null,
    config: null,
    state: {
      currentStage: 'initialization',
      progress: 0,
      isComplete: false,
    },
    agentStatuses: [],
    reports: [],
    decision: null,
    isRunning: false,
  },
  analysisHistory: [],
  preferences: {
    defaultConfig: {
      ticker: 'NVDA',
      selectedAnalysts: ['market', 'social', 'news', 'fundamentals'],
      llmProvider: 'openai',
      deepThinkLlm: 'o3-mini-high',
      quickThinkLlm: 'o3-mini-high',
      maxDebateRounds: 1,
      maxRiskDiscussRounds: 1,
      onlineTools: true,
      researchDepth: 'standard' as const,
    },
    recentTickers: ['NVDA', 'AAPL', 'TSLA', 'MSFT', 'GOOGL'],
    dashboardLayout: 'default',
    autoRefresh: true,
    refreshInterval: 30000,
  },
};

export const useAnalysisStore = create<AnalysisStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setCurrentAnalysis: (analysis) =>
          set(
            (state) => ({
              currentAnalysis: { ...state.currentAnalysis, ...analysis },
            }),
            false,
            'setCurrentAnalysis'
          ),

        updateAnalysisState: (newState) =>
          set(
            (state) => ({
              currentAnalysis: {
                ...state.currentAnalysis,
                state: { ...state.currentAnalysis.state, ...newState },
              },
            }),
            false,
            'updateAnalysisState'
          ),

        updateAgentStatus: (agentId, status) =>
          set(
            (state) => {
              const agentStatuses = [...state.currentAnalysis.agentStatuses];
              const existingIndex = agentStatuses.findIndex(
                (agent) => agent.id === agentId
              );

              if (existingIndex >= 0) {
                agentStatuses[existingIndex] = {
                  ...agentStatuses[existingIndex],
                  ...status,
                };
              } else {
                agentStatuses.push({
                  id: agentId,
                  name: agentId,
                  status: 'idle',
                  progress: 0,
                  lastUpdate: new Date().toISOString(),
                  ...status,
                });
              }

              return {
                currentAnalysis: {
                  ...state.currentAnalysis,
                  agentStatuses,
                },
              };
            },
            false,
            'updateAgentStatus'
          ),

        addReport: (report) =>
          set(
            (state) => ({
              currentAnalysis: {
                ...state.currentAnalysis,
                reports: [...state.currentAnalysis.reports, report],
              },
            }),
            false,
            'addReport'
          ),

        setDecision: (decision) =>
          set(
            (state) => ({
              currentAnalysis: {
                ...state.currentAnalysis,
                decision,
                isRunning: false,
              },
            }),
            false,
            'setDecision'
          ),

        addToHistory: (analysis) =>
          set(
            (state) => ({
              analysisHistory: [analysis, ...state.analysisHistory.slice(0, 49)], // 保留最近50条
            }),
            false,
            'addToHistory'
          ),

        updatePreferences: (newPreferences) =>
          set(
            (state) => ({
              preferences: { ...state.preferences, ...newPreferences },
            }),
            false,
            'updatePreferences'
          ),

        addRecentTicker: (ticker) =>
          set(
            (state) => {
              const recentTickers = [
                ticker.toUpperCase(),
                ...state.preferences.recentTickers.filter(
                  (t) => t !== ticker.toUpperCase()
                ),
              ].slice(0, 10); // 保留最近10个

              return {
                preferences: {
                  ...state.preferences,
                  recentTickers,
                },
              };
            },
            false,
            'addRecentTicker'
          ),

        clearCurrentAnalysis: () =>
          set(
            (state) => ({
              currentAnalysis: {
                ...initialState.currentAnalysis,
              },
            }),
            false,
            'clearCurrentAnalysis'
          ),

        reset: () =>
          set(
            () => ({ ...initialState }),
            false,
            'reset'
          ),
      }),
      {
        name: 'trading-agents-store',
        partialize: (state) => ({
          analysisHistory: state.analysisHistory,
          preferences: state.preferences,
        }),
      }
    ),
    {
      name: 'trading-agents-store',
    }
  )
);

// 选择器函数
export const useCurrentAnalysis = () =>
  useAnalysisStore((state) => state.currentAnalysis);

export const useAnalysisHistory = () =>
  useAnalysisStore((state) => state.analysisHistory);

export const usePreferences = () =>
  useAnalysisStore((state) => state.preferences);

export const useRecentTickers = () =>
  useAnalysisStore((state) => state.preferences.recentTickers);
