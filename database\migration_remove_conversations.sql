-- 数据库迁移脚本：移除conversations表，简化为task直接关联message
-- 执行前请备份数据库！

-- 1. 首先备份现有的messages数据（如果需要保留的话）
CREATE TABLE IF NOT EXISTS messages_backup AS 
SELECT * FROM messages;

-- 2. 删除messages表的外键约束
ALTER TABLE messages DROP FOREIGN KEY messages_ibfk_1;
ALTER TABLE messages DROP FOREIGN KEY messages_ibfk_2;

-- 3. 删除analysis_steps表中对conversations的外键约束
ALTER TABLE analysis_steps DROP FOREIGN KEY analysis_steps_ibfk_2;

-- 4. 修改messages表结构
-- 删除conversation_id列
ALTER TABLE messages DROP COLUMN conversation_id;

-- 添加thread_id列（如果不存在）
ALTER TABLE messages ADD COLUMN thread_id VARCHAR(100) COMMENT 'LangGraph线程ID' AFTER parent_message_id;

-- 5. 重新创建外键约束
ALTER TABLE messages 
ADD CONSTRAINT fk_messages_task_id 
FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE;

-- 6. 重新创建索引
DROP INDEX idx_conversation_id ON messages;
CREATE INDEX idx_thread_id ON messages (thread_id);

-- 7. 修改analysis_steps表，移除conversation_id列
ALTER TABLE analysis_steps DROP COLUMN conversation_id;

-- 8. 删除conversations表
DROP TABLE IF EXISTS conversations;

-- 9. 删除相关的存储过程
DROP PROCEDURE IF EXISTS StartConversation;

-- 10. 更新task_overview视图（已在schema.sql中更新）

-- 11. 验证数据完整性
-- 检查是否有孤立的messages（没有对应的task）
SELECT COUNT(*) as orphaned_messages 
FROM messages m 
LEFT JOIN tasks t ON m.task_id = t.task_id 
WHERE t.task_id IS NULL;

-- 12. 显示迁移后的表结构
DESCRIBE messages;
DESCRIBE analysis_steps;

-- 迁移完成提示
SELECT 'Database migration completed successfully!' as status;
