#!/bin/bash

# TradingAgents Frontend CI/CD 快速设置脚本
# 用于初始化 CI/CD 环境和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                TradingAgents Frontend                        ║
║                   CI/CD 快速设置                             ║
╚══════════════════════════════════════════════════════════════╝

此脚本将帮助您快速设置 CI/CD 环境，包括：
✓ 环境配置文件
✓ Docker 配置验证
✓ GitHub Secrets 设置指导
✓ 本地开发环境初始化

EOF
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Node.js
    if command -v node > /dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        log_success "Node.js 已安装: $NODE_VERSION"
    else
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查 npm
    if command -v npm > /dev/null 2>&1; then
        NPM_VERSION=$(npm --version)
        log_success "npm 已安装: $NPM_VERSION"
    else
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查 Docker
    if command -v docker > /dev/null 2>&1; then
        DOCKER_VERSION=$(docker --version)
        log_success "Docker 已安装: $DOCKER_VERSION"
    else
        log_warning "Docker 未安装，部分功能将不可用"
    fi
    
    # 检查 Docker Compose
    if command -v docker-compose > /dev/null 2>&1; then
        COMPOSE_VERSION=$(docker-compose --version)
        log_success "Docker Compose 已安装: $COMPOSE_VERSION"
    else
        log_warning "Docker Compose 未安装，部分功能将不可用"
    fi
    
    # 检查 Git
    if command -v git > /dev/null 2>&1; then
        GIT_VERSION=$(git --version)
        log_success "Git 已安装: $GIT_VERSION"
    else
        log_error "Git 未安装"
        exit 1
    fi
}

# 设置环境配置文件
setup_env_files() {
    log_info "设置环境配置文件..."
    
    # 创建本地开发环境配置
    if [[ ! -f ".env.local" ]]; then
        log_info "创建 .env.local 文件..."
        cp .env.example .env.local
        
        # 生成随机密钥
        JWT_SECRET=$(openssl rand -hex 32 2>/dev/null || echo "your_jwt_secret_$(date +%s)")
        SESSION_SECRET=$(openssl rand -hex 32 2>/dev/null || echo "your_session_secret_$(date +%s)")
        
        # 更新配置文件
        sed -i.bak "s/your_jwt_secret_here/$JWT_SECRET/g" .env.local
        sed -i.bak "s/your_session_secret_here/$SESSION_SECRET/g" .env.local
        rm .env.local.bak 2>/dev/null || true
        
        log_success ".env.local 文件已创建"
    else
        log_warning ".env.local 文件已存在，跳过创建"
    fi
    
    # 创建预发布环境配置模板
    if [[ ! -f ".env.staging" ]]; then
        log_info "创建 .env.staging 模板..."
        cat > .env.staging << 'EOF'
# 预发布环境配置
NODE_ENV=production
ENVIRONMENT=staging

# API 配置
NEXT_PUBLIC_API_BASE_URL=https://api-staging.tradingagents.example.com
NEXT_PUBLIC_WS_URL=wss://ws-staging.tradingagents.example.com
BACK_END_URL=https://api-staging.tradingagents.example.com

# 数据库配置
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD_STAGING}
MYSQL_DATABASE=trading_analysis_staging
MYSQL_USER=trading_user
MYSQL_PASSWORD=${MYSQL_PASSWORD_STAGING}

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
EOF
        log_success ".env.staging 模板已创建"
    fi
    
    # 创建生产环境配置模板
    if [[ ! -f ".env.production" ]]; then
        log_info "创建 .env.production 模板..."
        cat > .env.production << 'EOF'
# 生产环境配置
NODE_ENV=production
ENVIRONMENT=production

# API 配置
NEXT_PUBLIC_API_BASE_URL=https://api.tradingagents.example.com
NEXT_PUBLIC_WS_URL=wss://ws.tradingagents.example.com
BACK_END_URL=https://api.tradingagents.example.com

# 数据库配置
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD_PROD}
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
MYSQL_PASSWORD=${MYSQL_PASSWORD_PROD}

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}

# 性能优化
NEXT_TELEMETRY_DISABLED=1
EOF
        log_success ".env.production 模板已创建"
    fi
}

# 验证 Docker 配置
verify_docker_config() {
    log_info "验证 Docker 配置..."
    
    # 检查 Docker Compose 文件
    COMPOSE_FILES=(
        "docker/docker-compose.yml"
        "docker/docker-compose.dev.yml"
        "docker/docker-compose.prod.yml"
    )
    
    for file in "${COMPOSE_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "找到配置文件: $file"
            
            # 验证配置文件语法
            if docker-compose -f "$file" config > /dev/null 2>&1; then
                log_success "配置文件语法正确: $file"
            else
                log_warning "配置文件语法错误: $file"
            fi
        else
            log_warning "配置文件不存在: $file"
        fi
    done
    
    # 检查 Dockerfile
    if [[ -f "docker/Dockerfile" ]]; then
        log_success "找到 Dockerfile"
    else
        log_warning "Dockerfile 不存在"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [[ -f "package.json" ]]; then
        npm ci
        log_success "依赖安装完成"
    else
        log_error "package.json 不存在"
        exit 1
    fi
}

# 运行初始检查
run_initial_checks() {
    log_info "运行初始检查..."
    
    # ESLint 检查
    log_info "运行 ESLint 检查..."
    if npm run lint; then
        log_success "ESLint 检查通过"
    else
        log_warning "ESLint 检查发现问题，请修复后继续"
    fi
    
    # TypeScript 类型检查
    log_info "运行 TypeScript 类型检查..."
    if npm run type-check; then
        log_success "TypeScript 类型检查通过"
    else
        log_warning "TypeScript 类型检查发现问题，请修复后继续"
    fi
    
    # 构建测试
    log_info "测试应用构建..."
    if npm run build; then
        log_success "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 设置 Git hooks
setup_git_hooks() {
    log_info "设置 Git hooks..."
    
    # 创建 pre-commit hook
    mkdir -p .git/hooks
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for TradingAgents Frontend

echo "运行 pre-commit 检查..."

# 运行 ESLint
echo "运行 ESLint..."
npm run lint
if [ $? -ne 0 ]; then
    echo "ESLint 检查失败，请修复后再提交"
    exit 1
fi

# 运行 TypeScript 检查
echo "运行 TypeScript 检查..."
npm run type-check
if [ $? -ne 0 ]; then
    echo "TypeScript 检查失败，请修复后再提交"
    exit 1
fi

echo "Pre-commit 检查通过"
EOF
    
    chmod +x .git/hooks/pre-commit
    log_success "Git pre-commit hook 已设置"
}

# 显示 GitHub Secrets 设置指导
show_github_secrets_guide() {
    log_info "GitHub Secrets 设置指导"
    
    cat << 'EOF'

📋 需要在 GitHub 仓库中设置的 Secrets：

1. 进入 GitHub 仓库页面
2. 点击 Settings > Secrets and variables > Actions
3. 添加以下 Repository secrets：

🔐 数据库密钥：
   MYSQL_ROOT_PASSWORD_STAGING=your_staging_root_password
   MYSQL_ROOT_PASSWORD_PROD=your_production_root_password
   MYSQL_PASSWORD_STAGING=your_staging_user_password
   MYSQL_PASSWORD_PROD=your_production_user_password

🔑 API 密钥：
   NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
   NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key

🚀 部署密钥（如果使用 SSH 部署）：
   DEPLOY_SSH_KEY=your_private_ssh_key
   DEPLOY_HOST=your_server_host
   DEPLOY_USER=your_deploy_user

📊 监控密钥（可选）：
   LHCI_GITHUB_APP_TOKEN=your_lighthouse_ci_token
   SLACK_WEBHOOK_URL=your_slack_webhook_url

详细配置请参考：.github/SECRETS_SETUP.md

EOF
}

# 显示下一步操作
show_next_steps() {
    log_success "CI/CD 环境设置完成！"
    
    cat << 'EOF'

🎉 设置完成！接下来您可以：

📝 配置 API 密钥：
   编辑 .env.local 文件，填入您的 OpenAI 和 Finnhub API 密钥

🐳 启动开发环境：
   npm run dev                    # 启动开发服务器
   docker-compose up -d           # 启动 Docker 服务

🔧 运行检查：
   npm run lint                   # 代码检查
   npm run type-check             # 类型检查
   npm run build                  # 构建应用

🚀 部署应用：
   ./scripts/deploy.sh staging    # 部署到预发布环境
   ./scripts/deploy.sh production # 部署到生产环境

📚 查看文档：
   docs/CI_CD_GUIDE.md           # CI/CD 使用指南
   .github/SECRETS_SETUP.md      # GitHub Secrets 配置

🔗 有用的链接：
   - GitHub Actions: https://github.com/your-repo/actions
   - Docker Hub: https://hub.docker.com/
   - 项目文档: README.md

EOF
}

# 主函数
main() {
    show_welcome
    
    # 检查系统要求
    check_requirements
    
    # 安装依赖
    install_dependencies
    
    # 设置环境配置
    setup_env_files
    
    # 验证 Docker 配置
    verify_docker_config
    
    # 运行初始检查
    run_initial_checks
    
    # 设置 Git hooks
    setup_git_hooks
    
    # 显示 GitHub Secrets 设置指导
    show_github_secrets_guide
    
    # 显示下一步操作
    show_next_steps
}

# 运行主函数
main "$@"
