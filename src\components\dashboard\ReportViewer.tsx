'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  DocumentTextIcon, 
  ChartBarIcon, 
  NewspaperIcon, 
  HeartIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AnalysisReport } from '@/lib/api';

interface ReportViewerProps {
  reports: AnalysisReport[];
  selectedAnalysts: string[];
}

export function ReportViewer({ reports, selectedAnalysts }: ReportViewerProps) {
  const [selectedReport, setSelectedReport] = useState<AnalysisReport | null>(null);
  const [filterType, setFilterType] = useState<string>('all');

  const reportTypes = {
    market: { name: '市场分析', icon: ChartBarIcon, color: 'blue' },
    social: { name: '社交媒体', icon: HeartIcon, color: 'pink' },
    news: { name: '新闻分析', icon: NewspaperIcon, color: 'green' },
    fundamentals: { name: '基本面', icon: UserIcon, color: 'purple' },
    research: { name: '研究报告', icon: DocumentTextIcon, color: 'indigo' },
    trading: { name: '交易决策', icon: ChartBarIcon, color: 'orange' },
    risk: { name: '风险评估', icon: DocumentTextIcon, color: 'red' },
  };

  const filterOptions = [
    { id: 'all', name: '全部报告' },
    ...Object.entries(reportTypes).map(([key, value]) => ({
      id: key,
      name: value.name,
    })),
  ];

  const filteredReports = reports.filter(report => {
    if (filterType === 'all') return true;
    return report.type === filterType;
  });

  const getReportIcon = (type: string) => {
    const reportType = reportTypes[type as keyof typeof reportTypes];
    if (!reportType) return DocumentTextIcon;
    return reportType.icon;
  };

  const getReportColor = (type: string) => {
    const reportType = reportTypes[type as keyof typeof reportTypes];
    if (!reportType) return 'slate';
    return reportType.color;
  };

  const formatContent = (content: string) => {
    // 简单的 Markdown 格式化
    return content
      .replace(/### (.*)/g, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/## (.*)/g, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
      .replace(/# (.*)/g, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/\n/g, '<br>');
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 报告列表 */}
      <div className="lg:col-span-1 space-y-4">
        {/* 过滤器 */}
        <Card>
          <CardHeader>
            <CardTitle>报告筛选</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {filterOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => setFilterType(option.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    filterType === option.id
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100'
                      : 'hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300'
                  }`}
                >
                  {option.name}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 报告列表 */}
        <Card>
          <CardHeader>
            <CardTitle>分析报告 ({filteredReports.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredReports.length === 0 ? (
                <div className="text-center text-slate-500 py-8">
                  暂无报告
                </div>
              ) : (
                filteredReports.map((report, index) => {
                  const Icon = getReportIcon(report.type);
                  const color = getReportColor(report.type);
                  
                  return (
                    <motion.div
                      key={`${report.type}-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => setSelectedReport(report)}
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                        selectedReport === report
                          ? `border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20`
                          : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg bg-${color}-100 dark:bg-${color}-900/20`}>
                          <Icon className={`h-4 w-4 text-${color}-600`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 dark:text-white truncate">
                            {report.title}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs text-slate-500">
                              {report.agent}
                            </span>
                            <span className="text-xs text-slate-400">•</span>
                            <div className="flex items-center space-x-1 text-xs text-slate-500">
                              <ClockIcon className="h-3 w-3" />
                              <span>
                                {new Date(report.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 报告详情 */}
      <div className="lg:col-span-2">
        <Card className="h-full">
          <CardHeader>
            <CardTitle>
              {selectedReport ? selectedReport.title : '选择报告查看详情'}
            </CardTitle>
            {selectedReport && (
              <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400">
                <span>代理: {selectedReport.agent}</span>
                <span>•</span>
                <span>类型: {reportTypes[selectedReport.type as keyof typeof reportTypes]?.name || selectedReport.type}</span>
                <span>•</span>
                <span>时间: {new Date(selectedReport.timestamp).toLocaleString()}</span>
              </div>
            )}
          </CardHeader>
          <CardContent>
            {selectedReport ? (
              <motion.div
                key={selectedReport.timestamp}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="prose prose-slate dark:prose-invert max-w-none"
              >
                <div 
                  className="text-slate-700 dark:text-slate-300 leading-relaxed"
                  dangerouslySetInnerHTML={{ 
                    __html: `<p class="mb-4">${formatContent(selectedReport.content)}</p>` 
                  }}
                />
              </motion.div>
            ) : (
              <div className="flex items-center justify-center h-64 text-slate-500">
                <div className="text-center">
                  <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <p>请从左侧列表中选择一个报告查看详细内容</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
