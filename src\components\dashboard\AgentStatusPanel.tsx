'use client';

import { motion } from 'framer-motion';
import { 
  UserIcon, 
  ChartBarIcon, 
  NewspaperIcon, 
  HeartIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AgentStatus } from '@/lib/api';

interface AgentStatusPanelProps {
  agentStatuses: AgentStatus[];
  selectedAnalysts: string[];
}

export function AgentStatusPanel({ agentStatuses, selectedAnalysts }: AgentStatusPanelProps) {
  const agentConfig = {
    market: {
      name: '市场分析师',
      description: '分析技术指标和价格走势',
      icon: ChartBarIcon,
      color: 'blue',
    },
    social: {
      name: '社交媒体分析师',
      description: '分析社交媒体情绪和舆论',
      icon: HeartIcon,
      color: 'pink',
    },
    news: {
      name: '新闻分析师',
      description: '分析新闻事件和宏观经济',
      icon: NewspaperIcon,
      color: 'green',
    },
    fundamentals: {
      name: '基本面分析师',
      description: '分析财务报表和公司基本面',
      icon: UserIcon,
      color: 'purple',
    },
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'running':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      default:
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'error':
        return '错误';
      default:
        return '等待中';
    }
  };

  // 创建代理状态映射
  const agentStatusMap = agentStatuses.reduce((acc, agent) => {
    acc[agent.id] = agent;
    return acc;
  }, {} as Record<string, AgentStatus>);

  return (
    <div className="space-y-6">
      {/* 分析师状态 */}
      <Card>
        <CardHeader>
          <CardTitle>分析师团队状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectedAnalysts.map((analystId, index) => {
              const config = agentConfig[analystId as keyof typeof agentConfig];
              const status = agentStatusMap[analystId] || {
                id: analystId,
                name: config?.name || analystId,
                status: 'idle',
                progress: 0,
                lastUpdate: new Date().toISOString(),
              };

              if (!config) return null;

              return (
                <motion.div
                  key={analystId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 border rounded-lg ${getStatusColor(status.status)}`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg bg-${config.color}-100 dark:bg-${config.color}-900/20`}>
                        <config.icon className={`h-5 w-5 text-${config.color}-600`} />
                      </div>
                      <div>
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          {config.name}
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    {getStatusIcon(status.status)}
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        状态: {getStatusText(status.status)}
                      </span>
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                        {status.progress}%
                      </span>
                    </div>

                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full bg-${config.color}-500`}
                        initial={{ width: 0 }}
                        animate={{ width: `${status.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>

                    {status.message && (
                      <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                        {status.message}
                      </p>
                    )}

                    <p className="text-xs text-slate-500 dark:text-slate-500">
                      最后更新: {new Date(status.lastUpdate).toLocaleTimeString()}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 其他代理状态 */}
      <Card>
        <CardHeader>
          <CardTitle>其他代理状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 研究团队 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {['bull_researcher', 'bear_researcher'].map((agentId) => {
                const status = agentStatusMap[agentId];
                const isBull = agentId === 'bull_researcher';
                
                return (
                  <div
                    key={agentId}
                    className={`p-4 border rounded-lg ${
                      status ? getStatusColor(status.status) : 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${isBull ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'}`}>
                          <span className={`text-lg ${isBull ? 'text-green-600' : 'text-red-600'}`}>
                            {isBull ? '🐂' : '🐻'}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {isBull ? '多头研究员' : '空头研究员'}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {isBull ? '寻找买入机会' : '识别风险因素'}
                          </p>
                        </div>
                      </div>
                      {status && getStatusIcon(status.status)}
                    </div>

                    {status && (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            状态: {getStatusText(status.status)}
                          </span>
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {status.progress}%
                          </span>
                        </div>

                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full ${isBull ? 'bg-green-500' : 'bg-red-500'}`}
                            initial={{ width: 0 }}
                            animate={{ width: `${status.progress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>

                        {status.message && (
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                            {status.message}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* 交易员和风险管理 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { id: 'trader', name: '交易员', icon: '💼', description: '制定交易策略' },
                { id: 'risk_manager', name: '风险管理', icon: '🛡️', description: '评估投资风险' },
              ].map((agent) => {
                const status = agentStatusMap[agent.id];
                
                return (
                  <div
                    key={agent.id}
                    className={`p-4 border rounded-lg ${
                      status ? getStatusColor(status.status) : 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-lg bg-slate-100 dark:bg-slate-800">
                          <span className="text-lg">{agent.icon}</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {agent.name}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {agent.description}
                          </p>
                        </div>
                      </div>
                      {status && getStatusIcon(status.status)}
                    </div>

                    {status && (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            状态: {getStatusText(status.status)}
                          </span>
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {status.progress}%
                          </span>
                        </div>

                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className="h-2 rounded-full bg-slate-500"
                            initial={{ width: 0 }}
                            animate={{ width: `${status.progress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>

                        {status.message && (
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                            {status.message}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
