#!/bin/bash

# TradingAgents 用户认证系统初始化脚本
# 用于设置数据库和启动项目

set -e

echo "🚀 TradingAgents 用户认证系统初始化"
echo "=================================="

# 检查环境
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL 客户端未安装，请先安装 MySQL"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ Node.js 和 npm 未安装，请先安装 Node.js"
    exit 1
fi

# 检查环境变量
if [ ! -f .env.local ]; then
    echo "⚠️  未找到 .env.local 文件，将使用 .env.example 作为模板"
    cp .env.example .env.local
    echo "📝 请编辑 .env.local 文件，填入实际的配置值"
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
DB_HOST=$(grep DB_HOST .env.local | cut -d'=' -f2 | tr -d ' ')
DB_USER=$(grep DB_USER .env.local | cut -d'=' -f2 | tr -d ' ')
DB_PASSWORD=$(grep DB_PASSWORD .env.local | cut -d'=' -f2 | tr -d ' ')
DB_NAME=$(grep DB_NAME .env.local | cut -d'=' -f2 | tr -d ' ')
DB_PORT=$(grep DB_PORT .env.local | cut -d'=' -f2 | tr -d ' ')

if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME" 2>/dev/null; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败，请检查配置"
    echo "数据库配置："
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  User: $DB_USER"
    echo "  Database: $DB_NAME"
    exit 1
fi

# 初始化用户认证数据库
echo "📊 初始化用户认证数据库..."
if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < database/user_init.sql; then
    echo "✅ 用户认证数据库初始化成功"
else
    echo "❌ 用户认证数据库初始化失败"
    exit 1
fi

# 检查依赖
echo "📦 检查项目依赖..."
npm install

# 启动开发服务器
echo "🚀 启动开发服务器..."
echo "=================================="
echo "访问地址："
echo "  首页: http://localhost:3000"
echo "  注册: http://localhost:3000/register"
echo "  登录: http://localhost:3000/login"
echo "  任务列表: http://localhost:3000/tasks"
echo "  创建任务: http://localhost:3000/create-task"
echo "=================================="

npm run dev
