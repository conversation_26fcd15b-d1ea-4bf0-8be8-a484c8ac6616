import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // 尝试描述 tasks 表的结构
    const tableStructure = await query('DESCRIBE tasks');
    
    // 尝试查询 tasks 表中的所有数据
    const allTasks = await query('SELECT * FROM tasks LIMIT 10');

    return NextResponse.json({
      message: 'Database connection and query successful.',
      structure: tableStructure,
      sampleData: allTasks,
    });

  } catch (error) {
    console.error('Database test failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
    return NextResponse.json(
      { 
        error: 'Database test failed.',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
