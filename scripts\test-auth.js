#!/usr/bin/env node

/**
 * 用户认证系统测试脚本
 * 用于验证注册、登录、获取用户信息等功能
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_USERNAME = '测试用户';
const TEST_PASSWORD = 'Test@123456';

async function testAuthSystem() {
  console.log('🚀 开始测试用户认证系统...\n');

  try {
    // 1. 测试注册
    console.log('📋 测试用户注册...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        username: TEST_USERNAME,
        password: TEST_PASSWORD,
      }),
    });

    const registerResult = await registerResponse.json();
    if (registerResponse.ok) {
      console.log('✅ 注册成功:', registerResult.message);
    } else {
      console.log('⚠️  注册失败:', registerResult.error);
    }

    // 2. 测试登录
    console.log('\n🔑 测试用户登录...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      }),
    });

    const loginResult = await loginResponse.json();
    if (loginResponse.ok) {
      console.log('✅ 登录成功:', loginResult.message);
      console.log('👤 用户信息:', {
        id: loginResult.user.id,
        email: loginResult.user.email,
        username: loginResult.user.username,
      });
    } else {
      console.log('❌ 登录失败:', loginResult.error);
      return;
    }

    // 3. 测试获取用户信息
    console.log('\n👤 测试获取用户信息...');
    
    // 获取cookie
    const cookies = loginResponse.headers.get('set-cookie');
    const cookieHeader = cookies ? cookies.join('; ') : '';
    
    const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Cookie': cookieHeader,
      },
    });

    const meResult = await meResponse.json();
    if (meResponse.ok) {
      console.log('✅ 获取用户信息成功:', {
        id: meResult.user.id,
        email: meResult.user.email,
        username: meResult.user.username,
        role: meResult.user.role,
      });
    } else {
      console.log('❌ 获取用户信息失败:', meResult.error);
    }

    // 4. 测试登出
    console.log('\n🚪 测试用户登出...');
    const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Cookie': cookieHeader,
      },
    });

    const logoutResult = await logoutResponse.json();
    if (logoutResponse.ok) {
      console.log('✅ 登出成功:', logoutResult.message);
    } else {
      console.log('❌ 登出失败:', logoutResult.error);
    }

    console.log('\n🎉 用户认证系统测试完成！');
    console.log('\n📖 使用说明:');
    console.log(`   注册页面: ${BASE_URL}/register`);
    console.log(`   登录页面: ${BASE_URL}/login`);
    console.log(`   任务列表: ${BASE_URL}/tasks`);
    console.log(`   创建任务: ${BASE_URL}/create-task`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testAuthSystem();
}

module.exports = { testAuthSystem };
