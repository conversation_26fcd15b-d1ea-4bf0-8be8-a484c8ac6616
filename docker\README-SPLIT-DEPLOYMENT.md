# 分体式部署配置说明

## 概述

原有的 `docker-compose.prod.yml` 已被拆分为两个独立的配置文件，以实现更灵活的部署策略：

- `docker-compose.prod.frontend.yml` - 仅包含前端服务
- `docker-compose.prod.backend.yml` - 包含数据库和反向代理服务

## 文件结构

```
docker/
├── docker-compose.prod.yml         # 原始完整配置（保留）
├── docker-compose.prod.frontend.yml # 前端服务配置
├── docker-compose.prod.backend.yml  # 后端服务配置
└── README-SPLIT-DEPLOYMENT.md      # 本说明文档
```

## 使用方式

### 1. 独立部署前端

```bash
# 仅启动前端服务
docker-compose -f docker-compose.prod.frontend.yml up -d
```

### 2. 独立部署后端

```bash
# 仅启动后端服务（MySQL）
docker-compose -f docker-compose.prod.backend.yml up -d

# 启动后端服务 + Nginx（可选）
docker-compose -f docker-compose.prod.backend.yml --profile nginx up -d
```

### 3. 同时部署前后端

```bash
# 方法1：分别启动
docker-compose -f docker-compose.prod.backend.yml up -d
docker-compose -f docker-compose.prod.frontend.yml up -d

# 方法2：合并启动
docker-compose -f docker-compose.prod.backend.yml -f docker-compose.prod.frontend.yml up -d
```

### 4. 停止服务

```bash
# 停止前端
docker-compose -f docker-compose.prod.frontend.yml down

# 停止后端
docker-compose -f docker-compose.prod.backend.yml down

# 停止所有服务
docker-compose -f docker-compose.prod.backend.yml -f docker-compose.prod.frontend.yml down
```

## 配置说明

### 网络配置

两个配置文件共享同一个网络 `tradingagents-network`，确保服务间可以相互通信。

### 环境变量

- 前端服务仍然需要数据库连接信息（DB_HOST, DB_PORT 等）
- 后端服务使用独立的环境变量配置

### 数据持久化

- MySQL 数据卷 `mysql_data` 在后端配置中定义
- SSL 证书卷 `nginx-ssl` 也在后端配置中定义

## 部署场景

### 场景 1：前后端分离部署

- 前端部署在云服务器 A
- 后端（MySQL + Nginx）部署在云服务器 B
- 通过修改前端配置中的 DB_HOST 指向后端服务器

### 场景 2：单机部署

- 所有服务部署在同一台服务器
- 使用默认配置即可

### 场景 3：微服务架构

- 可以进一步拆分后端服务
- MySQL 和 Nginx 可以分别部署

## 注意事项

1. **网络连通性**：确保前后端服务器之间的网络连通
2. **防火墙配置**：开放必要的端口（3000, 3306, 80, 443）
3. **环境变量**：根据实际情况调整环境变量
4. **数据备份**：定期备份 MySQL 数据卷
5. **SSL 证书**：如果使用 HTTPS，确保 SSL 证书正确配置

## 故障排除

### 服务无法连接

检查网络配置：

```bash
docker network ls
docker network inspect tradingagents-network
```

### 端口冲突

检查端口占用：

```bash
netstat -tulnp | grep :3000
netstat -tulnp | grep :3306
```

### 查看日志

```bash
# 前端日志
docker-compose -f docker-compose.prod.frontend.yml logs -f

# 后端日志
docker-compose -f docker-compose.prod.backend.yml logs -f
```
