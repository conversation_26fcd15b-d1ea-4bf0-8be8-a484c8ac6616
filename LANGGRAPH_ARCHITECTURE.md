# LangGraph 架构重构说明

## 概述

本次重构将 LangGraph 的核心逻辑从前端迁移到后端，实现了更清晰的前后端分离架构。

## 架构变更

### 🔄 重构前 (旧架构)
```
前端 (useLangGraphAgent Hook)
├── LangGraph 实例化
├── 工具定义和配置
├── 状态图创建
├── AI 模型调用
├── 工具执行
└── 状态管理
```

### ✅ 重构后 (新架构)
```
后端 (LangGraphService)
├── LangGraph 实例化
├── 工具定义和配置
├── 状态图创建
├── AI 模型调用
├── 工具执行
└── 会话管理

前端 (简化的 Hook + Client)
├── 状态管理
├── API 调用
├── UI 交互
└── 错误处理
```

## 核心组件

### 1. 后端服务 (`src/lib/langgraph-server.ts`)

**LangGraphService 类**
- 管理 LangGraph 实例
- 处理会话状态
- 执行 AI 分析和对话
- 支持流式响应
- 事件驱动的状态更新

```typescript
export class LangGraphService extends EventEmitter {
  // 创建会话
  createSession(threadId?: string): string
  
  // 发送消息
  sendMessage(threadId: string, message: string): Promise<any>
  
  // 分析股票
  analyzeStock(threadId: string, ticker: string, config: any): Promise<any>
  
  // 流式分析
  streamAnalysis(threadId: string, ticker: string, config: any): AsyncGenerator<any>
  
  // 会话管理
  getSession(threadId: string): SessionState | null
  clearSession(threadId: string): void
}
```

### 2. 前端客户端 (`src/lib/langgraph-client.ts`)

**LangGraphClient 类**
- 封装 API 调用
- 处理流式响应
- 管理网络请求
- 类型安全的接口

```typescript
export class LangGraphClient {
  // 发送消息
  sendMessage(message: string, threadId?: string): Promise<any>
  
  // 分析股票
  analyzeStock(ticker: string, config: any, threadId?: string): Promise<any>
  
  // 流式分析
  streamAnalysis(ticker: string, config: any, threadId?: string): AsyncGenerator<any>
  
  // 会话管理
  getSessionState(threadId: string): Promise<any>
  clearSession(threadId: string): Promise<void>
}
```

### 3. 简化的 Hook (`src/hooks/useLangGraphAgent.ts`)

**重构后的功能**
- ✅ 状态管理 (messages, isProcessing, currentStep, etc.)
- ✅ API 调用封装
- ✅ 错误处理
- ✅ UI 交互逻辑
- ❌ LangGraph 实例化 (移至后端)
- ❌ 工具定义 (移至后端)
- ❌ AI 模型调用 (移至后端)

## API 端点

### 1. 聊天接口
```
POST /api/langgraph/chat
Body: { message: string, threadId?: string }
Response: { content: string, metadata: any }
```

### 2. 分析接口
```
POST /api/langgraph/analyze
Body: { ticker: string, config?: any, threadId?: string }
Response: { content: string, analysisResults: any, tradingDecision: any }
```

### 3. 流式分析接口
```
POST /api/langgraph/stream
Body: { ticker: string, config?: any, threadId?: string }
Response: Server-Sent Events (SSE)
```

### 4. 状态管理接口
```
GET /api/langgraph/state?threadId=xxx
Response: SessionState

POST /api/langgraph/state
Body: { threadId: string }
Response: { success: boolean }

DELETE /api/langgraph/state?threadId=xxx
Response: { success: boolean }
```

## 使用示例

### 1. 使用 Hook (推荐)
```typescript
const {
  messages,
  isProcessing,
  currentStep,
  analyzeStock,
  sendMessage,
  streamAnalysis,
  clearConversation,
} = useLangGraphAgent();

// 分析股票
await analyzeStock('AAPL', { analysisType: 'comprehensive' });

// 发送消息
await sendMessage('请分析 TSLA 的投资价值');

// 流式分析
for await (const chunk of streamAnalysis('NVDA')) {
  console.log('进度:', chunk.progress);
}
```

### 2. 直接使用客户端
```typescript
import { langGraphClient } from '@/lib/langgraph-client';

// 直接分析
const result = await langGraphClient.analyzeStock('AAPL');

// 流式分析
for await (const chunk of langGraphClient.streamAnalysis('TSLA')) {
  console.log(chunk);
}
```

## 优势

### 🚀 性能优化
- **服务器端执行**: LangGraph 在服务器端运行，减少客户端负载
- **流式响应**: 支持实时数据流，提升用户体验
- **会话复用**: 后端管理会话状态，避免重复初始化

### 🔒 安全性提升
- **API 密钥保护**: 敏感配置只在服务器端存储
- **请求验证**: 后端可以添加认证和授权
- **数据隔离**: 用户数据在服务器端隔离管理

### 🛠️ 可维护性
- **清晰分离**: 前后端职责明确
- **类型安全**: 完整的 TypeScript 类型定义
- **错误处理**: 统一的错误处理机制

### 📈 可扩展性
- **水平扩展**: 后端服务可以独立扩展
- **多客户端支持**: 同一后端可支持多种前端
- **微服务架构**: 可以拆分为独立的微服务

## 迁移指南

### 1. 现有代码迁移
```typescript
// 旧代码
const { analyzeStock } = useLangGraphAgent();
await analyzeStock(ticker, config);

// 新代码 (无需更改)
const { analyzeStock } = useLangGraphAgent();
await analyzeStock(ticker, config);
```

### 2. 直接 API 调用
```typescript
// 如果需要更细粒度的控制
import { langGraphClient } from '@/lib/langgraph-client';
const result = await langGraphClient.analyzeStock(ticker, config);
```

## 部署注意事项

### 1. 环境变量
```env
# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 内部 API 密钥 (可选)
INTERNAL_API_KEY=your_internal_api_key
```

### 2. 依赖安装
```bash
# 确保安装了 LangGraph 相关依赖
npm install @langchain/langgraph @langchain/openai @langchain/core
```

### 3. 内存管理
- 后端会话状态存储在内存中
- 生产环境建议使用 Redis 或数据库
- 定期清理过期会话

## 测试

运行示例组件来测试新架构：
```typescript
import { LangGraphExample } from '@/components/examples/LangGraphExample';

// 在页面中使用
<LangGraphExample />
```

## 总结

这次重构实现了：
- ✅ LangGraph 逻辑完全后端化
- ✅ 前端专注于 UI 和状态管理
- ✅ 支持流式响应和实时更新
- ✅ 类型安全的 API 接口
- ✅ 清晰的架构分离
- ✅ 更好的可扩展性和维护性

新架构为项目的长期发展奠定了坚实的基础。
