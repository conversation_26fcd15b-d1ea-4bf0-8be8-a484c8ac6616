# TradingAgents 用户认证系统使用指南

## 概述

本项目已集成完整的用户认证系统，专为中台项目设计，支持用户先浏览核心功能，在需要执行关键操作时要求登录。

## 功能特性

- ✅ 邮箱注册（支持密码强度验证）
- ✅ 用户登录（JWT Token 认证）
- ✅ 会话管理（自动刷新 Token）
- ✅ 用户信息获取
- ✅ 登出功能
- ✅ 响应式设计（移动端适配）
- ✅ 中台项目认证策略（先体验后登录）

## 中台项目认证策略

- **首页浏览**：无需登录即可访问首页、查看功能介绍
- **任务列表**：未登录用户可查看系统公开任务，登录后查看个人任务
- **创建任务**：未登录用户可查看创建界面，点击创建时提示登录
- **个人中心**：登录后显示个人任务、个人资料等

## 数据库结构

已创建以下用户相关表：

- `users` - 用户基本信息表
- `user_sessions` - 用户会话管理
- `user_activity_logs` - 用户活动日志

## 快速开始

### 1. 初始化数据库

```bash
# 运行用户认证系统初始化SQL
mysql -u root -p trading_analysis < database/user_init.sql
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env.local` 并配置：

```bash
# 认证相关配置
JWT_SECRET=your_strong_jwt_secret_here
REFRESH_SECRET=your_strong_refresh_secret_here
```

### 3. 启动项目

```bash
npm run dev
```

## 使用场景

### 未登录用户

- ✅ 浏览首页和核心功能
- ✅ 查看系统公开任务
- ✅ 查看任务详情和结果
- ❌ 无法创建新任务
- ❌ 无法查看个人任务

### 登录用户

- ✅ 创建个人分析任务
- ✅ 查看和管理个人任务
- ✅ 查看任务消息和详情
- ✅ 启动和停止任务
- ✅ 个人资料管理

## API 端点

### 认证相关

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 请求示例

#### 注册

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "测试用户",
    "password": "Test@123456"
  }'
```

#### 登录

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test@123456"
  }'
```

## 测试用户

系统已预置测试用户：

- 邮箱：<EMAIL>
- 密码：Test@123456
- 角色：普通用户

- 邮箱：<EMAIL>
- 密码：Test@123456
- 角色：管理员

## 前端页面

- `/` - 首页（无需登录）
- `/login` - 登录页面
- `/register` - 注册页面
- `/tasks` - 任务列表（未登录查看公开任务，登录查看个人任务）
- `/create-task` - 创建任务（未登录显示登录提示）

## 安全特性

- 密码使用 bcrypt 加密存储
- JWT Token 认证（15 分钟有效期）
- 刷新 Token（7 天有效期）
- 密码强度验证
- 输入验证和 SQL 注入防护
- 会话管理

## 密码要求

- 至少 8 位字符
- 包含大写字母
- 包含小写字母
- 包含数字
- 包含特殊字符

## 中台项目特色

- **渐进式认证**：用户可以先体验核心功能，在需要时登录
- **权限分级**：公开任务 vs 个人任务
- **用户体验**：登录提示友好，不强制中断用户流程
- **数据隔离**：个人数据与公开数据分离

## 后续扩展

- [ ] 邮箱验证功能
- [ ] 密码重置功能
- [ ] 第三方登录（微信、QQ 等）
- [ ] 用户头像上传
- [ ] 用户权限管理
- [ ] 记住我功能
- [ ] 登录历史查看

## 注意事项

1. 生产环境请使用强密码和密钥
2. 定期轮换 JWT 密钥
3. 启用 HTTPS 确保传输安全
4. 考虑添加验证码防止暴力破解
5. 监控登录异常行为
