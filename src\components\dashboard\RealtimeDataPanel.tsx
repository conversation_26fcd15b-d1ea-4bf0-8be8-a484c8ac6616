'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area 
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { tradingApi } from '@/lib/api';

interface RealtimeDataPanelProps {
  ticker: string;
  analysisDate: string;
}

export function RealtimeDataPanel({ ticker, analysisDate }: RealtimeDataPanelProps) {
  const [activeDataType, setActiveDataType] = useState('stock');

  // 获取股票数据
  const { data: stockData, isLoading: stockLoading } = useQuery({
    queryKey: ['stockData', ticker, analysisDate],
    queryFn: () => tradingApi.getStockData(ticker, analysisDate),
    refetchInterval: 30000, // 每30秒刷新
  });

  // 获取新闻数据
  const { data: newsData, isLoading: newsLoading } = useQuery({
    queryKey: ['newsData', ticker, analysisDate],
    queryFn: () => tradingApi.getNewsData(ticker, analysisDate),
    refetchInterval: 60000, // 每分钟刷新
  });

  // 获取技术指标数据
  const { data: technicalData, isLoading: technicalLoading } = useQuery({
    queryKey: ['technicalData', ticker, analysisDate],
    queryFn: () => tradingApi.getTechnicalIndicators(ticker, analysisDate),
    refetchInterval: 30000,
  });

  // 获取基本面数据
  const { data: fundamentalsData, isLoading: fundamentalsLoading } = useQuery({
    queryKey: ['fundamentalsData', ticker, analysisDate],
    queryFn: () => tradingApi.getFundamentalsData(ticker, analysisDate),
    refetchInterval: 300000, // 每5分钟刷新
  });

  const dataTypes = [
    { id: 'stock', name: '股价数据', icon: '📈' },
    { id: 'technical', name: '技术指标', icon: '📊' },
    { id: 'news', name: '新闻数据', icon: '📰' },
    { id: 'fundamentals', name: '基本面', icon: '📋' },
  ];

  const renderStockData = () => {
    if (stockLoading) {
      return <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>;
    }

    if (!stockData || !stockData.priceHistory) {
      return <div className="text-center text-slate-500 py-8">暂无股价数据</div>;
    }

    return (
      <div className="space-y-6">
        {/* 当前价格信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              ${stockData.currentPrice?.toFixed(2) || 'N/A'}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">当前价格</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              (stockData.change || 0) >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stockData.change >= 0 ? '+' : ''}{stockData.change?.toFixed(2) || 'N/A'}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">涨跌额</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              (stockData.changePercent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stockData.changePercent >= 0 ? '+' : ''}{stockData.changePercent?.toFixed(2) || 'N/A'}%
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">涨跌幅</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {stockData.volume?.toLocaleString() || 'N/A'}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">成交量</div>
          </div>
        </div>

        {/* 价格走势图 */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={stockData.priceHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip 
                formatter={(value: any) => [`$${value.toFixed(2)}`, '价格']}
                labelFormatter={(label) => `时间: ${label}`}
              />
              <Area 
                type="monotone" 
                dataKey="price" 
                stroke="#3b82f6" 
                fill="#3b82f6" 
                fillOpacity={0.1}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderTechnicalData = () => {
    if (technicalLoading) {
      return <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>;
    }

    if (!technicalData || !technicalData.indicators) {
      return <div className="text-center text-slate-500 py-8">暂无技术指标数据</div>;
    }

    return (
      <div className="space-y-6">
        {/* 技术指标概览 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(technicalData.indicators).map(([key, value]) => (
            <div key={key} className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <div className="text-lg font-bold text-slate-900 dark:text-white">
                {typeof value === 'number' ? value.toFixed(2) : String(value ?? '')}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                {key.toUpperCase()}
              </div>
            </div>
          ))}
        </div>

        {/* 技术指标图表 */}
        {technicalData.chartData && (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={technicalData.chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="rsi" stroke="#8884d8" name="RSI" />
                <Line type="monotone" dataKey="macd" stroke="#82ca9d" name="MACD" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>
    );
  };

  const renderNewsData = () => {
    if (newsLoading) {
      return <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>;
    }

    if (!newsData || !newsData.articles) {
      return <div className="text-center text-slate-500 py-8">暂无新闻数据</div>;
    }

    return (
      <div className="space-y-4">
        {newsData.articles.map((article: any, index: number) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-medium text-slate-900 dark:text-white line-clamp-2">
                {article.title}
              </h4>
              <span className="text-xs text-slate-500 ml-4 whitespace-nowrap">
                {new Date(article.publishedAt).toLocaleString()}
              </span>
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-3 mb-2">
              {article.description}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-xs text-slate-500">{article.source}</span>
              {article.sentiment && (
                <span className={`text-xs px-2 py-1 rounded-full ${
                  article.sentiment === 'positive' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : article.sentiment === 'negative'
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    : 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-400'
                }`}>
                  {article.sentiment === 'positive' ? '积极' : 
                   article.sentiment === 'negative' ? '消极' : '中性'}
                </span>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    );
  };

  const renderFundamentalsData = () => {
    if (fundamentalsLoading) {
      return <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>;
    }

    if (!fundamentalsData) {
      return <div className="text-center text-slate-500 py-8">暂无基本面数据</div>;
    }

    return (
      <div className="space-y-6">
        {/* 财务指标 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { key: 'pe', label: '市盈率', value: fundamentalsData.pe },
            { key: 'pb', label: '市净率', value: fundamentalsData.pb },
            { key: 'roe', label: 'ROE', value: fundamentalsData.roe },
            { key: 'roa', label: 'ROA', value: fundamentalsData.roa },
          ].map((metric) => (
            <div key={metric.key} className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <div className="text-lg font-bold text-slate-900 dark:text-white">
                {metric.value ? metric.value.toFixed(2) : 'N/A'}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                {metric.label}
              </div>
            </div>
          ))}
        </div>

        {/* 财务报表摘要 */}
        {fundamentalsData.financials && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">收入</h4>
              <div className="text-2xl font-bold text-green-600">
                ${fundamentalsData.financials.revenue?.toLocaleString() || 'N/A'}
              </div>
            </div>
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">净利润</h4>
              <div className="text-2xl font-bold text-blue-600">
                ${fundamentalsData.financials.netIncome?.toLocaleString() || 'N/A'}
              </div>
            </div>
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">总资产</h4>
              <div className="text-2xl font-bold text-purple-600">
                ${fundamentalsData.financials.totalAssets?.toLocaleString() || 'N/A'}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderContent = () => {
    switch (activeDataType) {
      case 'stock':
        return renderStockData();
      case 'technical':
        return renderTechnicalData();
      case 'news':
        return renderNewsData();
      case 'fundamentals':
        return renderFundamentalsData();
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* 数据类型选择 */}
      <div className="flex space-x-2 overflow-x-auto">
        {dataTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => setActiveDataType(type.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
              activeDataType === type.id
                ? 'bg-blue-600 text-white'
                : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
            }`}
          >
            <span>{type.icon}</span>
            <span>{type.name}</span>
          </button>
        ))}
      </div>

      {/* 数据内容 */}
      <Card>
        <CardHeader>
          <CardTitle>
            {dataTypes.find(t => t.id === activeDataType)?.name} - {ticker}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
}
