// 会话管理测试工具
import { sessionManager } from './session';

// 测试会话管理功能
export function testSessionManagement() {
  if (typeof window === 'undefined') {
    console.log('Session tests can only run in browser environment');
    return;
  }

  console.log('=== Testing Session Management ===');

  // 测试设置会话
  const testSessionId = 'test-session-123';
  sessionManager.setSession(testSessionId);
  console.log('✓ Session set:', sessionManager.getSession());

  // 测试获取会话
  const retrievedSession = sessionManager.getSession();
  console.log('✓ Session retrieved:', retrievedSession === testSessionId ? 'PASS' : 'FAIL');

  // 测试设置用户信息
  const testUser = { id: 1, username: 'testuser', email: '<EMAIL>' };
  sessionManager.setUser(testUser);
  console.log('✓ User set:', sessionManager.getUser());

  // 测试认证状态
  console.log('✓ Is authenticated:', sessionManager.isAuthenticated());

  // 测试清除会话
  sessionManager.clearSession();
  console.log('✓ Session cleared:', sessionManager.getSession() === null ? 'PASS' : 'FAIL');
  console.log('✓ User cleared:', sessionManager.getUser() === null ? 'PASS' : 'FAIL');
  console.log(
    '✓ Is authenticated after clear:',
    !sessionManager.isAuthenticated() ? 'PASS' : 'FAIL'
  );

  console.log('=== Session Management Tests Complete ===');
}

// 在浏览器控制台运行测试
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.testSession = testSessionManagement;
}
