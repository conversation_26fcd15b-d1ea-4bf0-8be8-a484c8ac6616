# 交易分析数据库

## 概述

这是一个简化的交易分析任务数据库，采用 Task -> Message 直接关联的结构，移除了 conversation 概念，使系统更加简洁高效。

## 数据库结构

### 核心表

1. **tasks** - 任务表
   - 记录分析任务的基本信息
   - 包含任务状态、配置参数、时间戳等

2. **messages** - 消息表
   - 记录任务执行过程中的消息
   - 直接关联到 task_id，支持不同类型的消息

3. **tool_calls** - 工具调用表
   - 记录分析过程中的工具调用
   - 包含输入参数、输出结果、执行时间等

4. **analysis_results** - 分析结果表
   - 记录最终的分析结果
   - 支持不同类型的分析结果

5. **analysis_steps** - 分析步骤表
   - 记录分析过程中的详细步骤
   - 包含步骤状态、进度、性能指标等

6. **system_logs** - 系统日志表
   - 记录系统级别的操作日志

### 视图

- **task_overview** - 任务概览视图
  - 提供任务的统计信息和概览数据

### 存储过程

- **CreateAnalysisTask** - 创建分析任务
- **StartTaskExecution** - 开始任务执行

## 安装和使用

### 1. 创建数据库

```sql
-- 方法1：直接执行schema文件
mysql -u root -p < database/schema.sql

-- 方法2：使用初始化脚本
mysql -u root -p < database/init_database.sql
```

### 2. 验证安装

```sql
USE trading_analysis;
SHOW TABLES;
SELECT COUNT(*) FROM tasks;
```

### 3. 基本使用

#### 创建任务
```sql
CALL CreateAnalysisTask(
    UUID(),                    -- task_id
    'AAPL',                   -- ticker
    'Apple股票分析',           -- title
    '分析Apple公司股票',       -- description
    '{"type": "comprehensive"}', -- config
    'user1',                  -- created_by
    'medium',                 -- research_depth
    '1m'                      -- analysis_period
);
```

#### 查询任务
```sql
-- 查看所有任务
SELECT * FROM task_overview;

-- 查看特定任务的消息
SELECT * FROM messages WHERE task_id = 'your-task-id' ORDER BY sequence_number;
```

#### 添加消息
```sql
INSERT INTO messages (
    message_id, task_id, message_type, content, sequence_number
) VALUES (
    UUID(), 'your-task-id', 'system', '任务开始执行', 1
);
```

## 新流程说明

### 简化的架构
```
旧架构: Task -> Conversation -> Message
新架构: Task -> Message (直接关联)
```

### 主要优势

1. **简化的数据结构**
   - 减少了一个中间层（conversation）
   - 数据关系更直接清晰
   - 减少了数据冗余

2. **更好的性能**
   - 减少了 JOIN 查询
   - 索引更简单高效
   - 查询路径更短

3. **更容易维护**
   - 代码逻辑更简单
   - 错误处理更直接
   - 调试更容易

### 任务流程

1. **创建任务** - 用户创建分析任务
2. **启动任务** - 系统开始执行任务
3. **记录消息** - 执行过程中记录各种消息
4. **保存结果** - 保存分析结果和步骤

## 配置说明

### 研究深度 (research_depth)
- `shallow` - 浅层分析，快速概览
- `medium` - 中等深度，平衡速度和质量
- `deep` - 深度分析，详细全面

### 分析周期 (analysis_period)
- `1d` - 1天
- `1w` - 1周
- `1m` - 1个月
- `3m` - 3个月
- `6m` - 6个月
- `1y` - 1年
- `custom` - 自定义

### 消息类型 (message_type)
- `human` - 用户消息
- `ai` - AI回复
- `system` - 系统消息
- `tool` - 工具调用消息

### 任务状态 (status)
- `pending` - 等待执行
- `running` - 正在执行
- `completed` - 已完成
- `failed` - 执行失败
- `cancelled` - 已取消

## 维护和监控

### 清理旧数据
```sql
-- 删除30天前的已完成任务
DELETE FROM tasks 
WHERE status = 'completed' 
AND completed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 性能监控
```sql
-- 查看任务执行统计
SELECT 
    status,
    COUNT(*) as count,
    AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)) as avg_duration
FROM tasks 
WHERE completed_at IS NOT NULL
GROUP BY status;
```

### 备份建议
```bash
# 备份整个数据库
mysqldump -u root -p trading_analysis > backup_$(date +%Y%m%d).sql

# 只备份数据（不包含结构）
mysqldump -u root -p --no-create-info trading_analysis > data_backup_$(date +%Y%m%d).sql
```

## 故障排除

### 常见问题

1. **外键约束错误**
   - 确保引用的 task_id 存在
   - 检查字符编码是否一致

2. **JSON 字段错误**
   - 确保 JSON 格式正确
   - 使用 JSON_VALID() 函数验证

3. **时区问题**
   - 确保数据库时区设置正确
   - 使用 UTC 时间存储

### 调试技巧

```sql
-- 检查表结构
DESCRIBE tasks;

-- 检查索引
SHOW INDEX FROM messages;

-- 检查外键约束
SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'trading_analysis';
```

## 版本历史

- **v2.0** - 移除 conversation 概念，简化架构
- **v1.0** - 初始版本，包含 conversation 概念
