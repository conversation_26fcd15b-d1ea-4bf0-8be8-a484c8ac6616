/* 自定义动画样式 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 4s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate 20s linear infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

/* 背景粒子动画 */
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

.particle-1 {
  width: 4px;
  height: 4px;
  background: rgba(59, 130, 246, 0.3);
  top: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite, glow 3s ease-in-out infinite;
}

.particle-2 {
  width: 2px;
  height: 2px;
  background: rgba(147, 51, 234, 0.4);
  top: 60%;
  right: 15%;
  animation: float 6s ease-in-out infinite reverse, pulse-slow 4s ease-in-out infinite;
}

.particle-3 {
  width: 3px;
  height: 3px;
  background: rgba(6, 182, 212, 0.3);
  bottom: 30%;
  left: 20%;
  animation: float 10s ease-in-out infinite, glow 5s ease-in-out infinite;
}

.particle-4 {
  width: 1px;
  height: 1px;
  background: rgba(168, 85, 247, 0.5);
  top: 40%;
  right: 30%;
  animation: float 7s ease-in-out infinite, pulse-slow 2s ease-in-out infinite;
}

/* 渐变背景动画 */
.gradient-bg {
  background: linear-gradient(-45deg, #131520, #1a1f2e, #0f1419, #1e2139);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 卡片悬浮效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* 光晕效果 */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.1),
    rgba(147, 51, 234, 0.1),
    rgba(6, 182, 212, 0.1),
    rgba(168, 85, 247, 0.1)
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::before {
  opacity: 1;
}

/* 股票相关动画 */
@keyframes stock-rise {
  0% {
    transform: translateY(0px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: translateY(0px);
    opacity: 0.8;
  }
}

@keyframes number-scroll {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-20px);
  }
}

@keyframes chart-draw {
  0% {
    stroke-dashoffset: 100%;
  }
  100% {
    stroke-dashoffset: 0%;
  }
}

@keyframes ticker-flash {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.animate-stock-rise {
  animation: stock-rise 3s ease-in-out infinite;
}

.animate-number-scroll {
  animation: number-scroll 4s linear infinite;
}

.animate-chart-draw {
  animation: chart-draw 2s ease-in-out infinite alternate;
}

.animate-ticker-flash {
  animation: ticker-flash 2s ease-in-out infinite;
}

/* K线图特效 */
.kline-candle {
  transition: all 0.3s ease;
}

.kline-candle:hover {
  opacity: 1 !important;
  transform: scale(1.1);
}

/* 股价曲线特效 */
.stock-curve {
  filter: drop-shadow(0 0 3px currentColor);
}

/* 金融图标特效 */
.financial-icon {
  transition: all 0.3s ease;
}

.financial-icon:hover {
  transform: scale(1.2);
  filter: drop-shadow(0 0 5px currentColor);
}
