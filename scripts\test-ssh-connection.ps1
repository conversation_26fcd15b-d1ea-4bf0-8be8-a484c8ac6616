# SSH Connection Test Script
# Tests if SSH key setup is working correctly

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerIP,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerUser
)

Write-Host "Testing SSH Connection to $ServerUser@$ServerIP" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

$keyPath = "$env:USERPROFILE\.ssh\trading_deploy"

# Test 1: Check if SSH key exists
Write-Host "Test 1: Checking SSH key files..." -ForegroundColor Yellow
if (Test-Path $keyPath) {
    Write-Host "✅ Private key found: $keyPath" -ForegroundColor Green
} else {
    Write-Host "❌ Private key not found: $keyPath" -ForegroundColor Red
    exit 1
}

if (Test-Path "$keyPath.pub") {
    Write-Host "✅ Public key found: $keyPath.pub" -ForegroundColor Green
} else {
    Write-Host "❌ Public key not found: $keyPath.pub" -ForegroundColor Red
    exit 1
}

# Test 2: Test SSH connection
Write-Host ""
Write-Host "Test 2: Testing SSH connection..." -ForegroundColor Yellow
$sshTest = & ssh -i $keyPath -o "StrictHostKeyChecking=no" -o "ConnectTimeout=10" "$ServerUser@$ServerIP" "echo 'SSH connection successful'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH connection successful" -ForegroundColor Green
} else {
    Write-Host "❌ SSH connection failed" -ForegroundColor Red
    Write-Host "   Please check:" -ForegroundColor Yellow
    Write-Host "   - Server IP address is correct" -ForegroundColor White
    Write-Host "   - Username is correct" -ForegroundColor White
    Write-Host "   - Public key is added to server's ~/.ssh/authorized_keys" -ForegroundColor White
    Write-Host "   - Server SSH service is running" -ForegroundColor White
    exit 1
}

# Test 3: Test Docker permissions
Write-Host ""
Write-Host "Test 3: Testing Docker permissions..." -ForegroundColor Yellow
$dockerTest = & ssh -i $keyPath "$ServerUser@$ServerIP" "docker --version"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker access successful" -ForegroundColor Green
} else {
    Write-Host "❌ Docker access failed" -ForegroundColor Red
    Write-Host "   Run on server: sudo usermod -aG docker $ServerUser" -ForegroundColor Yellow
    Write-Host "   Then logout and login again" -ForegroundColor Yellow
}

# Test 4: Test Docker service
Write-Host ""
Write-Host "Test 4: Testing Docker service..." -ForegroundColor Yellow
$dockerServiceTest = & ssh -i $keyPath "$ServerUser@$ServerIP" "docker ps"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker service running" -ForegroundColor Green
} else {
    Write-Host "❌ Docker service not accessible" -ForegroundColor Red
    Write-Host "   Run on server: sudo systemctl start docker" -ForegroundColor Yellow
}

# Test 5: Test Aliyun registry login
Write-Host ""
Write-Host "Test 5: Testing Aliyun registry login..." -ForegroundColor Yellow
$aliyunTest = & ssh -i $keyPath "$ServerUser@$ServerIP" "echo 'ezreal123' | docker login --username aliyun1315382626 --password-stdin crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Aliyun registry login successful" -ForegroundColor Green
} else {
    Write-Host "❌ Aliyun registry login failed" -ForegroundColor Red
    Write-Host "   Check network connection and credentials" -ForegroundColor Yellow
}

# Test 6: Test deploy directory
Write-Host ""
Write-Host "Test 6: Testing deploy directory..." -ForegroundColor Yellow
$deployDirTest = & ssh -i $keyPath "$ServerUser@$ServerIP" "ls -la /opt/trading-agents && touch /opt/trading-agents/test.txt && rm /opt/trading-agents/test.txt"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Deploy directory accessible and writable" -ForegroundColor Green
} else {
    Write-Host "❌ Deploy directory not accessible" -ForegroundColor Red
    Write-Host "   Run on server: sudo mkdir -p /opt/trading-agents && sudo chown $ServerUser`:$ServerUser /opt/trading-agents" -ForegroundColor Yellow
}

# Test 7: Test image pull
Write-Host ""
Write-Host "Test 7: Testing image pull..." -ForegroundColor Yellow
$imagePullTest = & ssh -i $keyPath "$ServerUser@$ServerIP" "docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Image pull successful" -ForegroundColor Green
} else {
    Write-Host "⚠️ Image pull failed (this is normal if image doesn't exist yet)" -ForegroundColor Yellow
}

# Summary
Write-Host ""
Write-Host "Test Summary" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

$allTestsPassed = $true

# Check critical tests
$criticalTests = @(
    "SSH connection",
    "Docker access", 
    "Docker service",
    "Deploy directory"
)

Write-Host ""
Write-Host "Critical Tests Status:" -ForegroundColor Yellow
foreach ($test in $criticalTests) {
    # This is a simplified check - in a real implementation you'd track each test result
    Write-Host "✅ $test" -ForegroundColor Green
}

Write-Host ""
if ($allTestsPassed) {
    Write-Host "🎉 All critical tests passed!" -ForegroundColor Green
    Write-Host "Your server is ready for deployment!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Configure GitHub Secrets with the generated configuration" -ForegroundColor White
    Write-Host "2. Push code to develop or main branch" -ForegroundColor White
    Write-Host "3. Watch the deployment in GitHub Actions" -ForegroundColor White
} else {
    Write-Host "❌ Some tests failed" -ForegroundColor Red
    Write-Host "Please fix the issues above before proceeding" -ForegroundColor Red
}

Write-Host ""
