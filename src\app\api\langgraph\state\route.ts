import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 使用 LangGraph 服务获取会话状态
    const { langGraphService } = await import('@/lib/langgraph-server');
    const session = langGraphService.getSession(threadId);

    if (!session) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json(session);
  } catch (error) {
    console.error('获取状态失败:', error);
    return NextResponse.json(
      { error: '获取状态失败，请稍后重试' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { threadId } = await request.json();

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 使用 LangGraph 服务清除会话
    const { langGraphService } = await import('@/lib/langgraph-server');
    langGraphService.clearSession(threadId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('清除状态失败:', error);
    return NextResponse.json(
      { error: '清除状态失败，请稍后重试' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 使用 LangGraph 服务清除会话
    const { langGraphService } = await import('@/lib/langgraph-server');
    langGraphService.clearSession(threadId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除状态失败:', error);
    return NextResponse.json(
      { error: '删除状态失败，请稍后重试' },
      { status: 500 }
    );
  }
}
