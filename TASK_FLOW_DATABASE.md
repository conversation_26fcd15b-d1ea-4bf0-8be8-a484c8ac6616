# 交易分析任务全流程记录系统

## 概述

这个系统设计用于记录交易分析任务的全流程，包括任务创建、分析过程中的多轮对话、工具调用、分析步骤和最终结果。所有的交互过程都会被完整记录到数据库中，方便后续分析和审计。

## 数据库设计

### 核心表结构

1. **tasks** - 任务表
   - 记录每个分析任务的基本信息
   - 包含任务状态、配置参数、时间戳等

2. **conversations** - 对话会话表
   - 记录任务中的对话会话
   - 支持一个任务多个对话会话

3. **messages** - 消息表
   - 记录具体的对话消息
   - 支持人类、AI、系统、工具等不同类型的消息

4. **tool_calls** - 工具调用表
   - 记录分析过程中的工具调用
   - 包含输入参数、输出结果、执行时间等

5. **analysis_results** - 分析结果表
   - 记录最终的分析结果
   - 支持不同类型的分析结果（基本面、技术面、情绪等）

6. **analysis_steps** - 分析步骤表
   - 记录分析过程中的详细步骤
   - 包含步骤状态、进度、性能指标等

7. **system_logs** - 系统日志表
   - 记录系统级别的操作日志

### 视图和存储过程

- **task_overview** - 任务概览视图，提供任务的统计信息
- **CreateAnalysisTask** - 创建任务的存储过程
- **StartConversation** - 开始对话的存储过程

## 使用方式

### 1. 数据库初始化

```sql
-- 执行 database/schema.sql 中的所有SQL语句
SOURCE database/schema.sql;
```

### 2. 前端集成

```typescript
import { TradingAgent } from '@/lib/langgraph';
import { taskFlowDb } from '@/lib/task-flow-database';

// 创建交易代理实例
const agent = new TradingAgent();

// 初始化任务
const taskId = await agent.initializeTask(
  'AAPL',
  'Apple股票分析',
  '全面分析Apple公司股票',
  { analysis_type: 'comprehensive' }
);

// 开始对话
const conversationId = await agent.startConversation();

// 执行分析（会自动记录所有步骤）
const result = await agent.analyze('AAPL', config);

// 完成任务
await agent.completeTask();
```

### 3. 查询任务历史

```typescript
// 获取任务完整历史
const history = await agent.getTaskHistory(taskId);

// 获取任务概览
const overview = await agent.getTaskOverview(taskId);

// 查询任务列表
const tasks = await taskFlowDb.queryTasks({
  status: ['completed'],
  ticker: 'AAPL',
  limit: 10
});
```

## 功能特性

### 1. 完整的流程记录
- 记录任务从创建到完成的全流程
- 记录所有的用户交互和系统响应
- 记录工具调用的详细信息

### 2. 多层级关联
- 任务 → 对话 → 消息的层级关系
- 支持一个任务多个对话会话
- 支持消息的父子关系（对话树）

### 3. 实时状态跟踪
- 任务状态实时更新
- 分析步骤进度跟踪
- 工具调用状态监控

### 4. 丰富的元数据
- JSON格式存储复杂数据结构
- 性能指标记录
- 错误信息和调试信息

### 5. 查询和分析
- 提供视图简化常用查询
- 支持复杂的过滤和分页
- 统计分析功能

## 演示页面

访问 `/task-flow-demo` 页面可以看到完整的演示：

1. **创建新任务** - 初始化一个新的分析任务
2. **执行分析** - 运行股票分析并记录所有步骤
3. **模拟对话** - 模拟用户与AI的多轮对话
4. **查看历史** - 查看任务的完整执行历史
5. **实时监控** - 监控任务状态和进度

## API接口

### 任务管理
- `POST /api/database/tasks` - 创建任务
- `GET /api/database/tasks` - 查询任务列表
- `GET /api/database/tasks/{id}` - 获取任务详情
- `PATCH /api/database/tasks/{id}` - 更新任务状态

### 对话管理
- `POST /api/database/conversations` - 开始对话
- `GET /api/database/conversations/{id}` - 获取对话详情
- `PATCH /api/database/conversations/{id}/end` - 结束对话

### 消息管理
- `POST /api/database/messages` - 添加消息
- `GET /api/database/conversations/{id}/messages` - 获取对话消息

### 工具调用
- `POST /api/database/tool-calls` - 记录工具调用
- `PATCH /api/database/tool-calls/{id}` - 更新工具调用结果

### 分析结果
- `POST /api/database/analysis-results` - 保存分析结果
- `GET /api/database/tasks/{id}/analysis-results` - 获取任务分析结果

## 配置要求

### 数据库配置
需要配置MySQL数据库连接，由于当前有认证模式问题，建议：

1. 更新MySQL客户端驱动
2. 或者修改MySQL服务器配置：
   ```sql
   ALTER USER 'username'@'localhost' IDENTIFIED WITH mysql_native_password BY 'password';
   ```

### 前端依赖
需要安装以下依赖：
```bash
npm install uuid
npm install @types/uuid
```

## 扩展功能

### 1. 实时通知
可以集成WebSocket来实现任务状态的实时推送

### 2. 数据可视化
可以基于记录的数据创建分析仪表板

### 3. 审计日志
系统已支持详细的审计日志记录

### 4. 性能监控
记录了执行时间和性能指标，可用于优化

### 5. 数据导出
支持将任务数据导出为各种格式

## 注意事项

1. **数据安全** - 敏感数据应该加密存储
2. **性能优化** - 大量数据时需要考虑分表分库
3. **数据清理** - 定期清理过期的任务数据
4. **备份恢复** - 定期备份重要的任务数据
5. **权限控制** - 实现用户级别的数据访问控制

## 技术栈

- **数据库**: MySQL 8.0+
- **后端**: Next.js API Routes
- **前端**: React + TypeScript
- **状态管理**: 本地状态 + 数据库存储
- **UI组件**: 自定义组件库
