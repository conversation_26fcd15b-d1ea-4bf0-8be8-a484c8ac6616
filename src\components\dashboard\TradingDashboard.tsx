'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AgentStatusPanel } from './AgentStatusPanel';
import { AnalysisProgress } from './AnalysisProgress';
import { RealtimeDataPanel } from './RealtimeDataPanel';
import { ReportViewer } from './ReportViewer';
import { TradingDecision } from './TradingDecision';
import { useTradingAnalysis } from '@/hooks/useTradingAnalysis';
import { LangGraphChat } from '@/components/langgraph/LangGraphChat';
import { WorkflowVisualization, defaultTradingWorkflowNodes, defaultTradingWorkflowEdges } from '@/components/langgraph/WorkflowVisualization';

interface TradingDashboardProps {
  config: any;
  onBack: () => void;
}

export function TradingDashboard({ config, onBack }: TradingDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const hasStartedAnalysis = useRef(false);

  const {
    analysisState,
    agentStatuses,
    reports,
    finalDecision,
    startAnalysis,
    isAnalyzing
  } = useTradingAnalysis(config);

  useEffect(() => {
    // 自动开始分析 - 只在组件挂载时执行一次
    if (!hasStartedAnalysis.current && !isAnalyzing) {
      hasStartedAnalysis.current = true;
      startAnalysis();
    }
  }, [startAnalysis, isAnalyzing]);

  const tabs = [
    { id: 'overview', name: '总览', icon: '📊' },
    { id: 'agents', name: '代理状态', icon: '🤖' },
    { id: 'data', name: '实时数据', icon: '📈' },
    { id: 'reports', name: '分析报告', icon: '📋' },
    { id: 'decision', name: '交易决策', icon: '💼' },
    { id: 'langgraph', name: 'LangGraph', icon: '🧠' },
    { id: 'workflow', name: '工作流', icon: '🔄' },
  ];

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            {config.ticker} 交易分析
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            分析日期: {config.analysisDate}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {isAnalyzing && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">分析进行中...</span>
            </div>
          )}
        </div>
      </motion.div>

      {/* Progress Bar */}
      <AnalysisProgress 
        currentStage={analysisState.currentStage}
        progress={analysisState.progress}
        isComplete={analysisState.isComplete}
      />

      {/* Tab Navigation */}
      <div className="border-b border-slate-200 dark:border-slate-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>分析概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">股票标识:</span>
                    <span className="font-semibold">{config.ticker}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">分析日期:</span>
                    <span className="font-semibold">{config.analysisDate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">选择的分析师:</span>
                    <span className="font-semibold">{config.selectedAnalysts.length}个</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">研究深度:</span>
                    <span className="font-semibold">
                      {config.researchDepth === 'quick' ? '快速' : 
                       config.researchDepth === 'standard' ? '标准' : '深度'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">在线工具:</span>
                    <span className={`font-semibold ${config.onlineTools ? 'text-green-600' : 'text-red-600'}`}>
                      {config.onlineTools ? '启用' : '禁用'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>当前状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {analysisState.progress}%
                    </div>
                    <div className="text-slate-600 dark:text-slate-400">
                      分析进度
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      当前阶段: {analysisState.currentStage}
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${analysisState.progress}%` }}
                      />
                    </div>
                  </div>

                  {analysisState.isComplete && finalDecision && (
                    <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="text-sm font-medium text-green-800 dark:text-green-200">
                        分析完成！
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400 mt-1">
                        交易决策已生成，请查看"交易决策"标签页
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'agents' && (
          <AgentStatusPanel 
            agentStatuses={agentStatuses}
            selectedAnalysts={config.selectedAnalysts}
          />
        )}

        {activeTab === 'data' && (
          <RealtimeDataPanel 
            ticker={config.ticker}
            analysisDate={config.analysisDate}
          />
        )}

        {activeTab === 'reports' && (
          <ReportViewer 
            reports={reports}
            selectedAnalysts={config.selectedAnalysts}
          />
        )}

        {activeTab === 'decision' && (
          <TradingDecision
            decision={finalDecision}
            isComplete={analysisState.isComplete}
            ticker={config.ticker}
          />
        )}

        {activeTab === 'langgraph' && (
          <LangGraphChat
            ticker={config.ticker}
            onAnalysisComplete={(result) => {
              console.log('LangGraph分析完成:', result);
              // 可以将结果集成到主分析流程中
            }}
          />
        )}

        {activeTab === 'workflow' && (
          <WorkflowVisualization
            nodes={defaultTradingWorkflowNodes}
            edges={defaultTradingWorkflowEdges}
            currentNode={analysisState.currentStage}
          />
        )}
      </motion.div>
    </div>
  );
}
