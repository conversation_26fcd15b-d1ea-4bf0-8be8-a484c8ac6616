# Server Configuration Script
# Automatically configures SSH key on Ubuntu server

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerIP,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerUser
)

Write-Host "Configuring SSH Key on Ubuntu Server" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

$keyPath = "$env:USERPROFILE\.ssh\trading_deploy"
$publicKeyPath = "$keyPath.pub"

# Check if keys exist
if (-not (Test-Path $publicKeyPath)) {
    Write-Host "❌ Public key not found. Please run simple-ssh-setup.ps1 first." -ForegroundColor Red
    exit 1
}

# Read public key
$publicKey = Get-Content $publicKeyPath -Raw
Write-Host "Public key to be installed:" -ForegroundColor Yellow
Write-Host $publicKey -ForegroundColor Cyan
Write-Host ""

# Copy public key to clipboard
$publicKey | Set-Clipboard
Write-Host "✅ Public key copied to clipboard" -ForegroundColor Green
Write-Host ""

Write-Host "Server Configuration Steps:" -ForegroundColor Yellow
Write-Host "1. You will be prompted for the server password" -ForegroundColor White
Write-Host "2. The script will automatically configure SSH key access" -ForegroundColor White
Write-Host "3. After configuration, you can connect without password" -ForegroundColor White
Write-Host ""

# Create temporary script for server
$serverScript = @"
#!/bin/bash
echo "Configuring SSH key access..."

# Create .ssh directory
mkdir -p ~/.ssh

# Add public key to authorized_keys
echo '$publicKey' >> ~/.ssh/authorized_keys

# Remove duplicates
sort ~/.ssh/authorized_keys | uniq > ~/.ssh/authorized_keys.tmp
mv ~/.ssh/authorized_keys.tmp ~/.ssh/authorized_keys

# Set correct permissions
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys

# Verify configuration
echo "SSH key configuration completed"
echo "Authorized keys:"
cat ~/.ssh/authorized_keys

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    echo "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker ubuntu
    sudo systemctl start docker
    sudo systemctl enable docker
    echo "Docker installation completed"
else
    echo "Docker already installed"
    # Ensure user is in docker group
    sudo usermod -aG docker ubuntu
fi

# Install Docker Compose if not present
if ! command -v docker-compose &> /dev/null; then
    echo "Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)`-`$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    echo "Docker Compose installation completed"
else
    echo "Docker Compose already installed"
fi

# Create deployment directory
sudo mkdir -p /opt/trading-agents
sudo chown ubuntu:ubuntu /opt/trading-agents

# Configure firewall
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000
sudo ufw allow 5000
sudo ufw allow 8000
sudo ufw --force enable

echo "Server configuration completed successfully!"
echo "Please logout and login again to apply docker group changes"
"@

# Save script to temporary file
$tempScript = [System.IO.Path]::GetTempFileName() + ".sh"
$serverScript | Out-File -FilePath $tempScript -Encoding UTF8

Write-Host "Connecting to server and configuring..." -ForegroundColor Green

try {
    # Upload and execute script on server
    & scp $tempScript "$ServerUser@$ServerIP`:~/setup_server.sh"
    & ssh "$ServerUser@$ServerIP" "chmod +x ~/setup_server.sh && ~/setup_server.sh && rm ~/setup_server.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Server configuration completed successfully!" -ForegroundColor Green
        
        # Test SSH key connection
        Write-Host ""
        Write-Host "Testing SSH key connection..." -ForegroundColor Yellow
        
        Start-Sleep -Seconds 2
        
        $testResult = & ssh -i $keyPath -o "StrictHostKeyChecking=no" "$ServerUser@$ServerIP" "echo 'SSH key authentication successful'"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ SSH key authentication working!" -ForegroundColor Green
            Write-Host ""
            Write-Host "🎉 Configuration Complete!" -ForegroundColor Green
            Write-Host "=========================" -ForegroundColor Green
            Write-Host ""
            Write-Host "Your server is now configured with:" -ForegroundColor Yellow
            Write-Host "✅ SSH key authentication" -ForegroundColor Green
            Write-Host "✅ Docker and Docker Compose" -ForegroundColor Green
            Write-Host "✅ Deployment directory (/opt/trading-agents)" -ForegroundColor Green
            Write-Host "✅ Firewall configuration" -ForegroundColor Green
            Write-Host ""
            Write-Host "Next steps:" -ForegroundColor Yellow
            Write-Host "1. Run the test script: .\scripts\test-ssh-connection.ps1 -ServerIP '$ServerIP' -ServerUser '$ServerUser'" -ForegroundColor White
            Write-Host "2. Configure GitHub Secrets" -ForegroundColor White
            Write-Host "3. Push code to trigger deployment" -ForegroundColor White
        } else {
            Write-Host "⚠️ SSH key test failed. You may need to logout and login to the server." -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Server configuration failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error during configuration: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Clean up temporary file
    Remove-Item $tempScript -ErrorAction SilentlyContinue
}

Write-Host ""
