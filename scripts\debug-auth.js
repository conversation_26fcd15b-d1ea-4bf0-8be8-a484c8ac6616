#!/usr/bin/env node

/**
 * 认证系统调试脚本
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function debugAuth() {
  console.log('🔍 调试认证系统...\n');

  try {
    // 1. 测试注册
    console.log('1. 测试注册...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        username: '调试用户',
        password: 'Debug@123456',
      }),
    });

    const registerResult = await registerResponse.json();
    console.log('注册响应:', registerResponse.status, registerResult);

    // 2. 测试登录
    console.log('\n2. 测试登录...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Debug@123456',
      }),
    });

    const loginResult = await loginResponse.json();
    console.log('登录响应:', loginResponse.status, loginResult);

    // 3. 检查cookie
    console.log('\n3. 检查cookie...');
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Set-Cookie:', cookies);

    // 4. 测试获取用户信息
    console.log('\n4. 测试获取用户信息...');
    const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Cookie': cookies || '',
      },
    });

    const meResult = await meResponse.json();
    console.log('用户信息响应:', meResponse.status, meResult);

    // 5. 测试登出
    console.log('\n5. 测试登出...');
    const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Cookie': cookies || '',
      },
    });

    const logoutResult = await logoutResponse.json();
    console.log('登出响应:', logoutResponse.status, logoutResult);

  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

// 运行调试
if (require.main === module) {
  debugAuth();
}
