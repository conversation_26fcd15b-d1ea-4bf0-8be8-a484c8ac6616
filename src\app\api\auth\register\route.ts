import {
  generateTokens,
  hashPassword,
  setAuthCookies,
  validateEmail,
  validatePassword,
} from '@/lib/auth';
import { createUser, createUserSession, getUserByEmail, logUserActivity } from '@/lib/user-db';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { email, username, password } = await request.json();

    // 验证输入
    if (!email || !username || !password) {
      return NextResponse.json({ error: '请填写所有必填字段' }, { status: 400 });
    }

    // 验证邮箱格式
    if (!validateEmail(email)) {
      return NextResponse.json({ error: '请输入有效的邮箱地址' }, { status: 400 });
    }

    // 验证密码强度
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return NextResponse.json({ error: passwordValidation.message }, { status: 400 });
    }

    // 验证用户名长度
    if (username.length < 2 || username.length > 50) {
      return NextResponse.json({ error: '用户名长度必须在2-50个字符之间' }, { status: 400 });
    }

    // 检查邮箱是否已存在
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json({ error: '该邮箱已被注册' }, { status: 409 });
    }

    // 加密密码
    const passwordHash = await hashPassword(password);

    // 创建用户
    const userId = await createUser({
      email,
      username,
      password_hash: passwordHash,
      verification_token: uuidv4(), // 用于邮箱验证
    });

    if (!userId) {
      return NextResponse.json({ error: '注册失败，请稍后重试' }, { status: 500 });
    }

    // 获取客户端IP和User-Agent
    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 记录注册活动
    await logUserActivity(userId, 'register', ipAddress, userAgent, { email, username });

    // 生成token并设置cookie
    const { accessToken, refreshToken } = await generateTokens(userId, email);

    // 创建会话
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15分钟
    const refreshExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天

    await createUserSession(
      userId,
      sessionId,
      accessToken,
      refreshToken,
      expiresAt,
      refreshExpiresAt,
      ipAddress as string,
      userAgent
    );

    // 设置认证cookie
    await setAuthCookies(accessToken, refreshToken);

    return NextResponse.json({
      success: true,
      message: '注册成功',
      user: {
        id: userId,
        email,
        username,
        email_verified: false,
      },
      sessionId,
    });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
