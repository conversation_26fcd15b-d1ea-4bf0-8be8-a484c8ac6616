'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeftIcon, PlayIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { format } from 'date-fns';

interface AnalysisConfigFormProps {
  onSubmit: (config: any) => void;
  onBack: () => void;
}

export function AnalysisConfigForm({ onSubmit, onBack }: AnalysisConfigFormProps) {
  const [config, setConfig] = useState({
    ticker: 'NVDA',
    analysisDate: format(new Date(), 'yyyy-MM-dd'),
    selectedAnalysts: ['market', 'social', 'news', 'fundamentals'],
    llmProvider: 'openai',
    deepThinkLlm: 'o3-mini-high',
    quickThinkLlm: 'o3-mini-high',
    maxDebateRounds: 1,
    maxRiskDiscussRounds: 1,
    onlineTools: true,
    researchDepth: 'standard',
    useLangGraph: false,
    langGraphConfig: {
      enableWorkflow: true,
      enableMemory: true,
      enableTools: true,
    },
  });

  const analystOptions = [
    { id: 'market', name: '市场分析师', description: '技术指标和价格走势分析' },
    { id: 'social', name: '社交媒体分析师', description: '社交媒体情绪和舆论分析' },
    { id: 'news', name: '新闻分析师', description: '新闻事件和宏观经济分析' },
    { id: 'fundamentals', name: '基本面分析师', description: '财务报表和公司基本面分析' },
  ];

  const llmOptions = [
    { value: 'o3-mini-high', label: 'O3 Mini High' },
    { value: 'gpt-4o-mini', label: 'GPT-4O Mini' },
    { value: 'o4-mini', label: 'O4 Mini' },
  ];

  const researchDepthOptions = [
    { value: 'quick', label: '快速分析', description: '基础分析，适合快速决策' },
    { value: 'standard', label: '标准分析', description: '平衡的深度和速度' },
    { value: 'deep', label: '深度分析', description: '全面分析，需要更多时间' },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(config);
  };

  const handleAnalystToggle = (analystId: string) => {
    setConfig(prev => ({
      ...prev,
      selectedAnalysts: prev.selectedAnalysts.includes(analystId)
        ? prev.selectedAnalysts.filter(id => id !== analystId)
        : [...prev.selectedAnalysts, analystId]
    }));
  };

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            返回
          </Button>
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
            配置分析参数
          </h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>基础配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    股票标识
                  </label>
                  <input
                    type="text"
                    value={config.ticker}
                    onChange={(e) => setConfig(prev => ({ ...prev, ticker: e.target.value.toUpperCase() }))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如: NVDA, AAPL, TSLA"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    分析日期
                  </label>
                  <input
                    type="date"
                    value={config.analysisDate}
                    onChange={(e) => setConfig(prev => ({ ...prev, analysisDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analyst Selection */}
          <Card>
            <CardHeader>
              <CardTitle>选择分析师</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {analystOptions.map((analyst) => (
                  <div
                    key={analyst.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      config.selectedAnalysts.includes(analyst.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-slate-300 dark:border-slate-600 hover:border-slate-400'
                    }`}
                    onClick={() => handleAnalystToggle(analyst.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={config.selectedAnalysts.includes(analyst.id)}
                        onChange={() => handleAnalystToggle(analyst.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <div>
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          {analyst.name}
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {analyst.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Advanced Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>高级配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    深度思考模型
                  </label>
                  <select
                    value={config.deepThinkLlm}
                    onChange={(e) => setConfig(prev => ({ ...prev, deepThinkLlm: e.target.value }))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {llmOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    快速思考模型
                  </label>
                  <select
                    value={config.quickThinkLlm}
                    onChange={(e) => setConfig(prev => ({ ...prev, quickThinkLlm: e.target.value }))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {llmOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  研究深度
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {researchDepthOptions.map((option) => (
                    <div
                      key={option.value}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        config.researchDepth === option.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-slate-300 dark:border-slate-600 hover:border-slate-400'
                      }`}
                      onClick={() => setConfig(prev => ({ ...prev, researchDepth: option.value }))}
                    >
                      <div className="text-center">
                        <h4 className="font-medium text-slate-900 dark:text-white mb-1">
                          {option.label}
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {option.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="onlineTools"
                    checked={config.onlineTools}
                    onChange={(e) => setConfig(prev => ({ ...prev, onlineTools: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                  />
                  <label htmlFor="onlineTools" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    使用在线工具获取实时数据
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="useLangGraph"
                    checked={config.useLangGraph}
                    onChange={(e) => setConfig(prev => ({ ...prev, useLangGraph: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                  />
                  <label htmlFor="useLangGraph" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    启用 LangGraph 智能工作流
                  </label>
                </div>

                {config.useLangGraph && (
                  <div className="ml-7 space-y-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="enableWorkflow"
                        checked={config.langGraphConfig.enableWorkflow}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          langGraphConfig: { ...prev.langGraphConfig, enableWorkflow: e.target.checked }
                        }))}
                        className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <label htmlFor="enableWorkflow" className="text-xs text-slate-600 dark:text-slate-400">
                        启用工作流可视化
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="enableMemory"
                        checked={config.langGraphConfig.enableMemory}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          langGraphConfig: { ...prev.langGraphConfig, enableMemory: e.target.checked }
                        }))}
                        className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <label htmlFor="enableMemory" className="text-xs text-slate-600 dark:text-slate-400">
                        启用对话记忆
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="enableTools"
                        checked={config.langGraphConfig.enableTools}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          langGraphConfig: { ...prev.langGraphConfig, enableTools: e.target.checked }
                        }))}
                        className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <label htmlFor="enableTools" className="text-xs text-slate-600 dark:text-slate-400">
                        启用智能工具调用
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" size="lg" className="px-8">
              <PlayIcon className="h-5 w-5 mr-2" />
              开始分析
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
