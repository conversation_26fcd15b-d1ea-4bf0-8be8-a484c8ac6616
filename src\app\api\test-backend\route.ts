import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const backendUrl = process.env.BACK_END_URL || 'http://localhost:5000';
  
  try {
    console.log('测试后端连接:', backendUrl);
    
    // 尝试连接到后端的健康检查端点
    const healthUrl = `${backendUrl}/health`;
    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json({
        success: true,
        message: '后端连接成功',
        backendUrl,
        healthData: data,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: '后端响应异常',
        backendUrl,
        status: response.status,
        statusText: response.statusText,
      });
    }
  } catch (error) {
    console.error('后端连接失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '无法连接到后端服务',
      backendUrl,
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestions: [
        '1. 检查后端服务是否正在运行',
        '2. 确认后端端口是否正确',
        '3. 检查防火墙设置',
        '4. 验证 BACK_END_URL 环境变量',
      ],
    });
  }
}
