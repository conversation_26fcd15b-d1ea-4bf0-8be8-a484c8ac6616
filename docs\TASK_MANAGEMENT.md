# 任务管理页面使用指南

## 概述

任务管理页面 (`/tasks`) 提供了一个完整的界面来管理和操作数据库中的分析任务。该页面集成了任务查看、消息管理和分析启动功能。

## 功能特性

### 1. 任务列表显示
- **完整的任务信息**：显示任务ID、股票代码、标题、状态、研究深度、分析周期和创建时间
- **状态可视化**：使用颜色标签区分不同的任务状态
  - 🟢 已完成 (completed)
  - 🟡 待处理 (pending) 
  - 🔵 运行中 (running)
  - 🔴 失败 (failed)
- **统计概览**：页面顶部显示任务总数和各状态的统计信息

### 2. 操作功能

#### 详情查看
- 点击"详情"按钮查看任务的完整信息
- 包含任务配置、时间戳、错误信息等详细数据
- 模态框形式展示，便于快速查看

#### 消息查看
- 点击"消息"按钮查看任务相关的对话消息
- 支持不同类型的消息显示（用户、AI、系统、工具）
- 按时间顺序排列，包含元数据信息
- 如果任务没有消息，会显示相应提示

#### 任务启动
- 点击"开始"按钮启动分析任务
- 调用 `/api/langgraph/analyze` 接口
- 提供实时反馈和错误处理
- 启动成功后自动刷新任务列表

### 3. 用户体验优化
- **加载状态**：所有操作都有相应的加载指示器
- **错误处理**：友好的错误提示和重试机制
- **响应式设计**：适配不同屏幕尺寸
- **实时反馈**：使用 toast 通知提供操作反馈

## 技术实现

### 数据库集成
- 使用 `/api/database/tasks` 获取任务列表
- 使用 `/api/database/messages` 获取任务消息
- 支持参数化查询和分页

### API 接口
```typescript
// 获取任务列表
GET /api/database/tasks

// 获取任务消息
GET /api/database/messages?task_id={taskId}

// 启动分析任务
POST /api/langgraph/analyze
{
  ticker: string,
  config: object,
  conversation_id?: string
}
```

### 状态管理
- 使用 React Hooks 管理组件状态
- 支持多个模态框的状态控制
- 优化的错误状态处理

## 使用方法

### 1. 访问页面
```
http://localhost:3001/tasks
```

### 2. 查看任务
- 页面加载时自动获取任务列表
- 使用"刷新列表"按钮手动更新数据
- 查看页面顶部的统计信息了解整体情况

### 3. 操作任务
1. **查看详情**：点击任务行的"详情"按钮
2. **查看消息**：点击"消息"按钮查看对话记录
3. **启动分析**：点击"开始"按钮启动新的分析

### 4. 错误处理
- 如果后端服务未运行，启动分析会显示相应错误
- 网络错误会有友好的提示信息
- 可以使用重试按钮重新加载数据

## 开发说明

### 依赖项
- `react-hot-toast`：用于通知提示
- `date-fns`：用于日期格式化
- `@types/database`：数据库类型定义

### 文件结构
```
src/app/tasks/
├── page.tsx              # 主页面组件
└── ...

src/app/api/database/
├── tasks/route.ts        # 任务API
└── messages/route.ts     # 消息API

src/app/api/langgraph/
└── analyze/route.ts      # 分析API
```

### 自定义配置
可以通过修改以下部分来自定义功能：
- 状态颜色映射
- 表格列显示
- 模态框样式
- API 调用参数

## 故障排除

### 常见问题
1. **任务列表为空**：检查数据库连接和数据
2. **启动分析失败**：确认后端服务正在运行
3. **消息加载失败**：检查消息API和数据库权限

### 调试方法
- 打开浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 使用提供的测试脚本验证API功能

## 扩展功能

### 可能的改进
- 添加任务搜索和过滤功能
- 支持批量操作
- 添加任务编辑功能
- 实现实时状态更新
- 添加导出功能

### 集成建议
- 与分析结果页面集成
- 添加任务调度功能
- 集成通知系统
- 添加用户权限管理
