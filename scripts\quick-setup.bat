@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                SSH密钥快速生成脚本                           ║
echo ║              TradingAgents Frontend                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 获取用户输入
set /p SERVER_IP="请输入Ubuntu服务器IP地址: "
set /p SERVER_USER="请输入Ubuntu服务器用户名 (如: ubuntu): "
set /p APP_DOMAIN="请输入应用域名 (可选，直接回车使用IP): "

if "%SERVER_IP%"=="" (
    echo 错误：服务器IP不能为空
    pause
    exit /b 1
)

if "%SERVER_USER%"=="" (
    echo 错误：用户名不能为空
    pause
    exit /b 1
)

echo.
echo 配置信息:
echo 服务器IP: %SERVER_IP%
echo 用户名: %SERVER_USER%
if not "%APP_DOMAIN%"=="" (
    echo 域名: %APP_DOMAIN%
) else (
    echo 域名: 使用IP地址
)
echo.

set /p CONFIRM="确认以上信息正确吗? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🔐 开始生成SSH密钥...

REM 创建.ssh目录
if not exist "%USERPROFILE%\.ssh" (
    mkdir "%USERPROFILE%\.ssh"
    echo ✅ 创建SSH目录成功
) else (
    echo ✅ SSH目录已存在
)

REM 生成SSH密钥
echo 正在生成SSH密钥...
ssh-keygen -t rsa -b 4096 -C "trading-agents-deploy" -f "%USERPROFILE%\.ssh\trading_deploy" -N ""

if %ERRORLEVEL% neq 0 (
    echo ❌ SSH密钥生成失败
    pause
    exit /b 1
)

echo ✅ SSH密钥生成成功

REM 显示公钥内容
echo.
echo 📋 您的SSH公钥内容:
echo ----------------------------------------
type "%USERPROFILE%\.ssh\trading_deploy.pub"
echo ----------------------------------------
echo.

echo 📝 请按以下步骤配置服务器:
echo.
echo 1. SSH连接到您的Ubuntu服务器:
echo    ssh %SERVER_USER%@%SERVER_IP%
echo.
echo 2. 在服务器上执行以下命令:
echo    mkdir -p ~/.ssh
echo    nano ~/.ssh/authorized_keys
echo.
echo 3. 将上面显示的公钥内容粘贴到 authorized_keys 文件中
echo.
echo 4. 设置正确权限:
echo    chmod 700 ~/.ssh
echo    chmod 600 ~/.ssh/authorized_keys
echo    exit
echo.

pause

REM 测试SSH连接
echo.
echo 🧪 测试SSH连接...
ssh -i "%USERPROFILE%\.ssh\trading_deploy" -o "StrictHostKeyChecking=no" %SERVER_USER%@%SERVER_IP% "echo 'SSH连接测试成功'"

if %ERRORLEVEL% neq 0 (
    echo ❌ SSH连接测试失败，请检查服务器配置
    pause
    exit /b 1
)

echo ✅ SSH连接测试成功

REM 生成GitHub Secrets配置
echo.
echo 📝 生成GitHub Secrets配置...

REM 设置URL
if not "%APP_DOMAIN%"=="" (
    set DEPLOY_URL=https://%APP_DOMAIN%
    set API_URL=https://api.%APP_DOMAIN%
    set WS_URL=wss://ws.%APP_DOMAIN%
) else (
    set DEPLOY_URL=http://%SERVER_IP%:3000
    set API_URL=http://%SERVER_IP%:5000
    set WS_URL=ws://%SERVER_IP%:8000
)

REM 创建配置文件
echo # GitHub Secrets 配置 > github_secrets_config.txt
echo # 请将以下内容添加到 GitHub 仓库的 Secrets 中 >> github_secrets_config.txt
echo. >> github_secrets_config.txt
echo # 服务器连接信息 >> github_secrets_config.txt
echo DEPLOY_SSH_KEY= >> github_secrets_config.txt
type "%USERPROFILE%\.ssh\trading_deploy" >> github_secrets_config.txt
echo. >> github_secrets_config.txt
echo DEPLOY_HOST=%SERVER_IP% >> github_secrets_config.txt
echo DEPLOY_USER=%SERVER_USER% >> github_secrets_config.txt
echo DEPLOY_PATH=/opt/trading-agents >> github_secrets_config.txt
echo. >> github_secrets_config.txt
echo # 应用URL配置 >> github_secrets_config.txt
echo DEPLOY_URL=%DEPLOY_URL% >> github_secrets_config.txt
echo API_URL=%API_URL% >> github_secrets_config.txt
echo WS_URL=%WS_URL% >> github_secrets_config.txt
echo. >> github_secrets_config.txt
echo # 数据库配置（请替换为实际密码） >> github_secrets_config.txt
echo MYSQL_ROOT_PASSWORD=请设置您的数据库root密码 >> github_secrets_config.txt
echo MYSQL_PASSWORD=请设置您的数据库用户密码 >> github_secrets_config.txt
echo. >> github_secrets_config.txt
echo # 阿里云镜像服务（已配置） >> github_secrets_config.txt
echo ALIYUN_REGISTRY_USERNAME=aliyun1315382626 >> github_secrets_config.txt
echo ALIYUN_REGISTRY_PASSWORD=ezreal123 >> github_secrets_config.txt

REM 复制私钥到剪贴板
type "%USERPROFILE%\.ssh\trading_deploy" | clip

echo ✅ GitHub Secrets配置已保存到: github_secrets_config.txt
echo ✅ 私钥已复制到剪贴板

echo.
echo 🎉 配置完成！
echo ================================
echo.
echo 📋 下一步操作:
echo 1. 查看生成的配置文件: github_secrets_config.txt
echo 2. 进入GitHub仓库 Settings ^> Secrets and variables ^> Actions
echo 3. 逐个添加配置文件中的所有Secrets
echo 4. 推送代码到 develop 或 main 分支测试部署
echo.
echo 💡 提示:
echo - 私钥已复制到剪贴板，可直接粘贴到 DEPLOY_SSH_KEY
echo - 请记得设置数据库密码
echo - 配置文件保存在当前目录
echo.

pause
