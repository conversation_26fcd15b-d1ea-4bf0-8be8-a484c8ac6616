#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的任务流程和数据库结构
验证移除conversation概念后的系统功能
"""

import requests
import json
import time
import uuid
from typing import Dict, Any, List

# 配置
BASE_URL = "http://localhost:3000"  # Next.js 前端服务地址
API_BASE = f"{BASE_URL}/api"

class TaskFlowTester:
    """任务流程测试类"""
    
    def __init__(self):
        self.test_results = []
        self.created_tasks = []  # 记录创建的测试任务，用于清理
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"   Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    def test_create_task(self) -> str:
        """测试创建任务"""
        test_name = "创建任务"
        
        try:
            # 创建测试任务
            task_data = {
                "ticker": "TEST",
                "title": "测试任务 - 新流程验证",
                "description": "验证移除conversation概念后的任务流程",
                "config": {
                    "analysisType": "comprehensive",
                    "testMode": True
                },
                "created_by": "test_user"
            }
            
            response = requests.post(
                f"{API_BASE}/database/tasks",
                json=task_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("task_id")
                if task_id:
                    self.created_tasks.append(task_id)
                    self.log_test(test_name, True, f"任务创建成功，ID: {task_id}")
                    return task_id
                else:
                    self.log_test(test_name, False, "响应中缺少task_id", result)
                    return None
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return None
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return None
    
    def test_get_task(self, task_id: str) -> bool:
        """测试获取任务详情"""
        test_name = "获取任务详情"
        
        try:
            response = requests.get(f"{API_BASE}/database/tasks/{task_id}")
            
            if response.status_code == 200:
                task = response.json()
                required_fields = ["task_id", "ticker", "title", "status"]
                missing_fields = [field for field in required_fields if field not in task]
                
                if not missing_fields:
                    self.log_test(test_name, True, f"任务详情获取成功，状态: {task.get('status')}")
                    return True
                else:
                    self.log_test(test_name, False, f"缺少必要字段: {missing_fields}", task)
                    return False
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
    
    def test_update_task_status(self, task_id: str) -> bool:
        """测试更新任务状态"""
        test_name = "更新任务状态"
        
        try:
            # 更新为运行状态
            response = requests.patch(
                f"{API_BASE}/database/tasks/{task_id}/status",
                json={"status": "running"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.log_test(test_name, True, "任务状态更新为running成功")
                    return True
                else:
                    self.log_test(test_name, False, "更新失败", result)
                    return False
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
    
    def test_add_message(self, task_id: str) -> bool:
        """测试添加消息（新结构，无conversation_id）"""
        test_name = "添加消息"
        
        try:
            # 添加系统消息
            message_data = {
                "task_id": task_id,
                "message_type": "system",
                "content": "测试消息 - 验证新的消息结构",
                "metadata": {
                    "test": True,
                    "timestamp": time.time()
                },
                "thread_id": f"test_thread_{uuid.uuid4().hex[:8]}"
            }
            
            response = requests.post(
                f"{API_BASE}/database/messages",
                json=message_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.log_test(test_name, True, f"消息添加成功，ID: {result.get('message_id')}")
                    return True
                else:
                    self.log_test(test_name, False, "添加失败", result)
                    return False
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
    
    def test_get_task_messages(self, task_id: str) -> bool:
        """测试获取任务消息"""
        test_name = "获取任务消息"
        
        try:
            response = requests.get(
                f"{API_BASE}/database/messages",
                params={
                    "task_id": task_id,
                    "include_metadata": "true"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                messages = result.get("messages", [])
                
                if isinstance(messages, list):
                    self.log_test(test_name, True, f"获取到 {len(messages)} 条消息")
                    
                    # 验证消息结构
                    if messages:
                        first_message = messages[0]
                        required_fields = ["message_id", "task_id", "message_type", "content"]
                        missing_fields = [field for field in required_fields if field not in first_message]
                        
                        if not missing_fields:
                            # 验证没有conversation_id字段
                            if "conversation_id" not in first_message:
                                self.log_test("消息结构验证", True, "消息结构正确，已移除conversation_id")
                            else:
                                self.log_test("消息结构验证", False, "消息中仍包含conversation_id字段")
                        else:
                            self.log_test("消息结构验证", False, f"消息缺少必要字段: {missing_fields}")
                    
                    return True
                else:
                    self.log_test(test_name, False, "响应格式错误", result)
                    return False
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
    
    def test_task_flow_integration(self, task_id: str) -> bool:
        """测试完整的任务流程集成"""
        test_name = "任务流程集成测试"
        
        try:
            # 模拟启动任务分析
            analyze_data = {
                "ticker": "TEST",
                "task_id": task_id,
                "config": {
                    "analysisType": "comprehensive",
                    "testMode": True
                }
            }
            
            # 注意：这里可能会失败，因为后端服务可能没有运行
            # 但我们主要测试前端API的参数传递是否正确
            response = requests.post(
                f"{API_BASE}/langgraph/analyze",
                json=analyze_data,
                headers={"Content-Type": "application/json"},
                timeout=5  # 设置超时避免长时间等待
            )
            
            # 即使后端不可用，我们也认为测试通过，因为重点是验证API结构
            if response.status_code in [200, 503]:  # 503表示后端服务不可用
                self.log_test(test_name, True, "API调用结构正确")
                return True
            else:
                self.log_test(test_name, False, f"HTTP {response.status_code}", response.text)
                return False
                
        except requests.exceptions.Timeout:
            self.log_test(test_name, True, "API调用超时（预期行为，后端可能未运行）")
            return True
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
    
    def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        for task_id in self.created_tasks:
            try:
                # 这里可以添加删除任务的API调用
                # 目前只是标记为已取消
                requests.patch(
                    f"{API_BASE}/database/tasks/{task_id}/status",
                    json={"status": "cancelled"},
                    headers={"Content-Type": "application/json"}
                )
                print(f"   任务 {task_id} 已标记为取消")
            except:
                pass
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试新的任务流程...")
        print("=" * 50)
        
        # 1. 创建任务
        task_id = self.test_create_task()
        if not task_id:
            print("❌ 任务创建失败，停止后续测试")
            return
        
        # 2. 获取任务详情
        self.test_get_task(task_id)
        
        # 3. 更新任务状态
        self.test_update_task_status(task_id)
        
        # 4. 添加消息
        self.test_add_message(task_id)
        
        # 5. 获取任务消息
        self.test_get_task_messages(task_id)
        
        # 6. 测试任务流程集成
        self.test_task_flow_integration(task_id)
        
        # 生成测试报告
        self.generate_report()
        
        # 清理
        self.cleanup()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test_name']}: {result['message']}")
        
        print("\n✅ 新流程验证完成！")

if __name__ == "__main__":
    tester = TaskFlowTester()
    tester.run_all_tests()
