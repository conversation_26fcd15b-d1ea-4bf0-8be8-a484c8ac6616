import { getCurrentUser } from '@/lib/auth';
import { getUserById } from '@/lib/user-db';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    // 获取完整用户信息
    const user = await getUserById(currentUser.userId);

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 返回用户信息（不包含敏感信息）
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        email_verified: user.email_verified,
        avatar_url: user.avatar_url,
        role: user.role,
        created_at: user.created_at,
        last_login_at: user.last_login_at,
      },
    });
  } catch (error) {
    console.error('Get user info error:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
