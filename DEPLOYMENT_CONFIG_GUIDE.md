# 部署配置指南

## 🔧 需要配置的服务器信息

### 1. GitHub Secrets 需要添加的服务器密钥

在 GitHub 仓库 `Settings` > `Secrets and variables` > `Actions` 中添加：

#### 服务器连接信息
```
# SSH 连接信息
DEPLOY_SSH_KEY=你的SSH私钥内容
DEPLOY_HOST=你的服务器IP地址
DEPLOY_USER=你的服务器用户名
DEPLOY_PORT=22

# 服务器路径
DEPLOY_PATH=/path/to/your/app

# 域名信息
STAGING_DOMAIN=你的预发布域名
PRODUCTION_DOMAIN=你的生产域名
```

#### 数据库密钥
```
# 预发布环境数据库
MYSQL_ROOT_PASSWORD_STAGING=你的预发布数据库root密码
MYSQL_PASSWORD_STAGING=你的预发布数据库用户密码

# 生产环境数据库
MYSQL_ROOT_PASSWORD_PROD=你的生产数据库root密码
MYSQL_PASSWORD_PROD=你的生产数据库用户密码
```

#### 阿里云镜像服务（已配置）
```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

### 2. 部署方式选择

#### 方式一：SSH 部署到服务器
如果您有自己的服务器，需要配置 SSH 连接：

```yaml
# 示例配置
DEPLOY_HOST=*************  # 您的服务器IP
DEPLOY_USER=ubuntu         # 服务器用户名
DEPLOY_PATH=/home/<USER>/trading-agents  # 部署路径
STAGING_DOMAIN=staging.yourdomain.com    # 预发布域名
PRODUCTION_DOMAIN=yourdomain.com         # 生产域名
```

#### 方式二：云服务部署
如果使用阿里云、腾讯云等云服务：

```yaml
# 阿里云 ECS 示例
DEPLOY_HOST=your-ecs-ip
DEPLOY_USER=root
STAGING_DOMAIN=staging.your-domain.com
PRODUCTION_DOMAIN=your-domain.com
```

#### 方式三：容器化部署
如果使用 Docker Swarm 或 Kubernetes：

```yaml
# Docker Swarm 示例
SWARM_MANAGER_HOST=your-swarm-manager-ip
SWARM_USER=docker-user

# Kubernetes 示例
K8S_CLUSTER_URL=your-k8s-cluster-url
K8S_TOKEN=your-k8s-token
```

### 3. 当前需要更新的示例地址

在 `.github/workflows/deploy.yml` 中需要替换：

```yaml
# 当前示例地址（需要替换）
url: https://staging.tradingagents.example.com
NEXT_PUBLIC_API_BASE_URL=https://api-staging.tradingagents.example.com
NEXT_PUBLIC_WS_URL=wss://ws-staging.tradingagents.example.com

# 替换为您的实际地址
url: https://staging.yourdomain.com
NEXT_PUBLIC_API_BASE_URL=https://api-staging.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://ws-staging.yourdomain.com
```

## 🚀 快速配置步骤

### 步骤1：确定部署方式
- [ ] 我有自己的服务器（VPS/云服务器）
- [ ] 我使用云平台服务（如阿里云、腾讯云）
- [ ] 我使用容器编排服务（Docker Swarm/K8s）
- [ ] 我暂时只想本地测试

### 步骤2：准备服务器信息
如果您有服务器，请准备：
- 服务器IP地址
- SSH用户名和密钥
- 域名（如果有）
- 数据库密码

### 步骤3：生成SSH密钥（如果需要）
```bash
# 在本地生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥添加到服务器
ssh-copy-id user@your-server-ip

# 私钥内容添加到GitHub Secrets
cat ~/.ssh/id_rsa  # 复制私钥内容到 DEPLOY_SSH_KEY
```

### 步骤4：配置域名（可选）
如果您有域名，配置DNS解析：
```
# A记录示例
staging.yourdomain.com  ->  your-server-ip
yourdomain.com         ->  your-server-ip
api-staging.yourdomain.com -> your-server-ip
```

## 🔧 简化配置选项

### 选项1：仅本地部署
如果暂时只想本地测试，可以禁用自动部署：

```yaml
# 在 deploy.yml 中添加条件
if: false  # 暂时禁用自动部署
```

### 选项2：使用Docker Compose本地部署
```bash
# 直接使用Docker Compose
docker-compose -f docker/docker-compose.yml up -d
```

### 选项3：手动部署
```bash
# 手动拉取和运行镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
docker run -d -p 3000:3000 --name frontend \
  crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

## 📝 配置模板

### 最小配置（仅镜像构建）
```yaml
# 只需要阿里云镜像服务密钥
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

### 完整配置（包含自动部署）
```yaml
# 服务器信息
DEPLOY_SSH_KEY=-----BEGIN RSA PRIVATE KEY-----...
DEPLOY_HOST=*************
DEPLOY_USER=ubuntu
DEPLOY_PATH=/home/<USER>/app

# 域名信息
STAGING_DOMAIN=staging.yourdomain.com
PRODUCTION_DOMAIN=yourdomain.com

# 数据库密码
MYSQL_ROOT_PASSWORD_STAGING=your_staging_password
MYSQL_ROOT_PASSWORD_PROD=your_production_password
MYSQL_PASSWORD_STAGING=your_staging_user_password
MYSQL_PASSWORD_PROD=your_production_user_password

# 阿里云镜像服务
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

## 🆘 如果您暂时没有服务器

### 临时解决方案
1. **禁用自动部署**：在 deploy.yml 中添加 `if: false`
2. **仅构建镜像**：CI/CD 只构建和推送镜像到阿里云
3. **本地测试**：使用 Docker 在本地运行镜像

### 推荐的云服务
- **阿里云ECS**：与阿里云镜像服务集成好
- **腾讯云CVM**：国内访问速度快
- **华为云ECS**：企业级稳定性
- **Vultr/DigitalOcean**：海外服务器选择

---

**💡 建议**：如果您暂时没有服务器，可以先配置镜像构建部分，等有服务器后再配置自动部署。这样可以先验证CI/CD的镜像构建功能是否正常。
