# GitHub Actions 工作流说明

## 统一 CI/CD Pipeline

我们将原来的三个独立工作流（`ci.yml`、`deploy.yml`、`docker.yml`）合并为一个统一的 CI/CD Pipeline，消除了重复代码并提高了维护效率。

### 工作流触发条件

- **Push 到 main/develop 分支**：自动触发完整的 CI/CD 流程
- **Pull Request**：只执行代码质量检查和性能测试
- **Tag 推送**：触发版本发布流程
- **手动触发**：支持自定义部署环境和选项

### 工作流阶段

#### 1. 代码质量检查 (code-quality)

- ESLint 代码检查
- TypeScript 类型检查
- 依赖缓存优化

#### 2. 单元测试 (unit-tests)

- 预留阶段，当前已禁用
- 支持测试覆盖率报告

#### 3. Docker 构建和推送 (build-and-push)

- 多平台构建 (linux/amd64, linux/arm64)
- 智能标签策略
- 镜像安全扫描
- 构建缓存优化

#### 4. 镜像测试 (test-images)

- 容器启动测试
- 健康检查验证

#### 5. 自动部署 (deploy)

- 生产环境：main 分支自动部署
- 预发布环境：develop 分支自动部署
- 支持手动部署和强制部署
- 完整的健康检查和回滚机制

#### 6. 性能测试 (performance-test)

- Lighthouse CI 性能测试
- 仅在 Pull Request 时执行

### 环境变量配置

#### GitHub Secrets 需要配置：

```
# 阿里云镜像仓库
ALIYUN_REGISTRY_USERNAME
ALIYUN_REGISTRY_PASSWORD

# 部署服务器
DEPLOY_SSH_KEY
DEPLOY_HOST
DEPLOY_USER
DEPLOY_PATH

# 应用配置
API_URL
DEPLOY_URL
NEXT_PUBLIC_WS_URL
NEXT_PUBLIC_OPENAI_API_KEY
NEXT_PUBLIC_FINNHUB_API_KEY

# 数据库配置
MYSQL_ROOT_PASSWORD
MYSQL_PASSWORD

# 性能测试
LHCI_GITHUB_APP_TOKEN
```

### 优化亮点

1. **消除重复**：合并了三个工作流的重复逻辑
2. **智能部署**：根据分支自动选择部署环境
3. **安全扫描**：集成 Trivy 安全扫描
4. **多平台支持**：支持 AMD64 和 ARM64 架构
5. **完整监控**：从构建到部署的全流程监控
6. **灵活配置**：支持手动触发和自定义参数

### 使用方式

#### 自动触发

- 推送到 `main` 分支：自动部署到生产环境
- 推送到 `develop` 分支：自动部署到预发布环境
- 创建 Pull Request：执行代码检查和性能测试

#### 手动触发

1. 进入 GitHub Actions 页面
2. 选择 "CI/CD Pipeline" 工作流
3. 点击 "Run workflow"
4. 选择部署环境和选项
5. 点击 "Run workflow" 确认

### 故障排查

#### 常见问题

1. **镜像推送失败**：检查阿里云镜像仓库凭据
2. **部署失败**：检查 SSH 密钥和服务器连接
3. **健康检查失败**：检查应用配置和端口映射

#### 调试方法

1. 查看 GitHub Actions 日志
2. SSH 到服务器检查容器状态
3. 查看应用日志：`docker-compose logs frontend`

### 维护建议

1. **定期更新**：保持 Actions 版本最新
2. **监控性能**：关注构建时间和部署效率
3. **安全审计**：定期检查镜像安全扫描结果
4. **文档更新**：及时更新配置变更
