'use client';

import { motion } from 'framer-motion';
import { CheckIcon } from '@heroicons/react/24/solid';
import { Card, CardContent } from '@/components/ui/Card';

interface AnalysisProgressProps {
  currentStage: string;
  progress: number;
  isComplete: boolean;
}

export function AnalysisProgress({ currentStage, progress, isComplete }: AnalysisProgressProps) {
  const stages = [
    { id: 'initialization', name: '初始化', description: '准备分析环境和数据' },
    { id: 'data_collection', name: '数据收集', description: '获取市场数据和新闻信息' },
    { id: 'analyst_analysis', name: '分析师分析', description: '各专业分析师进行深度分析' },
    { id: 'research_debate', name: '研究辩论', description: '多头空头研究员辩论' },
    { id: 'trading_decision', name: '交易决策', description: '交易员制定交易策略' },
    { id: 'risk_assessment', name: '风险评估', description: '风险管理团队评估风险' },
    { id: 'final_decision', name: '最终决策', description: '投资组合管理最终决策' },
  ];

  const getCurrentStageIndex = () => {
    return stages.findIndex(stage => stage.id === currentStage);
  };

  const currentStageIndex = getCurrentStageIndex();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Progress Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              分析进度
            </h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              {isComplete ? '已完成' : `${progress}% 完成`}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
            <motion.div 
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>

          {/* Stages */}
          <div className="space-y-4">
            {stages.map((stage, index) => {
              const isActive = index === currentStageIndex;
              const isCompleted = index < currentStageIndex || isComplete;
              const isPending = index > currentStageIndex && !isComplete;

              return (
                <motion.div
                  key={stage.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center space-x-4 p-4 rounded-lg transition-all ${
                    isActive 
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                      : isCompleted
                      ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                      : 'bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700'
                  }`}
                >
                  {/* Stage Icon */}
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    isCompleted
                      ? 'bg-green-500 text-white'
                      : isActive
                      ? 'bg-blue-500 text-white'
                      : 'bg-slate-300 dark:bg-slate-600 text-slate-600 dark:text-slate-400'
                  }`}>
                    {isCompleted ? (
                      <CheckIcon className="h-5 w-5" />
                    ) : (
                      <span className="text-sm font-semibold">{index + 1}</span>
                    )}
                  </div>

                  {/* Stage Content */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className={`font-medium ${
                        isActive 
                          ? 'text-blue-900 dark:text-blue-100' 
                          : isCompleted
                          ? 'text-green-900 dark:text-green-100'
                          : 'text-slate-700 dark:text-slate-300'
                      }`}>
                        {stage.name}
                      </h4>
                      
                      {isActive && (
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                        </div>
                      )}
                    </div>
                    
                    <p className={`text-sm ${
                      isActive 
                        ? 'text-blue-700 dark:text-blue-300' 
                        : isCompleted
                        ? 'text-green-700 dark:text-green-300'
                        : 'text-slate-500 dark:text-slate-500'
                    }`}>
                      {stage.description}
                    </p>
                  </div>

                  {/* Stage Status */}
                  <div className="text-xs font-medium">
                    {isCompleted && (
                      <span className="text-green-600 dark:text-green-400">✓ 完成</span>
                    )}
                    {isActive && (
                      <span className="text-blue-600 dark:text-blue-400">● 进行中</span>
                    )}
                    {isPending && (
                      <span className="text-slate-400 dark:text-slate-600">○ 等待中</span>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Current Stage Detail */}
          {!isComplete && currentStageIndex >= 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
            >
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  当前正在执行: {stages[currentStageIndex]?.name}
                </span>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                {stages[currentStageIndex]?.description}
              </p>
            </motion.div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
