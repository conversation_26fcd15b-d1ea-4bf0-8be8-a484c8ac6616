# 阿里云容器镜像服务部署指南

本文档详细说明如何配置 CI/CD 流程，将 Docker 镜像自动推送到阿里云容器镜像服务。

## 📋 目录

- [概述](#概述)
- [阿里云配置](#阿里云配置)
- [GitHub Secrets 配置](#github-secrets-配置)
- [CI/CD 工作流](#cicd-工作流)
- [本地测试](#本地测试)
- [故障排除](#故障排除)

## 🎯 概述

我们的 CI/CD 流程现在推送 Docker 镜像到：

- **阿里云容器镜像服务** - 国内访问更快，稳定可靠

### 镜像命名规则

#### 阿里云容器镜像服务

```
crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:tag
```

## ☁️ 阿里云配置

### 1. 容器镜像服务信息

- **注册表地址**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com`
- **命名空间**: `ez_trading`
- **仓库名称**: `frontend`
- **用户名**: `aliyun1315382626`
- **密码**: `ezreal123`

### 2. 支持的镜像标签

CI/CD 流程会自动生成以下标签：

- `latest` - main 分支的最新版本
- `staging` - develop 分支的最新版本
- `main-<sha>` - main 分支特定提交
- `develop-<sha>` - develop 分支特定提交
- `pr-<number>` - Pull Request 版本

### 3. 手动操作命令

如果需要手动推送镜像：

```bash
# 登录阿里云容器镜像服务
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com

# 构建镜像
docker build -t frontend:latest -f docker/Dockerfile .

# 标记镜像
docker tag frontend:latest crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 推送镜像
docker push crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 拉取镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

## 🔐 GitHub Secrets 配置

### 1. 必需的 Secrets

在 GitHub 仓库中添加以下 Secrets：

```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

### 2. 配置步骤

1. 进入 GitHub 仓库页面
2. 点击 `Settings` > `Secrets and variables` > `Actions`
3. 点击 `New repository secret`
4. 添加上述两个密钥

### 3. 验证配置

配置完成后，推送代码到 main 或 develop 分支，检查 GitHub Actions 是否成功推送到阿里云。

## 🔄 CI/CD 工作流

### 自动触发条件

- **推送到 main 分支**: 构建并推送 `latest` 标签
- **推送到 develop 分支**: 构建并推送 `staging` 标签
- **创建 Pull Request**: 构建但不推送到阿里云
- **手动触发**: 支持选择环境和选项

### 工作流程

1. **代码检查**: ESLint、TypeScript 类型检查
2. **安全扫描**: 依赖扫描、文件系统扫描
3. **构建镜像**: 多架构构建 (amd64, arm64)
4. **推送镜像**: 推送到阿里云容器镜像服务
5. **安全扫描**: 镜像安全扫描
6. **部署**: 自动部署到对应环境

### 环境变量配置

CI/CD 工作流中的相关环境变量：

```yaml
env:
  # 阿里云容器镜像服务
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  ALIYUN_IMAGE_NAME: frontend
```

## 🧪 本地测试

### 1. 测试阿里云登录

```bash
# 测试登录
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com

# 输入密码: ezreal123
```

### 2. 测试镜像推送

```bash
# 构建测试镜像
docker build -t test-frontend:local -f docker/Dockerfile .

# 标记为阿里云镜像
docker tag test-frontend:local crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:test

# 推送测试镜像
docker push crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:test

# 清理测试镜像
docker rmi test-frontend:local
docker rmi crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:test
```

### 3. 测试镜像拉取

```bash
# 拉取最新镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 运行容器测试
docker run -d -p 3000:3000 --name test-frontend \
  crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 测试访问
curl http://localhost:3000

# 清理测试容器
docker stop test-frontend
docker rm test-frontend
```

## 🔧 故障排除

### 常见问题

#### 1. 登录失败

```bash
Error response from daemon: login attempt to ... failed with status: 401 Unauthorized
```

**解决方案**:

- 检查用户名和密码是否正确
- 确认阿里云容器镜像服务是否已开通
- 检查网络连接

#### 2. 推送失败

```bash
denied: requested access to the resource is denied
```

**解决方案**:

- 确认命名空间 `ez_trading` 存在
- 检查仓库 `frontend` 是否已创建
- 验证用户权限

#### 3. GitHub Actions 失败

**检查步骤**:

1. 查看 Actions 运行日志
2. 确认 GitHub Secrets 配置正确
3. 检查网络连接问题

#### 4. 镜像拉取慢

**解决方案**:

- 使用阿里云镜像加速器
- 配置 Docker daemon.json:

```json
{
  "registry-mirrors": ["https://your-accelerator.mirror.aliyuncs.com"]
}
```

### 调试命令

```bash
# 查看本地镜像
docker images | grep frontend

# 查看镜像详情
docker inspect crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 查看容器日志
docker logs container-name

# 测试网络连接
ping crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
```

## 📊 监控和维护

### 1. 镜像管理

- 定期清理旧版本镜像
- 监控镜像大小和安全漏洞
- 设置镜像保留策略

### 2. 访问统计

- 监控镜像下载次数
- 分析使用模式
- 优化镜像分发策略

### 3. 成本优化

- 选择合适的镜像存储类型
- 配置镜像生命周期管理
- 监控存储和流量费用

## 🔗 相关链接

- [阿里云容器镜像服务文档](https://help.aliyun.com/product/60716.html)
- [Docker 官方文档](https://docs.docker.com/)
- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [项目 CI/CD 指南](CI_CD_GUIDE.md)

---

**注意**: 请妥善保管阿里云账号信息，不要在代码中硬编码敏感信息。定期更换密码以确保安全。
