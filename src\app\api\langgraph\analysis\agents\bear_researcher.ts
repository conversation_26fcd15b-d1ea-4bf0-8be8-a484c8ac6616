import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { AIMessage } from '@langchain/core/messages';

const bearResearcherPrompt = PromptTemplate.fromTemplate(`
你是一位资深的空头研究员，专门从看跌角度深度挖掘投资风险。你拥有15年的A股风险研究经验，擅长发现被市场高估的风险和泡沫。你的任务是基于分析师团队的初步分析，从空头角度进行深度研究和风险论证。

【研究任务】
股票代码: {ticker}
分析日期: {date}
研究深度: {researchDepth}

【分析师团队报告】
{analysisReports}

【空头研究框架】
请从以下角度进行深度空头研究：

1. **估值泡沫论证**
   - 分析当前估值的不合理性
   - 对比历史估值和同行业水平
   - 识别估值回归的压力和催化剂
   - 评估市场预期过于乐观的风险

2. **基本面恶化风险**
   - 深度分析财务数据中的隐忧
   - 识别盈利能力下滑的趋势
   - 评估现金流和债务风险
   - 分析业务模式的可持续性问题

3. **竞争劣势暴露**
   - 分析竞争地位的弱化趋势
   - 评估技术落后和创新不足
   - 识别市场份额流失的风险
   - 分析护城河被侵蚀的可能性

4. **负面催化剂识别**
   - 政策风险和监管压力
   - 行业周期下行和需求萎缩
   - 管理层问题和治理风险
   - 黑天鹅事件和突发风险

5. **技术面压力**
   - 从技术角度论证下跌风险
   - 识别关键支撑位破位风险
   - 分析资金流出和抛压增加
   - 评估技术形态的看跌信号

6. **市场情绪转向**
   - 分析投资者情绪的变化趋势
   - 识别市场预期过高的风险
   - 评估负面新闻的累积效应
   - 分析机构资金的流向变化

7. **风险收益不匹配**
   - 论证当前价位的风险过大
   - 分析下行空间大于上行空间
   - 提出减持或做空的理由
   - 设定止损位和风险控制策略

【输出要求】
- 论证要客观理性，基于事实和数据
- 重点突出最具说服力的空头观点
- 提供具体的风险量化和概率评估
- 承认机会但重点论证风险大于收益
- 为后续辩论准备充分的反驳论据
`);

export async function bearResearcherNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, analysis, messages } = state;

  console.log(`[空头研究员] 开始深度研究股票: ${ticker}`);

  try {
    // 检查分析师报告是否可用
    if (!analysis || Object.keys(analysis).length === 0) {
      throw new Error('分析师团队报告不可用，无法进行深度研究');
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.2, // 稍高的温度以获得更有创意的论证
      openAIApiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 整理分析师报告
    const analysisReports = formatAnalysisReports(analysis);

    const prompt = await bearResearcherPrompt.format({
      ticker,
      date,
      researchDepth: config.researchDepth || 'standard',
      analysisReports,
    });

    console.log(`[空头研究员] 正在进行深度空头研究...`);
    const response = await llm.invoke(prompt);
    const researchReport = response.content as string;

    // 提取关键论点和风险因素
    const keyArguments = extractBearArguments(researchReport);
    const riskFactors = extractRiskFactors(researchReport);
    const targetPrice = extractTargetPrice(researchReport);
    const confidence = calculateBearConfidence(analysis, keyArguments);

    // 生成结构化摘要
    const summary = generateBearSummary(researchReport, keyArguments, targetPrice);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【空头研究员报告】\n\n${researchReport}`,
        name: 'BearResearcher',
      }),
    ];

    // 更新研究状态
    const research = {
      ...state.research,
      bear: {
        summary,
        report: researchReport,
        keyArguments,
        riskFactors,
        targetPrice,
        confidence,
        analyst: 'BearResearcher',
        timestamp: new Date().toISOString(),
      },
    };

    console.log(`[空头研究员] 研究完成，置信度: ${confidence.toFixed(2)}`);
    return {
      messages: newMessages,
      research,
      currentStage: 'bear_research_completed',
      progress: Math.min(state.progress + 15, 100),
    };
  } catch (error) {
    console.error('[空头研究员] 研究失败:', error);
    const errorMessage = `空头研究失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【空头研究员】${errorMessage}`,
        name: 'BearResearcher',
      }),
    ];

    const research = {
      ...state.research,
      bear: {
        summary: '空头研究失败',
        report: errorMessage,
        error: true,
        analyst: 'BearResearcher',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, research };
  }
}

// 辅助函数：格式化分析师报告
function formatAnalysisReports(analysis: any): string {
  let reports = '';

  if (analysis.fundamental) {
    reports += `\n【基本面分析】\n${analysis.fundamental.summary}\n`;
    if (analysis.fundamental.keyMetrics) {
      reports += `关键指标: ${JSON.stringify(analysis.fundamental.keyMetrics, null, 2)}\n`;
    }
  }

  if (analysis.technical) {
    reports += `\n【技术分析】\n${analysis.technical.summary}\n`;
    if (analysis.technical.technicalSignals) {
      reports += `技术信号: ${JSON.stringify(analysis.technical.technicalSignals, null, 2)}\n`;
    }
  }

  if (analysis.sentiment) {
    reports += `\n【情绪分析】\n${analysis.sentiment.summary}\n`;
  }

  if (analysis.news) {
    reports += `\n【新闻分析】\n${analysis.news.summary}\n`;
  }

  return reports || '分析师报告暂不可用';
}

// 辅助函数：提取空头论点
function extractBearArguments(report: string): string[] {
  const keyArguments: string[] = [];

  // 使用正则表达式提取关键论点
  const patterns = [
    /估值[过高|泡沫|不合理]/g,
    /风险[增加|暴露|加大]/g,
    /竞争[劣势|压力|威胁]/g,
    /下跌|看跌|负面/g,
    /债务|现金流|财务]/g,
  ];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of patterns) {
      if (pattern.test(line) && line.length > 10 && line.length < 200) {
        keyArguments.push(line.trim());
        break;
      }
    }
  }

  return keyArguments.slice(0, 5); // 返回前5个关键论点
}

// 辅助函数：提取风险因素
function extractRiskFactors(report: string): string[] {
  const riskFactors: string[] = [];

  const riskPatterns = [
    /政策[风险|压力]/g,
    /监管[风险|变化]/g,
    /竞争[加剧|威胁]/g,
    /需求[下滑|萎缩]/g,
    /技术[落后|风险]/g,
  ];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of riskPatterns) {
      if (pattern.test(line) && line.length > 10 && line.length < 150) {
        riskFactors.push(line.trim());
        break;
      }
    }
  }

  return riskFactors.slice(0, 3); // 返回前3个风险因素
}

// 辅助函数：提取目标价位
function extractTargetPrice(report: string): number | null {
  const pricePatterns = [
    /目标价[位]?[：:]\s*([0-9.]+)/,
    /下跌[至到]\s*([0-9.]+)/,
    /支撑[位]?[：:]\s*([0-9.]+)/,
  ];

  for (const pattern of pricePatterns) {
    const match = report.match(pattern);
    if (match) {
      return parseFloat(match[1]);
    }
  }

  return null;
}

// 辅助函数：计算空头置信度
function calculateBearConfidence(analysis: any, keyArguments: string[]): number {
  let confidence = 0.5; // 基础置信度

  // 基于分析师报告的消极信号
  if (
    analysis.fundamental?.investmentRating === '减持' ||
    analysis.fundamental?.investmentRating === '卖出'
  ) {
    confidence += 0.15;
  }

  if (analysis.technical?.tradingSignal === '卖出') {
    confidence += 0.15;
  }

  if (analysis.sentiment?.overallSentiment === 'negative') {
    confidence += 0.1;
  }

  if (analysis.news?.sentiment < 0.4) {
    confidence += 0.1;
  }

  // 基于论点数量和质量
  confidence += Math.min(keyArguments.length * 0.02, 0.1);

  return Math.min(confidence, 0.95); // 最高95%置信度
}

// 辅助函数：生成空头摘要
function generateBearSummary(
  report: string,
  keyArguments: string[],
  targetPrice: number | null
): string {
  const lines = report.split('\n').filter((line) => line.trim());
  const firstParagraph = lines.slice(0, 2).join(' ');

  let summary = `空头观点 - ${firstParagraph.substring(0, 120)}...`;

  if (keyArguments.length > 0) {
    summary += ` 核心论点: ${keyArguments[0].substring(0, 50)}...`;
  }

  if (targetPrice) {
    summary += ` 目标价: ${targetPrice}`;
  }

  return summary;
}
