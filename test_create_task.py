#!/usr/bin/env python3
"""
测试脚本：验证 /create-task 页面的功能
这个脚本会测试页面的基本功能，包括表单验证和提交
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options


def test_create_task_page():
    """测试创建任务页面的功能"""

    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument(
        "--headless"
    )  # 无头模式，如果需要看到浏览器界面可以注释掉这行
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    # 启动浏览器
    driver = webdriver.Chrome(options=chrome_options)

    try:
        print("🚀 开始测试 /create-task 页面...")

        # 访问页面
        driver.get("http://localhost:3001/create-task")
        print("✅ 成功访问页面")

        # 等待页面加载
        wait = WebDriverWait(driver, 10)

        # 检查页面标题
        title_element = wait.until(
            EC.presence_of_element_located(
                (By.XPATH, "//p[contains(text(), 'Create New Analysis Task')]")
            )
        )
        print("✅ 页面标题显示正确")

        # 测试股票代码输入
        stock_input = driver.find_element(
            By.XPATH, "//input[@placeholder='Enter stock code']"
        )
        stock_input.clear()
        stock_input.send_keys("AAPL")
        print("✅ 股票代码输入功能正常")

        # 测试分析周期选择
        period_select = Select(
            driver.find_element(
                By.XPATH, "//select[preceding-sibling::p[text()='Analysis Period']]"
            )
        )
        period_select.select_by_value("1m")
        print("✅ 分析周期选择功能正常")

        # 测试分析师团队选择
        team_select = Select(
            driver.find_element(
                By.XPATH, "//select[preceding-sibling::p[text()='Analyst Team']]"
            )
        )
        team_select.select_by_value("team1")
        print("✅ 分析师团队选择功能正常")

        # 测试研究深度选择
        depth_select = Select(
            driver.find_element(
                By.XPATH, "//select[preceding-sibling::p[text()='Research Depth']]"
            )
        )
        depth_select.select_by_value("medium")
        print("✅ 研究深度选择功能正常")

        # 检查提交按钮
        submit_button = driver.find_element(
            By.XPATH, "//button[contains(text(), 'Create Task')]"
        )
        assert submit_button.is_enabled(), "提交按钮应该是可用的"
        print("✅ 提交按钮状态正确")

        # 检查页面样式（深色主题）
        body_element = driver.find_element(By.TAG_NAME, "body")
        body_classes = body_element.get_attribute("class")
        print(f"📋 页面样式类: {body_classes}")

        # 检查背景颜色是否为深色主题
        page_container = driver.find_element(
            By.XPATH, "//div[contains(@class, 'bg-[#131520]')]"
        )
        print("✅ 深色主题背景应用正确")

        # 检查表单容器是否居中
        form_container = driver.find_element(
            By.XPATH, "//div[contains(@class, 'flex flex-col items-center')]"
        )
        print("✅ 表单容器居中对齐正确")

        # 检查表单字段的样式和宽度
        input_elements = driver.find_elements(By.XPATH, "//input | //select")
        for element in input_elements:
            classes = element.get_attribute("class")
            assert "bg-[#282d43]" in classes, f"表单元素应该有深色背景: {classes}"
        print("✅ 表单元素样式正确")

        # 检查表单字段容器的最大宽度
        field_containers = driver.find_elements(
            By.XPATH, "//div[contains(@class, 'max-w-[480px]')]"
        )
        assert len(field_containers) >= 4, "应该有至少4个表单字段容器"
        print("✅ 表单字段容器宽度限制正确")

        print("\n🎉 所有测试通过！页面功能正常")

        # 可选：测试表单提交（注意：这会实际发送请求）
        print("\n⚠️  注意：实际提交测试需要后端服务运行")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        # 截图保存错误状态
        driver.save_screenshot("test_error.png")
        print("📸 错误截图已保存为 test_error.png")

    finally:
        # 关闭浏览器
        driver.quit()
        print("🔚 测试完成")


def test_api_endpoint():
    """测试API端点是否可用"""
    try:
        print("\n🔍 测试API端点...")

        # 测试健康检查端点（如果存在）
        try:
            response = requests.get("http://localhost:3001/api/health", timeout=5)
            print(f"✅ 健康检查端点响应: {response.status_code}")
        except:
            print("⚠️  健康检查端点不可用")

        # 测试任务创建端点（不发送实际数据）
        try:
            response = requests.options(
                "http://localhost:3001/api/langgraph/analysis/start", timeout=5
            )
            print(f"✅ 任务创建端点可访问: {response.status_code}")
        except:
            print("⚠️  任务创建端点不可用")

    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")


if __name__ == "__main__":
    print("🧪 开始测试 Create Task 页面")
    print("=" * 50)

    # 首先测试API端点
    test_api_endpoint()

    # 然后测试页面功能
    test_create_task_page()

    print("\n" + "=" * 50)
    print("✨ 测试完成！")
