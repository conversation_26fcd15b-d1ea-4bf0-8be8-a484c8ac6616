import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 后端API专用配置 - 与前端axios实例区分开
const SERVER_API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL || 'http://localhost:3000',
  timeout: 10000, // 后端调用超时时间较短
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'TradingAgents-NextJS-Server/1.0.0',
  },
};

// 创建后端专用的axios实例
export const serverApi: AxiosInstance = axios.create(SERVER_API_CONFIG);

// 后端专用请求拦截器
serverApi.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加服务器端特有的请求头
    if (config.headers) {
      config.headers['X-Request-Source'] = 'nextjs-server';
      config.headers['X-Request-Timestamp'] = new Date().toISOString();
    }

    // 在服务器端可以添加内部API密钥等
    const internalApiKey = process.env.INTERNAL_API_KEY;
    if (internalApiKey && config.headers) {
      config.headers['X-Internal-API-Key'] = internalApiKey;
    }

    return config;
  },
  (error) => {
    console.error('Server API Request Error:', error);
    return Promise.reject(error);
  }
);

// 后端专用响应拦截器
serverApi.interceptors.response.use(
  (response: AxiosResponse) => {
    // 可以在这里添加服务器端特有的响应处理逻辑
    return response;
  },
  (error) => {
    // 服务器端错误处理
    console.error('Server API Response Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });
    
    return Promise.reject(error);
  }
);

// 后端专用API方法
export const serverApiMethods = {
  // 健康检查
  healthCheck: async (): Promise<{ status: string; timestamp: string }> => {
    try {
      const response = await serverApi.get('/api/health', {
        timeout: 5000, // 健康检查使用更短的超时时间
      });
      return {
        status: response.status === 200 ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      };
    }
  },

  // 获取服务器状态
  getServerStatus: async (): Promise<any> => {
    try {
      const response = await serverApi.get('/api/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get server status:', error);
      throw error;
    }
  },

  // 内部API调用 - 仅用于服务器端
  internalApiCall: async (endpoint: string, data?: any): Promise<any> => {
    try {
      const response = await serverApi.post(`/internal${endpoint}`, data);
      return response.data;
    } catch (error) {
      console.error(`Internal API call failed for ${endpoint}:`, error);
      throw error;
    }
  },
};

// 导出配置常量供其他地方使用
export const SERVER_API_BASE_URL = SERVER_API_CONFIG.baseURL;
export const SERVER_API_TIMEOUT = SERVER_API_CONFIG.timeout;
