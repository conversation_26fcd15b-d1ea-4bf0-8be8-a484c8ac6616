'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  PlayIcon, 
  PauseIcon, 
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface WorkflowNode {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  output?: any;
}

interface WorkflowEdge {
  from: string;
  to: string;
  condition?: string;
}

interface WorkflowVisualizationProps {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  currentNode?: string;
  className?: string;
}

export function WorkflowVisualization({ 
  nodes, 
  edges, 
  currentNode, 
  className 
}: WorkflowVisualizationProps) {
  const [animatedEdges, setAnimatedEdges] = useState<Set<string>>(new Set());

  // 当当前节点变化时，动画显示边
  useEffect(() => {
    if (currentNode) {
      const incomingEdges = edges.filter(edge => edge.to === currentNode);
      const newAnimatedEdges = new Set(animatedEdges);
      
      incomingEdges.forEach(edge => {
        newAnimatedEdges.add(`${edge.from}-${edge.to}`);
      });
      
      setAnimatedEdges(newAnimatedEdges);
    }
  }, [currentNode, edges, animatedEdges]);

  const getNodeIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayIcon className="h-4 w-4 text-blue-600 animate-pulse" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'error':
        return <ExclamationCircleIcon className="h-4 w-4 text-red-600" />;
      default:
        return <ClockIcon className="h-4 w-4 text-slate-400" />;
    }
  };

  const getNodeColor = (status: string, isCurrent: boolean) => {
    if (isCurrent) {
      return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg';
    }
    
    switch (status) {
      case 'running':
        return 'border-blue-300 bg-blue-50 dark:bg-blue-900/20';
      case 'completed':
        return 'border-green-300 bg-green-50 dark:bg-green-900/20';
      case 'error':
        return 'border-red-300 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-slate-300 bg-slate-50 dark:bg-slate-800';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge variant="info" size="sm">运行中</Badge>;
      case 'completed':
        return <Badge variant="success" size="sm">已完成</Badge>;
      case 'error':
        return <Badge variant="danger" size="sm">错误</Badge>;
      default:
        return <Badge variant="default" size="sm">等待中</Badge>;
    }
  };

  const calculateDuration = (node: WorkflowNode) => {
    if (!node.startTime) return null;
    const endTime = node.endTime || new Date();
    const duration = endTime.getTime() - node.startTime.getTime();
    return Math.round(duration / 1000); // 秒
  };

  // 简单的布局算法 - 垂直排列
  const nodePositions = nodes.reduce((acc, node, index) => {
    acc[node.id] = {
      x: 50,
      y: 100 + index * 120,
    };
    return acc;
  }, {} as Record<string, { x: number; y: number }>);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>工作流可视化</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative h-96 overflow-auto">
          <svg
            width="100%"
            height={Math.max(400, nodes.length * 120 + 100)}
            className="absolute inset-0"
          >
            {/* 渲染边 */}
            {edges.map((edge) => {
              const fromPos = nodePositions[edge.from];
              const toPos = nodePositions[edge.to];
              
              if (!fromPos || !toPos) return null;
              
              const isAnimated = animatedEdges.has(`${edge.from}-${edge.to}`);
              
              return (
                <g key={`${edge.from}-${edge.to}`}>
                  <motion.line
                    x1={fromPos.x + 150}
                    y1={fromPos.y + 40}
                    x2={toPos.x}
                    y2={toPos.y + 40}
                    stroke={isAnimated ? '#3b82f6' : '#e2e8f0'}
                    strokeWidth={isAnimated ? 3 : 2}
                    markerEnd="url(#arrowhead)"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: isAnimated ? 1 : 0.3 }}
                    transition={{ duration: 0.5 }}
                  />
                  
                  {edge.condition && (
                    <text
                      x={(fromPos.x + toPos.x + 150) / 2}
                      y={(fromPos.y + toPos.y) / 2 + 35}
                      textAnchor="middle"
                      className="text-xs fill-slate-600 dark:fill-slate-400"
                    >
                      {edge.condition}
                    </text>
                  )}
                </g>
              );
            })}
            
            {/* 箭头标记 */}
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#3b82f6"
                />
              </marker>
            </defs>
          </svg>
          
          {/* 渲染节点 */}
          {nodes.map((node) => {
            const position = nodePositions[node.id];
            const isCurrent = currentNode === node.id;
            const duration = calculateDuration(node);
            
            return (
              <motion.div
                key={node.id}
                className={`absolute w-40 p-3 border-2 rounded-lg ${getNodeColor(node.status, isCurrent)}`}
                style={{
                  left: position.x,
                  top: position.y,
                }}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    {getNodeIcon(node.status)}
                    {getStatusBadge(node.status)}
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-slate-900 dark:text-white">
                      {node.name}
                    </h4>
                    <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                      {node.description}
                    </p>
                  </div>
                  
                  {duration !== null && (
                    <div className="text-xs text-slate-500">
                      耗时: {duration}秒
                    </div>
                  )}
                  
                  {node.output && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-slate-600 dark:text-slate-400">
                        输出
                      </summary>
                      <pre className="mt-1 p-1 bg-slate-100 dark:bg-slate-700 rounded text-xs overflow-auto max-h-20">
                        {JSON.stringify(node.output, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
        
        {/* 图例 */}
        <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
          <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            状态说明
          </h5>
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center space-x-1">
              <ClockIcon className="h-3 w-3 text-slate-400" />
              <span>等待中</span>
            </div>
            <div className="flex items-center space-x-1">
              <PlayIcon className="h-3 w-3 text-blue-600" />
              <span>运行中</span>
            </div>
            <div className="flex items-center space-x-1">
              <CheckCircleIcon className="h-3 w-3 text-green-600" />
              <span>已完成</span>
            </div>
            <div className="flex items-center space-x-1">
              <ExclamationCircleIcon className="h-3 w-3 text-red-600" />
              <span>错误</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 默认的交易分析工作流节点
export const defaultTradingWorkflowNodes: WorkflowNode[] = [
  {
    id: 'start',
    name: '开始',
    description: '初始化分析流程',
    status: 'pending',
  },
  {
    id: 'data_collection',
    name: '数据收集',
    description: '获取股票数据和市场信息',
    status: 'pending',
  },
  {
    id: 'fundamental_analysis',
    name: '基本面分析',
    description: '分析财务指标和公司基本面',
    status: 'pending',
  },
  {
    id: 'technical_analysis',
    name: '技术分析',
    description: '分析价格走势和技术指标',
    status: 'pending',
  },
  {
    id: 'sentiment_analysis',
    name: '情绪分析',
    description: '分析新闻和社交媒体情绪',
    status: 'pending',
  },
  {
    id: 'risk_assessment',
    name: '风险评估',
    description: '评估投资风险和不确定性',
    status: 'pending',
  },
  {
    id: 'decision_making',
    name: '决策制定',
    description: '综合分析结果制定交易决策',
    status: 'pending',
  },
  {
    id: 'end',
    name: '完成',
    description: '分析流程完成',
    status: 'pending',
  },
];

// 默认的工作流边
export const defaultTradingWorkflowEdges: WorkflowEdge[] = [
  { from: 'start', to: 'data_collection' },
  { from: 'data_collection', to: 'fundamental_analysis' },
  { from: 'data_collection', to: 'technical_analysis' },
  { from: 'data_collection', to: 'sentiment_analysis' },
  { from: 'fundamental_analysis', to: 'risk_assessment' },
  { from: 'technical_analysis', to: 'risk_assessment' },
  { from: 'sentiment_analysis', to: 'risk_assessment' },
  { from: 'risk_assessment', to: 'decision_making' },
  { from: 'decision_making', to: 'end' },
];
