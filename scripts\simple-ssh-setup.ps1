# Simple SSH Key Setup Script for TradingAgents
# Usage: .\simple-ssh-setup.ps1 -ServerIP "your-ip" -ServerUser "ubuntu"

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerIP,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerUser,
    
    [string]$ServerPath = "/opt/trading-agents"
)

Write-Host "SSH Key Setup for TradingAgents" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Server IP: $ServerIP" -ForegroundColor Yellow
Write-Host "Username: $ServerUser" -ForegroundColor Yellow
Write-Host "Deploy Path: $ServerPath" -ForegroundColor Yellow
Write-Host ""

# Create .ssh directory
Write-Host "Creating SSH directory..." -ForegroundColor Green
$sshDir = "$env:USERPROFILE\.ssh"
if (-not (Test-Path $sshDir)) {
    New-Item -ItemType Directory -Force -Path $sshDir | Out-Null
    Write-Host "SSH directory created: $sshDir" -ForegroundColor Green
} else {
    Write-Host "SSH directory exists: $sshDir" -ForegroundColor Green
}

# Generate SSH key
Write-Host "Generating SSH key..." -ForegroundColor Green
$keyPath = "$sshDir\trading_deploy"

if (Test-Path $keyPath) {
    $overwrite = Read-Host "SSH key exists. Overwrite? (y/N)"
    if ($overwrite -eq "y" -or $overwrite -eq "Y") {
        Remove-Item $keyPath -Force
        Remove-Item "$keyPath.pub" -Force -ErrorAction SilentlyContinue
    } else {
        Write-Host "Using existing key" -ForegroundColor Yellow
    }
}

if (-not (Test-Path $keyPath)) {
    # Generate key with empty passphrase
    & ssh-keygen -t rsa -b 4096 -C "trading-agents-deploy" -f $keyPath -N '""'
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SSH key generated successfully" -ForegroundColor Green
    } else {
        Write-Host "SSH key generation failed" -ForegroundColor Red
        exit 1
    }
}

# Read key contents
Write-Host "Reading key contents..." -ForegroundColor Green
$privateKey = Get-Content $keyPath -Raw
$publicKey = Get-Content "$keyPath.pub" -Raw

# Display public key
Write-Host ""
Write-Host "Your SSH Public Key:" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow
Write-Host $publicKey -ForegroundColor Cyan
Write-Host ""

# Instructions for server setup
Write-Host "Server Setup Instructions:" -ForegroundColor Yellow
Write-Host "1. SSH to your Ubuntu server:" -ForegroundColor White
Write-Host "   ssh $ServerUser@$ServerIP" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Run these commands on the server:" -ForegroundColor White
Write-Host "   mkdir -p ~/.ssh" -ForegroundColor Cyan
Write-Host "   nano ~/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Paste the public key above into the file" -ForegroundColor White
Write-Host ""
Write-Host "4. Set permissions:" -ForegroundColor White
Write-Host "   chmod 700 ~/.ssh" -ForegroundColor Cyan
Write-Host "   chmod 600 ~/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host "   exit" -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter after configuring the server"

# Test SSH connection
Write-Host "Testing SSH connection..." -ForegroundColor Green
$testResult = & ssh -i $keyPath -o "StrictHostKeyChecking=no" "$ServerUser@$ServerIP" "echo 'Connection successful'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "SSH connection test passed!" -ForegroundColor Green
} else {
    Write-Host "SSH connection test failed. Please check server configuration." -ForegroundColor Red
    exit 1
}

# Generate GitHub Secrets configuration
Write-Host "Generating GitHub Secrets configuration..." -ForegroundColor Green

$deployUrl = "http://$ServerIP`:3000"
$apiUrl = "http://$ServerIP`:5000"
$wsUrl = "ws://$ServerIP`:8000"

$secretsConfig = @"
# GitHub Secrets Configuration
# Add these to your GitHub repository: Settings > Secrets and variables > Actions

# Server Connection
DEPLOY_SSH_KEY=$privateKey
DEPLOY_HOST=$ServerIP
DEPLOY_USER=$ServerUser
DEPLOY_PATH=$ServerPath

# Application URLs
DEPLOY_URL=$deployUrl
API_URL=$apiUrl
WS_URL=$wsUrl

# Database Configuration (replace with your actual passwords)
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_user_password

# Aliyun Container Registry (already configured)
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
"@

# Save configuration to file
$secretsConfig | Out-File -FilePath "github_secrets_config.txt" -Encoding UTF8
Write-Host "GitHub Secrets configuration saved to: github_secrets_config.txt" -ForegroundColor Green

# Copy private key to clipboard
$privateKey | Set-Clipboard
Write-Host "Private key copied to clipboard" -ForegroundColor Green

# Final instructions
Write-Host ""
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open github_secrets_config.txt to see all required secrets" -ForegroundColor White
Write-Host "2. Go to GitHub repository Settings > Secrets and variables > Actions" -ForegroundColor White
Write-Host "3. Add each secret from the configuration file" -ForegroundColor White
Write-Host "4. Set your database passwords in MYSQL_ROOT_PASSWORD and MYSQL_PASSWORD" -ForegroundColor White
Write-Host "5. Push code to develop or main branch to test deployment" -ForegroundColor White
Write-Host ""
Write-Host "Note: Private key is already copied to clipboard for DEPLOY_SSH_KEY" -ForegroundColor Yellow
