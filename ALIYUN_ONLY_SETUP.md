# 阿里云容器镜像服务专用配置完成

🎉 **您的 CI/CD 流程已成功配置为仅使用阿里云容器镜像服务！**

## 📋 配置概览

### 镜像注册表
- **阿里云容器镜像服务**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend`
- **用户名**: `aliyun1315382626`
- **密码**: `ezreal123`

### 优势
- ✅ **国内访问速度快** - 阿里云在国内的网络优化
- ✅ **稳定可靠** - 阿里云企业级服务保障
- ✅ **成本优化** - 避免多注册表的重复存储成本
- ✅ **简化管理** - 单一镜像源，管理更简单

## 🔧 已更新的文件

### CI/CD 工作流
- ✅ `.github/workflows/ci.yml` - 移除 GitHub Container Registry，仅使用阿里云
- ✅ `.github/workflows/docker.yml` - 更新为阿里云专用配置
- ✅ `.github/workflows/deploy.yml` - 更新环境变量为阿里云

### 文档更新
- ✅ `docs/CI_CD_GUIDE.md` - 更新镜像注册表信息
- ✅ `docs/ALIYUN_DEPLOYMENT.md` - 更新为阿里云专用指南
- ✅ `CI_CD_README.md` - 更新特性描述

## 🚀 立即开始

### 1. 确认 GitHub Secrets 配置

确保在 GitHub 仓库中已添加以下 Secrets：

```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

### 2. 测试 CI/CD 流程

```bash
# 创建测试分支
git checkout -b test/aliyun-only

# 提交更改
git add .
git commit -m "feat: 配置阿里云专用容器镜像服务"

# 推送并观察 CI/CD 流程
git push origin test/aliyun-only
```

### 3. 验证镜像推送

推送代码后，检查 GitHub Actions 是否成功：
- ✅ 代码质量检查通过
- ✅ 安全扫描完成
- ✅ Docker 镜像构建成功
- ✅ 推送到阿里云容器镜像服务成功

## 🔄 自动化流程

### 触发条件和行为

| 分支/事件 | 阿里云推送 | 镜像标签 |
|-----------|-----------|----------|
| `main` 分支推送 | ✅ | `latest` |
| `develop` 分支推送 | ✅ | `staging` |
| Pull Request | ❌ 仅构建 | `pr-<number>` |
| 手动触发 | ✅ | 自定义 |

### 镜像标签规则

- `latest` - main 分支最新版本
- `staging` - develop 分支最新版本
- `main-<sha>` - main 分支特定提交
- `develop-<sha>` - develop 分支特定提交

## 🛠️ 手动操作

### 本地推送镜像
```bash
# 登录阿里云
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com

# 构建镜像
docker build -t frontend:latest -f docker/Dockerfile .

# 标记镜像
docker tag frontend:latest crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 推送镜像
docker push crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

### 拉取和运行镜像
```bash
# 拉取最新镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 运行容器
docker run -d -p 3000:3000 --name frontend \
  crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

## 🧪 测试脚本

### Linux/macOS
```bash
# 运行阿里云设置和测试脚本
./scripts/setup-aliyun.sh

# 仅测试现有配置
./scripts/setup-aliyun.sh --test-only
```

### Windows
```powershell
# 运行阿里云设置和测试脚本
.\scripts\setup-aliyun.ps1

# 仅测试现有配置
.\scripts\setup-aliyun.ps1 -TestOnly
```

## 📊 配置对比

### 之前（双注册表）
- GitHub Container Registry + 阿里云
- 需要管理两套密钥
- 镜像存储在两个地方
- 推送时间较长

### 现在（阿里云专用）
- 仅阿里云容器镜像服务
- 单一密钥管理
- 专注国内优化
- 推送速度更快

## 🔧 故障排除

### 常见问题

1. **推送失败**
   - 检查 GitHub Secrets 是否正确配置
   - 验证阿里云服务状态
   - 确认网络连接

2. **镜像拉取慢**
   - 使用阿里云镜像加速器
   - 检查网络环境
   - 考虑使用 CDN

3. **构建失败**
   - 查看 GitHub Actions 日志
   - 检查 Dockerfile 语法
   - 验证依赖项

### 获取帮助
- 查看 [阿里云部署指南](docs/ALIYUN_DEPLOYMENT.md)
- 检查 GitHub Actions 运行日志
- 运行本地测试脚本
- 联系项目维护者

## 📚 相关文档

- [阿里云部署详细指南](docs/ALIYUN_DEPLOYMENT.md)
- [CI/CD 使用指南](docs/CI_CD_GUIDE.md)
- [GitHub Secrets 配置](/.github/SECRETS_SETUP.md)

## 🎯 下一步

1. **验证配置**: 推送代码测试 CI/CD 流程
2. **监控性能**: 观察构建和推送速度
3. **优化配置**: 根据使用情况调整设置
4. **团队培训**: 确保团队了解新的工作流程

---

**🎉 您的项目现在专门使用阿里云容器镜像服务！享受更快的国内访问速度和简化的管理流程！**

如有任何问题，请参考相关文档或联系项目维护者。
