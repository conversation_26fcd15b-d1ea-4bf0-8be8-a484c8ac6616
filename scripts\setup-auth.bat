@echo off
REM TradingAgents 用户认证系统初始化脚本 (Windows)
REM 用于设置数据库和启动项目

echo 🚀 TradingAgents 用户认证系统初始化
echo ==================================

REM 检查MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL 客户端未安装，请先安装 MySQL
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查环境变量
if not exist .env.local (
    echo ⚠️  未找到 .env.local 文件，将使用 .env.example 作为模板
    copy .env.example .env.local
    echo 📝 请编辑 .env.local 文件，填入实际的配置值
)

REM 检查数据库连接
echo 🔍 检查数据库连接...

REM 从.env.local读取配置
for /f "tokens=1,2 delims==" %%a in (.env.local) do (
    if "%%a"=="DB_HOST" set DB_HOST=%%b
    if "%%a"=="DB_USER" set DB_USER=%%b
    if "%%a"=="DB_PASSWORD" set DB_PASSWORD=%%b
    if "%%a"=="DB_NAME" set DB_NAME=%%b
    if "%%a"=="DB_PORT" set DB_PORT=%%b
)

REM 去除空格
set DB_HOST=%DB_HOST: =%
set DB_USER=%DB_USER: =%
set DB_PASSWORD=%DB_PASSWORD: =%
set DB_NAME=%DB_NAME: =%
set DB_PORT=%DB_PORT: =%

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%" 2>nul
if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，请检查配置
    echo 数据库配置：
    echo   Host: %DB_HOST%
    echo   Port: %DB_PORT%
    echo   User: %DB_USER%
    echo   Database: %DB_NAME%
    pause
    exit /b 1
)

echo ✅ 数据库连接成功

REM 初始化用户认证数据库
echo 📊 初始化用户认证数据库...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < database/user_init.sql
if %errorlevel% neq 0 (
    echo ❌ 用户认证数据库初始化失败
    pause
    exit /b 1
)
echo ✅ 用户认证数据库初始化成功

REM 检查依赖
echo 📦 检查项目依赖...
call npm install

REM 启动开发服务器
echo 🚀 启动开发服务器...
echo ==================================
echo 访问地址：
echo   首页: http://localhost:3000
echo   注册: http://localhost:3000/register
echo   登录: http://localhost:3000/login
echo   任务列表: http://localhost:3000/tasks
echo   创建任务: http://localhost:3000/create-task
echo ==================================

call npm run dev

pause
