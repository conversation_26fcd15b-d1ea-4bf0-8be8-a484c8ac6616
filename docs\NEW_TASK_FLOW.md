# 新任务流程设计文档

## 概述

本文档描述了重新设计后的任务流程，主要变更是移除了 `conversation` 概念，简化为任务直接关联消息的结构。

## 主要变更

### 1. 数据库结构变更

#### 移除的表
- `conversations` 表已被完全移除

#### 修改的表
- `messages` 表：
  - 移除 `conversation_id` 字段
  - 添加 `thread_id` 字段（可选）
  - 直接通过 `task_id` 关联任务

- `analysis_steps` 表：
  - 移除 `conversation_id` 字段

#### 更新的视图
- `task_overview` 视图：
  - 移除 `conversation_count` 字段
  - 其他统计信息保持不变

### 2. API 接口变更

#### 新增接口
- `PATCH /api/database/tasks/{id}/status` - 更新任务状态

#### 修改的接口
- `GET /api/database/messages` - 现在只需要 `task_id` 参数
- `POST /api/database/messages` - 移除 `conversation_id`，添加 `thread_id`
- `POST /api/langgraph/analyze` - 使用 `task_id` 替代 `conversation_id`

#### 移除的接口
- 所有 `/api/database/conversations/*` 相关接口

### 3. 前端代码变更

#### 任务启动流程
```javascript
// 旧流程
1. 创建任务
2. 创建conversation
3. 启动分析

// 新流程
1. 创建任务
2. 直接启动分析（传递task_id）
```

#### 消息查询
```javascript
// 旧方式
GET /api/database/messages?conversation_id=xxx

// 新方式
GET /api/database/messages?task_id=xxx
```

## 新流程说明

### 1. 任务创建
用户通过前端创建任务，任务包含以下信息：
- 股票代码 (ticker)
- 任务标题和描述
- 分析配置
- 研究深度和分析周期

### 2. 任务启动
用户在任务列表中选择任务并点击"开始"：
1. 前端更新任务状态为 `running`
2. 调用 `/api/langgraph/analyze` 接口，传递 `task_id`
3. 后端接收请求并开始分析流程

### 3. 消息记录
分析过程中的所有消息直接关联到 `task_id`：
- 系统消息：记录任务状态变更
- AI消息：记录分析结果
- 工具调用：记录API调用和结果

### 4. 消息查询
前端通过 `task_id` 查询所有相关消息，支持：
- 按消息类型过滤
- 按线程ID过滤
- 分页查询
- 包含/排除元数据

## 数据库迁移

### 执行迁移
```sql
-- 执行迁移脚本
source database/migration_remove_conversations.sql
```

### 迁移内容
1. 备份现有数据
2. 删除外键约束
3. 修改表结构
4. 重新创建索引
5. 删除conversations表
6. 验证数据完整性

## 测试

### 1. Python测试脚本
```bash
cd tests
python test_new_flow.py
```

### 2. JavaScript测试（浏览器）
1. 打开浏览器开发者工具
2. 在控制台中粘贴 `tests/test_new_flow.js` 的内容
3. 运行 `testNewFlow()`

### 3. 测试内容
- 任务创建和查询
- 任务状态更新
- 消息添加和查询
- API接口参数验证
- 数据结构验证

## 优势

### 1. 简化的架构
- 减少了一个中间层（conversation）
- 数据关系更直接清晰
- 减少了数据冗余

### 2. 更好的性能
- 减少了JOIN查询
- 索引更简单高效
- 查询路径更短

### 3. 更容易维护
- 代码逻辑更简单
- 错误处理更直接
- 调试更容易

### 4. 更符合业务逻辑
- 一个任务就是一个完整的分析流程
- 消息直接属于任务
- 状态管理更清晰

## 兼容性说明

### 向后兼容
- 现有的任务数据保持不变
- 消息数据会被迁移到新结构
- API版本保持兼容

### 迁移注意事项
1. 执行迁移前请备份数据库
2. 确保没有正在运行的任务
3. 迁移后验证数据完整性
4. 更新相关文档和代码

## 故障排除

### 常见问题

#### 1. 迁移失败
- 检查数据库权限
- 确保没有外键约束冲突
- 验证数据完整性

#### 2. API调用失败
- 检查参数是否正确（使用task_id而不是conversation_id）
- 验证任务是否存在
- 检查任务状态

#### 3. 消息查询为空
- 确认task_id正确
- 检查消息是否已正确迁移
- 验证API参数

### 调试技巧
1. 使用浏览器开发者工具查看网络请求
2. 检查数据库日志
3. 使用测试脚本验证功能
4. 查看应用程序日志

## 后续计划

1. 监控新流程的性能表现
2. 收集用户反馈
3. 优化查询性能
4. 完善错误处理
5. 更新相关文档
