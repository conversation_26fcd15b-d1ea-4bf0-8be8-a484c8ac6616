# TradingAgents Frontend 技术栈

## 核心技术栈

### 前端框架

- **Next.js 15.3**: 全栈 React 框架，支持 SSR 和 API Routes
- **React 18**: 现代化的用户界面库，支持并发特性
- **TypeScript 5.0**: 类型安全，提升开发效率和代码质量

### UI 和样式

- **Tailwind CSS 3.3**: 实用优先的 CSS 框架，快速构建响应式界面
- **Headless UI**: 无样式的可访问组件
- **Heroicons**: 精美的 SVG 图标
- **Lucide React**: 现代图标库
- **Framer Motion**: 动画库

### 状态管理

- **TanStack Query**: 服务器状态管理，缓存和同步
- **Zustand**: 轻量级客户端状态管理
- **Axios**: HTTP 客户端

### AI 和工作流

- **LangGraph 0.3**: 智能工作流引擎，支持复杂的多智能体协作
- **LangChain.js**: 大语言模型集成框架
- **OpenAI API**: 支持 GPT-4、o1-preview 等先进模型
- **Zod**: 数据验证和类型安全

### 数据可视化

- **Recharts**: React 图表库

### 数据库

- **MySQL 8.0**: 关系型数据库，存储任务、消息和分析结果

### 实时通信

- **Socket.io**: 实时通信，支持 WebSocket

### 开发工具

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **PostCSS**: CSS 处理
- **Autoprefixer**: CSS 前缀自动添加

## 环境要求

- **Node.js**: 18.0 或更高版本
- **npm 或 yarn**: 包管理器

## 常用命令

### 开发命令

```bash
# 安装依赖
npm install
# 或
yarn install

# 启动开发服务器
npm run dev
# 或
yarn dev

# 代码检查
npm run lint
# 或
yarn lint

# 类型检查
npm run type-check
# 或
yarn type-check
```

### 构建命令

```bash
# 构建生产版本
npm run build
# 或
yarn build

# 启动生产服务器
npm run start
# 或
yarn start
```

### Docker 命令

```bash
# 使用 Docker Compose 启动
docker-compose up -d

# 仅启动前端
docker-compose up frontend -d

# 查看日志
docker-compose logs -f frontend

# 停止服务
docker-compose down
```

## 环境变量配置

项目使用 `.env` 文件进行环境配置，主要包括：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# OpenAI API配置
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_OPENAI_BASE_URL=https://api.openai.com/v1

# FinnHub API配置
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key

# 后端URL
BACK_END_URL=http://localhost:5000
```

## 代码规范

### 命名约定

- **文件命名**:

  - 组件文件使用 PascalCase (如 `Button.tsx`)
  - 工具和钩子文件使用 camelCase (如 `useAuth.ts`)
  - 常量文件使用 camelCase (如 `constants.ts`)

- **变量命名**:
  - 常量使用 UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
  - 变量和函数使用 camelCase (如 `getUserData`)
  - 组件使用 PascalCase (如 `UserProfile`)
  - 类型和接口使用 PascalCase (如 `UserData`)

### 代码风格

- 使用 TypeScript 类型定义
- 使用函数式组件和 React Hooks
- 避免使用 `any` 类型
- 使用 async/await 处理异步操作
- 使用 try/catch 进行错误处理

### 组件结构

```tsx
// 组件结构示例
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ComponentProps {
  // 定义 props 类型
}

export function Component({}: ComponentProps) {
  // 组件逻辑
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      {/* 组件内容 */}
    </motion.div>
  );
}
```

## API 集成

### 后端接口设计

```typescript
// 分析管理
POST / api / analysis / start; // 开始分析
GET / api / analysis / { id } / status; // 获取分析状态
GET / api / analysis / { id } / agents; // 获取代理状态
GET / api / analysis / { id } / reports; // 获取分析报告
GET / api / analysis / { id } / decision; // 获取交易决策
POST / api / analysis / { id } / stop; // 停止分析

// 数据接口
GET / api / data / stock / { ticker }; // 股票数据
GET / api / data / news / { ticker }; // 新闻数据
GET / api / data / technical / { ticker }; // 技术指标
GET / api / data / fundamentals / { ticker }; // 基本面数据

// WebSocket
WS / ws / analysis / { id }; // 实时数据推送
```

## 部署方案

### 开发环境

```bash
# 快速启动
npm install
npm run dev

# 或使用提供的脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 生产环境

```bash
# 构建生产版本
npm run build
npm start

# 或使用 Docker
docker-compose up -d
```

### Docker 部署

- **多阶段构建**: 优化镜像大小
- **健康检查**: 自动监控服务状态
- **反向代理**: Nginx 配置 SSL 和负载均衡
