#!/bin/bash

# 阿里云容器镜像服务快速设置脚本
# 用于配置本地 Docker 环境和测试阿里云镜像推送

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 阿里云配置
ALIYUN_REGISTRY="crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com"
ALIYUN_USERNAME="aliyun1315382626"
ALIYUN_PASSWORD="ezreal123"
ALIYUN_NAMESPACE="ez_trading"
ALIYUN_REPO="frontend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                阿里云容器镜像服务设置                         ║
║              TradingAgents Frontend                          ║
╚══════════════════════════════════════════════════════════════╝

此脚本将帮助您：
✓ 配置阿里云容器镜像服务登录
✓ 测试镜像推送和拉取
✓ 验证 CI/CD 配置

EOF
}

# 显示配置信息
show_config() {
    log_info "阿里云容器镜像服务配置："
    echo "  注册表地址: $ALIYUN_REGISTRY"
    echo "  用户名: $ALIYUN_USERNAME"
    echo "  命名空间: $ALIYUN_NAMESPACE"
    echo "  仓库名称: $ALIYUN_REPO"
    echo ""
}

# 检查 Docker 环境
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker > /dev/null 2>&1; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请启动 Docker"
        exit 1
    fi
    
    DOCKER_VERSION=$(docker --version)
    log_success "Docker 环境正常: $DOCKER_VERSION"
}

# 登录阿里云容器镜像服务
login_aliyun() {
    log_info "登录阿里云容器镜像服务..."
    
    echo "$ALIYUN_PASSWORD" | docker login --username="$ALIYUN_USERNAME" --password-stdin "$ALIYUN_REGISTRY"
    
    if [ $? -eq 0 ]; then
        log_success "阿里云容器镜像服务登录成功"
    else
        log_error "阿里云容器镜像服务登录失败"
        exit 1
    fi
}

# 构建测试镜像
build_test_image() {
    log_info "构建测试镜像..."
    
    if [ ! -f "docker/Dockerfile" ]; then
        log_error "Dockerfile 不存在: docker/Dockerfile"
        exit 1
    fi
    
    # 构建镜像
    docker build -t "test-frontend:local" -f docker/Dockerfile .
    
    if [ $? -eq 0 ]; then
        log_success "测试镜像构建成功"
    else
        log_error "测试镜像构建失败"
        exit 1
    fi
}

# 推送测试镜像
push_test_image() {
    log_info "推送测试镜像到阿里云..."
    
    # 标记镜像
    ALIYUN_IMAGE="$ALIYUN_REGISTRY/$ALIYUN_NAMESPACE/$ALIYUN_REPO:test-$(date +%Y%m%d-%H%M%S)"
    docker tag "test-frontend:local" "$ALIYUN_IMAGE"
    
    # 推送镜像
    docker push "$ALIYUN_IMAGE"
    
    if [ $? -eq 0 ]; then
        log_success "测试镜像推送成功: $ALIYUN_IMAGE"
        echo "PUSHED_IMAGE=$ALIYUN_IMAGE" > /tmp/aliyun_test_image
    else
        log_error "测试镜像推送失败"
        exit 1
    fi
}

# 拉取并测试镜像
test_pull_image() {
    log_info "测试镜像拉取..."
    
    if [ ! -f "/tmp/aliyun_test_image" ]; then
        log_warning "未找到推送的测试镜像信息，跳过拉取测试"
        return
    fi
    
    source /tmp/aliyun_test_image
    
    # 删除本地镜像
    docker rmi "$PUSHED_IMAGE" 2>/dev/null || true
    
    # 重新拉取镜像
    docker pull "$PUSHED_IMAGE"
    
    if [ $? -eq 0 ]; then
        log_success "镜像拉取测试成功"
    else
        log_error "镜像拉取测试失败"
        exit 1
    fi
}

# 运行容器测试
test_container() {
    log_info "测试容器运行..."
    
    if [ ! -f "/tmp/aliyun_test_image" ]; then
        log_warning "未找到测试镜像信息，跳过容器测试"
        return
    fi
    
    source /tmp/aliyun_test_image
    
    # 运行测试容器
    CONTAINER_NAME="aliyun-test-frontend"
    docker run -d --name "$CONTAINER_NAME" -p 3001:3000 "$PUSHED_IMAGE"
    
    if [ $? -eq 0 ]; then
        log_success "测试容器启动成功"
        
        # 等待容器启动
        sleep 10
        
        # 检查容器状态
        if docker ps | grep -q "$CONTAINER_NAME"; then
            log_success "容器运行正常"
            
            # 测试 HTTP 访问
            if curl -f -s http://localhost:3001 > /dev/null 2>&1; then
                log_success "HTTP 访问测试成功"
            else
                log_warning "HTTP 访问测试失败，但容器正在运行"
            fi
        else
            log_error "容器启动失败"
        fi
        
        # 清理测试容器
        docker stop "$CONTAINER_NAME" > /dev/null 2>&1
        docker rm "$CONTAINER_NAME" > /dev/null 2>&1
        log_info "测试容器已清理"
    else
        log_error "测试容器启动失败"
    fi
}

# 清理测试资源
cleanup() {
    log_info "清理测试资源..."
    
    # 删除本地测试镜像
    docker rmi "test-frontend:local" 2>/dev/null || true
    
    # 删除推送的测试镜像（可选）
    if [ -f "/tmp/aliyun_test_image" ]; then
        source /tmp/aliyun_test_image
        read -p "是否删除推送的测试镜像 $PUSHED_IMAGE? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker rmi "$PUSHED_IMAGE" 2>/dev/null || true
            log_info "已删除推送的测试镜像"
        fi
        rm -f /tmp/aliyun_test_image
    fi
    
    log_success "清理完成"
}

# 显示 GitHub Secrets 配置提示
show_github_secrets() {
    log_info "GitHub Secrets 配置提示："
    
    cat << EOF

请在 GitHub 仓库中添加以下 Secrets：

1. 进入 GitHub 仓库页面
2. 点击 Settings > Secrets and variables > Actions
3. 添加以下 Repository secrets：

   ALIYUN_REGISTRY_USERNAME=$ALIYUN_USERNAME
   ALIYUN_REGISTRY_PASSWORD=$ALIYUN_PASSWORD

配置完成后，推送代码到 main 或 develop 分支，
CI/CD 流程将自动构建并推送镜像到阿里云。

EOF
}

# 显示使用说明
show_usage() {
    cat << EOF
用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -t, --test-only     仅运行测试，不构建新镜像
    -c, --cleanup       仅清理测试资源
    --skip-build        跳过镜像构建
    --skip-push         跳过镜像推送
    --skip-test         跳过容器测试

示例:
    $0                  # 完整测试流程
    $0 --test-only      # 仅测试现有镜像
    $0 --cleanup        # 清理测试资源

EOF
}

# 解析命令行参数
TEST_ONLY=false
CLEANUP_ONLY=false
SKIP_BUILD=false
SKIP_PUSH=false
SKIP_TEST=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -t|--test-only)
            TEST_ONLY=true
            shift
            ;;
        -c|--cleanup)
            CLEANUP_ONLY=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-push)
            SKIP_PUSH=true
            shift
            ;;
        --skip-test)
            SKIP_TEST=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 主函数
main() {
    show_welcome
    show_config
    
    # 如果只是清理
    if [ "$CLEANUP_ONLY" = true ]; then
        cleanup
        exit 0
    fi
    
    # 检查 Docker 环境
    check_docker
    
    # 登录阿里云
    login_aliyun
    
    # 如果不是仅测试模式
    if [ "$TEST_ONLY" = false ]; then
        # 构建镜像
        if [ "$SKIP_BUILD" = false ]; then
            build_test_image
        fi
        
        # 推送镜像
        if [ "$SKIP_PUSH" = false ]; then
            push_test_image
        fi
    fi
    
    # 测试镜像拉取
    if [ "$SKIP_TEST" = false ]; then
        test_pull_image
        test_container
    fi
    
    # 显示 GitHub Secrets 配置提示
    show_github_secrets
    
    log_success "阿里云容器镜像服务配置和测试完成！"
    
    # 询问是否清理
    read -p "是否清理测试资源? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    fi
}

# 运行主函数
main "$@"
