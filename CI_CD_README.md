# TradingAgents Frontend CI/CD 配置完成

🎉 **恭喜！您的 CI/CD 自动化流程已经配置完成！**

## 📁 已创建的文件

### GitHub Actions 工作流

- `.github/workflows/ci.yml` - 主要 CI/CD 流程
- `.github/workflows/docker.yml` - Docker 构建和推送
- `.github/workflows/deploy.yml` - 自动化部署流程

### 配置和文档

- `.github/SECRETS_SETUP.md` - GitHub Secrets 配置指南
- `.env.example` - 环境变量配置模板（已更新）
- `docs/CI_CD_GUIDE.md` - 详细使用指南

### 部署脚本

- `scripts/deploy.sh` - Linux/macOS 部署脚本
- `scripts/deploy.ps1` - Windows PowerShell 部署脚本
- `scripts/setup-cicd.sh` - CI/CD 环境快速设置脚本

## 🚀 快速开始

### 1. 配置环境变量

```bash
# 复制环境配置模板
cp .env.example .env.local

# 编辑配置文件，填入您的 API 密钥
# 重要：填入 NEXT_PUBLIC_OPENAI_API_KEY 和 NEXT_PUBLIC_FINNHUB_API_KEY
```

### 2. 设置 GitHub Secrets

在 GitHub 仓库中设置以下 Secrets：

- `MYSQL_ROOT_PASSWORD_STAGING`
- `MYSQL_ROOT_PASSWORD_PROD`
- `MYSQL_PASSWORD_STAGING`
- `MYSQL_PASSWORD_PROD`
- `NEXT_PUBLIC_OPENAI_API_KEY`
- `NEXT_PUBLIC_FINNHUB_API_KEY`

详细配置请参考：`.github/SECRETS_SETUP.md`

### 3. 本地开发

```bash
# 安装依赖
npm ci

# 启动开发服务器
npm run dev

# 代码检查
npm run lint
npm run type-check

# 构建应用
npm run build
```

### 4. Docker 部署

```bash
# 启动完整环境（包括数据库）
docker-compose -f docker/docker-compose.yml up -d

# 仅启动前端
docker-compose -f docker/docker-compose-frontend-only.yml up -d
```

### 5. 使用部署脚本

#### Linux/macOS

```bash
# 部署到预发布环境
./scripts/deploy.sh staging

# 部署到生产环境（需要确认）
./scripts/deploy.sh production --backup

# 检查环境状态
./scripts/deploy.sh staging --check

# 回滚操作
./scripts/deploy.sh staging --rollback
```

#### Windows

```powershell
# 部署到预发布环境
.\scripts\deploy.ps1 -Environment staging

# 部署到生产环境
.\scripts\deploy.ps1 -Environment production -Backup

# 检查环境状态
.\scripts\deploy.ps1 -Environment staging -CheckOnly

# 回滚操作
.\scripts\deploy.ps1 -Environment staging -Rollback
```

## 🔄 CI/CD 工作流程

### 自动触发

1. **代码推送到 `main` 或 `develop` 分支**

   - 自动运行代码检查、构建、安全扫描
   - 构建并推送 Docker 镜像
   - 自动部署到对应环境

2. **创建 Pull Request**
   - 运行代码质量检查
   - 执行构建验证
   - 运行安全扫描

### 手动触发

- 在 GitHub Actions 页面手动触发部署
- 选择目标环境和部署选项
- 支持强制部署和回滚操作

## 🛡️ 安全特性

- **代码扫描**: ESLint、TypeScript 检查
- **依赖扫描**: npm audit 安全检查
- **容器扫描**: Trivy 安全扫描
- **敏感信息检测**: TruffleHog 扫描
- **密钥管理**: GitHub Secrets 安全存储
- **阿里云集成**: 推送到阿里云容器镜像服务，国内访问更快

## 📊 监控和日志

- **健康检查**: 自动检测服务状态
- **部署日志**: 详细的部署过程记录
- **错误告警**: 部署失败自动通知
- **性能监控**: Lighthouse CI 性能测试

## 🔧 环境配置

### 开发环境 (dev)

- 本地开发和测试
- 热重载和调试功能
- 使用 `docker-compose.dev.yml`

### 预发布环境 (staging)

- 集成测试和验收测试
- 模拟生产环境配置
- 自动部署 `develop` 分支

### 生产环境 (production)

- 正式运行环境
- 需要手动确认部署
- 自动备份和回滚机制

## 📚 相关文档

- [CI/CD 详细使用指南](docs/CI_CD_GUIDE.md)
- [阿里云部署指南](docs/ALIYUN_DEPLOYMENT.md)
- [GitHub Secrets 配置](/.github/SECRETS_SETUP.md)
- [Docker 部署说明](docker/README.md)
- [项目总体说明](README.md)

## 🆘 故障排除

### 常见问题

1. **构建失败**: 检查 Node.js 版本和依赖
2. **部署失败**: 验证环境配置和密钥
3. **健康检查失败**: 检查服务端口和网络
4. **权限错误**: 确认 GitHub Secrets 配置

### 获取帮助

- 查看 GitHub Actions 运行日志
- 检查 Docker 容器日志
- 参考故障排除文档
- 联系项目维护者

## 🎯 下一步

1. **配置 API 密钥**: 在 `.env.local` 中填入真实的 API 密钥
2. **设置 GitHub Secrets**: 按照指南配置所有必需的密钥
3. **测试部署流程**: 先在预发布环境测试
4. **监控和优化**: 根据实际使用情况调整配置

---

**🎉 您的 CI/CD 流程已经准备就绪！开始享受自动化开发和部署的便利吧！**

如有任何问题，请参考相关文档或联系项目维护者。
