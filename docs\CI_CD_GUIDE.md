# TradingAgents Frontend CI/CD 使用指南

本文档详细说明了 TradingAgents Frontend 项目的 CI/CD 流程配置和使用方法。

## 📋 目录

- [概述](#概述)
- [工作流程](#工作流程)
- [配置说明](#配置说明)
- [使用方法](#使用方法)
- [部署脚本](#部署脚本)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🎯 概述

我们的 CI/CD 流程包含以下主要组件：

- **持续集成 (CI)**: 代码质量检查、构建验证、安全扫描
- **持续部署 (CD)**: 自动化部署到不同环境
- **Docker 构建**: 容器化应用程序
- **环境管理**: 多环境配置和密钥管理

## 🔄 工作流程

### 1. 主要工作流 (`.github/workflows/ci.yml`)

**触发条件:**

- 推送到 `main` 或 `develop` 分支
- 创建 Pull Request
- 手动触发

**执行步骤:**

1. **代码质量检查**

   - ESLint 代码检查
   - TypeScript 类型检查
   - 应用程序构建验证

2. **安全扫描**

   - npm audit 依赖安全检查
   - Trivy 文件系统扫描
   - TruffleHog 敏感信息检查

3. **单元测试** (预留)
   - Jest 单元测试
   - 代码覆盖率报告

### 2. Docker 构建工作流 (`.github/workflows/docker.yml`)

**触发条件:**

- 推送到主要分支
- 创建标签
- 手动触发

**执行步骤:**

1. 构建多架构 Docker 镜像 (amd64, arm64)
2. 推送到阿里云容器镜像服务
3. 镜像安全扫描
4. 镜像测试验证

**镜像注册表:**

- **阿里云**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend`

### 3. 部署工作流 (`.github/workflows/deploy.yml`)

**触发条件:**

- CI 工作流成功完成
- 手动触发部署

**执行步骤:**

1. **预发布环境** (`develop` 分支)

   - 自动部署到 staging 环境
   - 健康检查和集成测试

2. **生产环境** (`main` 分支)
   - 需要手动确认
   - 数据库备份
   - 蓝绿部署
   - 自动回滚机制

## ⚙️ 配置说明

### 环境变量配置

#### 开发环境

```bash
# 复制环境配置模板
cp .env.example .env.local

# 编辑配置文件
nano .env.local
```

#### GitHub Secrets 配置

在 GitHub 仓库设置中添加以下 Secrets：

```
# 数据库密钥
MYSQL_ROOT_PASSWORD_STAGING=your_staging_password
MYSQL_ROOT_PASSWORD_PROD=your_production_password
MYSQL_PASSWORD_STAGING=your_staging_user_password
MYSQL_PASSWORD_PROD=your_production_user_password

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_key
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_key

# 阿里云容器镜像服务
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123

# 部署密钥
DEPLOY_SSH_KEY=your_ssh_private_key
DEPLOY_HOST=your_server_host
DEPLOY_USER=your_deploy_user
```

详细配置请参考 [GitHub Secrets 配置指南](.github/SECRETS_SETUP.md)。

### Docker 配置

项目包含多个 Docker Compose 配置文件：

- `docker/docker-compose.dev.yml` - 开发环境
- `docker/docker-compose.yml` - 预发布环境
- `docker/docker-compose.prod.yml` - 生产环境

## 🚀 使用方法

### 1. 开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和测试
npm run dev
npm run lint
npm run type-check

# 3. 提交代码
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# 4. 创建 Pull Request
# CI 会自动运行代码检查和构建验证
```

### 2. 部署流程

#### 自动部署

- **预发布**: 合并到 `develop` 分支自动部署
- **生产**: 合并到 `main` 分支需要手动确认

#### 手动部署

```bash
# 使用 GitHub Actions 手动触发
# 1. 进入 Actions 页面
# 2. 选择 "Deploy Application" 工作流
# 3. 点击 "Run workflow"
# 4. 选择环境和选项
```

#### 本地部署

```bash
# Linux/macOS
./scripts/deploy.sh staging --backup

# Windows
.\scripts\deploy.ps1 -Environment staging -Backup
```

### 3. 监控和维护

#### 查看部署状态

```bash
# 检查服务状态
./scripts/deploy.sh staging --check

# 查看容器日志
docker-compose -f docker/docker-compose.yml logs -f
```

#### 回滚操作

```bash
# 自动回滚到上一个版本
./scripts/deploy.sh production --rollback

# 手动回滚
docker-compose -f docker/docker-compose.prod.yml down
docker-compose -f docker/docker-compose.prod.yml up -d
```

## 🛠️ 部署脚本

### Linux/macOS 脚本 (`scripts/deploy.sh`)

```bash
# 基本用法
./scripts/deploy.sh <environment> [options]

# 示例
./scripts/deploy.sh staging                  # 部署到预发布
./scripts/deploy.sh production --backup      # 部署到生产并备份
./scripts/deploy.sh staging --rollback       # 回滚预发布环境
./scripts/deploy.sh production --check       # 检查生产环境状态
```

### Windows 脚本 (`scripts/deploy.ps1`)

```powershell
# 基本用法
.\scripts\deploy.ps1 -Environment <env> [options]

# 示例
.\scripts\deploy.ps1 -Environment staging
.\scripts\deploy.ps1 -Environment production -Backup
.\scripts\deploy.ps1 -Environment staging -Rollback
.\scripts\deploy.ps1 -Environment production -CheckOnly
```

### 脚本选项说明

| 选项             | 说明               |
| ---------------- | ------------------ |
| `-f, --force`    | 强制部署，跳过确认 |
| `-b, --backup`   | 部署前创建备份     |
| `-r, --rollback` | 回滚到上一个版本   |
| `-c, --check`    | 仅检查环境状态     |
| `--no-build`     | 跳过构建步骤       |
| `--no-migrate`   | 跳过数据库迁移     |
| `--no-restart`   | 跳过服务重启       |

## 🔧 故障排除

### 常见问题

#### 1. 构建失败

```bash
# 检查 Node.js 版本
node --version

# 清理缓存
npm ci
rm -rf .next
npm run build
```

#### 2. Docker 构建失败

```bash
# 清理 Docker 缓存
docker system prune -f
docker-compose build --no-cache
```

#### 3. 部署失败

```bash
# 检查环境配置
cat .env.staging

# 检查服务状态
docker-compose ps
docker-compose logs
```

#### 4. 健康检查失败

```bash
# 检查服务端口
netstat -tlnp | grep :3000

# 检查应用日志
docker-compose logs frontend
```

### 调试命令

```bash
# 查看工作流运行日志
# 在 GitHub Actions 页面查看详细日志

# 本地调试
docker-compose -f docker/docker-compose.yml config
docker-compose -f docker/docker-compose.yml ps
docker-compose -f docker/docker-compose.yml logs -f frontend
```

## 📝 最佳实践

### 1. 代码质量

- 提交前运行 `npm run lint` 和 `npm run type-check`
- 编写有意义的提交信息
- 保持小而频繁的提交

### 2. 分支管理

- `main` - 生产环境代码
- `develop` - 开发集成分支
- `feature/*` - 功能开发分支
- `hotfix/*` - 紧急修复分支

### 3. 部署策略

- 预发布环境先验证
- 生产部署前创建备份
- 监控部署后的系统状态
- 准备回滚计划

### 4. 安全考虑

- 定期更新依赖包
- 使用强密码和密钥
- 限制生产环境访问权限
- 监控安全扫描结果

### 5. 性能优化

- 使用 Docker 多阶段构建
- 启用构建缓存
- 优化镜像大小
- 监控应用性能

## 📞 支持和反馈

如果遇到问题或有改进建议：

1. 查看本文档的故障排除部分
2. 检查 GitHub Issues 中的已知问题
3. 创建新的 Issue 描述问题
4. 联系项目维护者

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
