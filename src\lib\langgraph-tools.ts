import { tool } from '@langchain/core/tools';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { z } from 'zod';

// 交易分析工具定义
export const stockAnalysisTool = tool(
  async ({ ticker, analysisType }) => {
    // 模拟股票分析数据
    const mockData = {
      ticker,
      analysisType,
      data: {
        price: Math.random() * 1000 + 100,
        change: (Math.random() - 0.5) * 10,
        volume: Math.floor(Math.random() * 1000000),
        recommendation: ['买入', '持有', '卖出'][Math.floor(Math.random() * 3)],
      },
      timestamp: new Date().toISOString(),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'stock_analysis',
    description: '分析股票的基本面、技术面或新闻情绪',
    schema: z.object({
      ticker: z.string().describe('股票代码，如NVDA、AAPL等'),
      analysisType: z.enum(['fundamentals', 'technical', 'news', 'sentiment']).describe('分析类型'),
    }),
  }
);

export const marketDataTool = tool(
  async ({ ticker, dataType, period }) => {
    // 模拟市场数据
    const mockData = {
      ticker,
      dataType,
      period,
      data: Array.from({ length: 10 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 100 + 50,
      })),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'market_data',
    description: '获取股票的市场数据，包括价格、成交量等',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      dataType: z.enum(['price', 'volume', 'indicators']).describe('数据类型'),
      period: z.enum(['1d', '1w', '1m', '3m', '1y']).describe('时间周期'),
    }),
  }
);

export const newsAnalysisTool = tool(
  async ({ ticker, sentiment }) => {
    // 模拟新闻分析
    const mockData = {
      ticker,
      sentiment: sentiment ? 'positive' : 'neutral',
      news: [
        { title: `${ticker} 财报超预期`, sentiment: 'positive', impact: 'high' },
        { title: `市场对 ${ticker} 前景看好`, sentiment: 'positive', impact: 'medium' },
      ],
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'news_analysis',
    description: '分析股票相关新闻的情绪和影响',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      sentiment: z.boolean().optional().describe('是否进行情绪分析'),
    }),
  }
);

export const riskAssessmentTool = tool(
  async ({ ticker, portfolio, riskLevel }) => {
    // 模拟风险评估
    const mockData = {
      ticker,
      riskLevel,
      assessment: {
        volatility: Math.random() * 0.5,
        beta: Math.random() * 2,
        sharpeRatio: Math.random() * 3,
        recommendation: riskLevel === 'low' ? '适合保守投资者' : '适合激进投资者',
      },
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'risk_assessment',
    description: '评估投资风险和组合风险',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      portfolio: z.array(z.string()).optional().describe('投资组合中的其他股票'),
      riskLevel: z.enum(['low', 'medium', 'high']).describe('风险偏好'),
    }),
  }
);

export const tools = [stockAnalysisTool, marketDataTool, newsAnalysisTool, riskAssessmentTool];
export const toolNode = new ToolNode(tools);
