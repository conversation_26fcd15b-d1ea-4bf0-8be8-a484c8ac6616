import { clearAuthCookies, getCurrentUser } from '@/lib/auth';
import { logUserActivity } from '@/lib/user-db';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (currentUser) {
      // 获取客户端信息
      const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
      const userAgent = request.headers.get('user-agent') || 'unknown';

      // 记录登出活动
      await logUserActivity(currentUser.userId, 'logout', ipAddress, userAgent);
    }

    // 清除认证cookie
    await clearAuthCookies();

    return NextResponse.json({
      success: true,
      message: '登出成功',
    });
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
