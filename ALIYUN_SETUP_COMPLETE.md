# 阿里云容器镜像服务配置完成

🎉 **恭喜！您的 CI/CD 流程已成功配置阿里云容器镜像服务支持！**

## 📋 配置概览

### 镜像注册表配置
- **GitHub Container Registry**: `ghcr.io/your-repo/trading-agents-frontend`
- **阿里云容器镜像服务**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend`

### 阿里云服务信息
- **注册表地址**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com`
- **命名空间**: `ez_trading`
- **仓库名称**: `frontend`
- **用户名**: `aliyun1315382626`

## 🔧 已更新的文件

### CI/CD 工作流
- ✅ `.github/workflows/ci.yml` - 更新为支持双注册表推送
- ✅ `.github/workflows/docker.yml` - 保持原有配置
- ✅ `.github/workflows/deploy.yml` - 保持原有配置

### 配置和文档
- ✅ `.github/SECRETS_SETUP.md` - 添加阿里云密钥配置说明
- ✅ `docs/CI_CD_GUIDE.md` - 更新包含阿里云信息
- ✅ `docs/ALIYUN_DEPLOYMENT.md` - 新增阿里云专门指南
- ✅ `CI_CD_README.md` - 更新包含阿里云支持

### 设置脚本
- ✅ `scripts/setup-aliyun.sh` - Linux/macOS 阿里云设置脚本
- ✅ `scripts/setup-aliyun.ps1` - Windows PowerShell 阿里云设置脚本

## 🚀 立即开始

### 1. 配置 GitHub Secrets

在 GitHub 仓库中添加以下 Secrets：

```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

**配置步骤**:
1. 进入 GitHub 仓库页面
2. 点击 `Settings` > `Secrets and variables` > `Actions`
3. 点击 `New repository secret`
4. 添加上述两个密钥

### 2. 测试本地配置

#### Linux/macOS
```bash
# 运行阿里云设置脚本
./scripts/setup-aliyun.sh

# 手动测试登录
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
```

#### Windows
```powershell
# 运行阿里云设置脚本
.\scripts\setup-aliyun.ps1

# 手动测试登录
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
```

### 3. 验证 CI/CD 流程

```bash
# 创建测试分支
git checkout -b test/aliyun-cicd

# 提交更改
git add .
git commit -m "feat: 配置阿里云容器镜像服务支持"

# 推送到远程仓库
git push origin test/aliyun-cicd

# 创建 Pull Request 或合并到 develop/main 分支
```

## 🔄 自动化流程

### 触发条件和行为

| 分支/事件 | GitHub Registry | 阿里云 Registry | 标签 |
|-----------|----------------|----------------|------|
| `main` 分支推送 | ✅ 推送 | ✅ 推送 | `latest` |
| `develop` 分支推送 | ✅ 推送 | ✅ 推送 | `staging` |
| Pull Request | ✅ 构建 | ❌ 不推送 | `pr-<number>` |
| 手动触发 | ✅ 推送 | ✅ 推送 | 自定义 |

### 镜像标签规则

- `latest` - main 分支最新版本
- `staging` - develop 分支最新版本
- `main-<sha>` - main 分支特定提交
- `develop-<sha>` - develop 分支特定提交
- `pr-<number>` - Pull Request 版本

## 🛡️ 安全特性

- ✅ **双重备份**: 镜像同时存储在 GitHub 和阿里云
- ✅ **访问控制**: 使用 GitHub Secrets 安全管理密钥
- ✅ **安全扫描**: 对推送的镜像进行安全扫描
- ✅ **审计日志**: 完整的构建和推送日志记录

## 📊 优势对比

### GitHub Container Registry
- ✅ 与 GitHub 深度集成
- ✅ 免费额度充足
- ✅ 全球 CDN 分发
- ❌ 国内访问较慢

### 阿里云容器镜像服务
- ✅ 国内访问速度快
- ✅ 与阿里云生态集成
- ✅ 稳定可靠
- ❌ 需要阿里云账号

## 🔧 手动操作命令

### 推送镜像到阿里云
```bash
# 登录
docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com

# 构建镜像
docker build -t frontend:latest -f docker/Dockerfile .

# 标记镜像
docker tag frontend:latest crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 推送镜像
docker push crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

### 从阿里云拉取镜像
```bash
# 拉取最新镜像
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest

# 运行容器
docker run -d -p 3000:3000 --name frontend \
  crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
```

## 📚 相关文档

- [阿里云部署详细指南](docs/ALIYUN_DEPLOYMENT.md)
- [CI/CD 完整使用指南](docs/CI_CD_GUIDE.md)
- [GitHub Secrets 配置](/.github/SECRETS_SETUP.md)
- [总体 CI/CD 说明](CI_CD_README.md)

## 🆘 故障排除

### 常见问题

1. **阿里云登录失败**
   - 检查用户名和密码是否正确
   - 确认网络连接正常
   - 验证阿里云容器镜像服务是否开通

2. **GitHub Actions 推送失败**
   - 检查 GitHub Secrets 配置
   - 查看 Actions 运行日志
   - 确认阿里云服务状态

3. **镜像拉取缓慢**
   - 使用阿里云镜像加速器
   - 配置 Docker daemon 镜像源
   - 选择合适的网络环境

### 获取帮助

- 查看 [阿里云部署指南](docs/ALIYUN_DEPLOYMENT.md) 的故障排除部分
- 检查 GitHub Actions 运行日志
- 运行本地测试脚本进行诊断
- 联系项目维护者

## 🎯 下一步

1. **配置 GitHub Secrets**: 按照上述步骤添加阿里云密钥
2. **测试本地环境**: 运行设置脚本验证配置
3. **验证 CI/CD**: 推送代码测试自动构建和推送
4. **监控和优化**: 根据使用情况调整配置

---

**🎉 您的项目现在支持双注册表镜像推送！享受更快的国内访问速度和更可靠的镜像分发服务！**

如有任何问题，请参考相关文档或联系项目维护者。
