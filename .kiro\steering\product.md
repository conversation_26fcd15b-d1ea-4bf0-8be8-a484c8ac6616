# TradingAgents Frontend 产品概述

## 产品定位

TradingAgents Frontend 是一个企业级金融分析前端应用，专为多智能体交易分析框架设计。该项目模拟真实交易公司的组织结构和工作流程，通过多智能体协作实现专业化的金融分析和决策制定。

## 核心价值

- **企业级架构**：模拟真实交易公司的组织结构和工作流程
- **AI 驱动**：集成 LangGraph 和大语言模型，提供智能化分析能力
- **专业分析**：涵盖基本面、技术面、情绪面和风险管理的全方位分析
- **实时协作**：多智能体协同工作，模拟真实的团队决策过程
- **生产就绪**：完整的错误处理、状态管理和性能优化

## 设计理念

本项目的核心设计理念是**模拟真实交易公司的运作模式**，通过多智能体协作实现专业化的金融分析和决策制定。

### 企业级分工模式

```
交易公司组织架构
├── 分析师团队
│   ├── 基本面分析师 (Fundamental Analyst)
│   ├── 技术分析师 (Technical Analyst)
│   ├── 情绪分析师 (Sentiment Analyst)
│   └── 新闻分析师 (News Analyst)
├── 研究团队
│   ├── 多头研究员 (Bull Researcher)
│   └── 空头研究员 (Bear Researcher)
├── 风险管理团队
│   └── 风险管理师 (Risk Manager)
├── 交易执行团队
│   └── 交易员 (Trader)
└── 决策层
    └── 投资组合经理 (Portfolio Manager)
```

### 智能体协作机制

- **并行分析**：多个分析师同时工作，提高效率
- **结构化辩论**：多头和空头研究员进行观点碰撞
- **层级决策**：从分析到研究到最终决策的层级审批
- **风险控制**：全流程风险评估和管控

## 核心功能

1. **欢迎页面**：项目介绍和功能展示、分析配置表单、工作流程说明
2. **交易仪表板**：
   - 总览：分析进度和配置信息
   - 代理状态：实时监控各代理工作状态
   - 实时数据：股价、技术指标、新闻、基本面数据
   - 分析报告：各代理生成的详细报告
   - 交易决策：最终的交易建议和参数
3. **实时功能**：WebSocket 连接实时更新、自动数据刷新、状态同步
4. **LangGraph 智能分析**：对话式分析、智能工作流、工具集成、内存管理
5. **数据管理系统**：消息记录、数据库集成、API 接口、数据同步

## 业务流程

### 任务管理流程

1. **任务创建阶段**：

   ```
   用户访问首页 → 点击快速开始 → 填写分析配置 → 选择股票代码
   → 设置分析参数 → 提交创建任务 → 生成任务ID → 存储到数据库 → 跳转到分析页面
   ```

2. **任务执行阶段**：
   ```
   任务列表页面 → 选择待执行任务 → 点击开始按钮 → 更新任务状态为running
   → 调用LangGraph分析接口 → 启动多智能体工作流 → 并行执行分析任务
   → 实时更新进度状态 → 生成分析报告 → 制定交易决策 → 任务完成
   ```

## 用户界面

- **现代化设计**：基于 Tailwind CSS 的响应式设计
- **暗色主题**：完整的明暗主题切换支持
- **移动端适配**：完美的移动端和平板体验
- **无障碍访问**：符合 WCAG 2.1 标准
- **国际化**：完整的中文本地化支持
